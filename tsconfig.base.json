{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@angular/fire/firestore": ["node_modules/@angular/fire/firestore"], "@app/ha-api-proxy": ["apps/functions/src/index.ts"], "@rj/angular-ui-components": ["libs/rj-ui-components/src/index.ts"], "@rjui": ["libs/rj-ui-components/src/index.ts"], "@ui": ["libs/opc-component-library/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}