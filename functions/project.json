{"name": "functions", "$schema": "./node_modules/nx/schemas/project-schema.json", "sourceRoot": "functions/src", "projectType": "application", "tags": ["type:firebase", "scope:api", "platform:node"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "functions/dist", "main": "functions/src/index.ts", "tsConfig": "functions/tsconfig.lib.json", "assets": [{"glob": "*.md", "input": "functions", "output": "."}, {"glob": "package.json", "input": "functions", "output": "."}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true, "excludeFiles": ["**/*.spec.ts", "**/*.test.ts"]}}, "serve": {"executor": "nx:run-commands", "options": {"commands": [{"command": "cd functions && npm run serve"}]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["functions/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "functions/jest.config.ts"}}, "deploy": {"executor": "nx:run-commands", "dependsOn": ["build"], "options": {"commands": [{"command": "cd functions && npm run deploy"}]}}}}