# Firebase Functions

This directory contains Firebase Cloud Functions for the Home Assistant dashboard.

## Development

### Local Development

To run functions locally:

```bash
nx serve functions
```

### Testing

```bash
nx test functions
```

### Deployment

```bash
nx deploy functions
```

## Available Functions

- `helloWorld`: A simple HTTP function that returns a greeting
- `echo`: An HTTP function that echoes back the request data
- `corsProxy`: A proxy function to help with CORS issues when making API calls from the browser

### Using the CORS Proxy

The CORS proxy function allows your frontend to make API calls to services that don't support CORS or don't have your domain in their allowed origins.

#### Request format:

- **Endpoint**: `https://your-project-id.cloudfunctions.net/corsProxy`
- **Method**: Use any HTTP method (GET, POST, PUT, DELETE, etc.) - the proxy will use the same method for the target call
- **Query Parameters**:
  - `url` (required): The target URL to proxy the request to
  - `headers` (optional): JSON string of additional headers to send with the request

#### Example usage:

```javascript
// Making a proxied API call from your frontend
async function fetchData() {
  const targetUrl = 'https://api.example.com/data';
  const response = await fetch(`https://your-project-id.cloudfunctions.net/corsProxy?url=${encodeURIComponent(targetUrl)}`, {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer your-token',
      'Content-Type': 'application/json'
    }
  });
  
  return await response.json();
}
```

### Using with Home Assistant

The Home Assistant service in this application has been updated to use the CORS proxy for all API calls. This ensures that:

1. Direct API calls to Home Assistant are never made from the browser (preventing CORS issues)
2. The authentication token is never exposed in browser network requests to external URLs
3. All API calls are proxied through Firebase Functions, providing an additional security layer

The HomeAssistantService automatically:
- Constructs the proper URLs for the proxy
- Forwards authentication headers
- Handles error responses appropriately

If you need to make additional Home Assistant API calls from other services or components, use the `makeProxyRequest` method in the HomeAssistantService rather than making direct HTTP requests.

## Structure

- `src/index.ts`: Main entry point that initializes Firebase Admin SDK and exports functions
- `src/lib/`: Contains all individual function implementations 