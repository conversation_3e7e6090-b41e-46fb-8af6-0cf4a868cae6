import * as functionsTest from 'firebase-functions-test';
import * as admin from 'firebase-admin';
import axios from 'axios';

// Initialize the firebase-functions-test SDK
const testEnv = functionsTest();

// We need to mock Firebase Admin SDK
jest.mock('firebase-admin', () => ({
  initializeApp: jest.fn(),
  firestore: jest.fn(() => ({
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn(),
        set: jest.fn()
      }))
    }))
  }))
}));

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Import our functions after mocking Firebase
import { helloWorld, echo, corsProxy } from './functions';

describe('Firebase Cloud Functions', () => {
  // Clean up after tests
  afterAll(() => {
    testEnv.cleanup();
  });

  describe('helloWorld', () => {
    it('should return a successful response with a message', () => {
      // Create a mock request object
      const req = { method: 'GET' };

      // Create a fake response object with a json function we can spy on
      const res = {
        json: jest.fn()
      };

      // Call the function with our mock request and response
      helloWorld(req as any, res as any);

      // Assert that the json function was called with the expected data
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Hello from Firebase Functions!',
          timestamp: expect.any(String)
        })
      );
    });
  });

  describe('echo', () => {
    it('should echo back the request details', () => {
      // Create a mock request object
      const req = {
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: { test: 'data' },
        query: { param: 'value' },
        params: { id: '123' }
      };

      // Create a fake response object with a json function we can spy on
      const res = {
        json: jest.fn()
      };

      // Call the function with our mock request and response
      echo(req as any, res as any);

      // Assert that the json function was called with the expected data
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          headers: { 'content-type': 'application/json' },
          body: { test: 'data' },
          query: { param: 'value' },
          params: { id: '123' },
          timestamp: expect.any(String)
        })
      );
    });
  });

  describe('corsProxy', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return 400 if URL parameter is missing', async () => {
      // Create a mock request with no URL
      const req = {
        method: 'GET',
        query: {},
        headers: {}
      };

      // Create a mock response
      const res = {
        set: jest.fn(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        send: jest.fn()
      };

      // Call the function
      await corsProxy(req as any, res as any);

      // Check that the correct error was returned
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Missing required URL parameter'
        })
      );
    });

    it('should handle OPTIONS request for CORS preflight', async () => {
      // Create a mock OPTIONS request
      const req = {
        method: 'OPTIONS',
        query: { 'url': 'https://example.com/api' }
      };

      // Create a mock response
      const res = {
        set: jest.fn(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      // Call the function
      await corsProxy(req as any, res as any);

      // Check that the correct CORS headers were set
      expect(res.set).toHaveBeenCalledWith('Access-Control-Allow-Origin', '*');
      expect(res.set).toHaveBeenCalledWith('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      expect(res.status).toHaveBeenCalledWith(204);
      expect(res.send).toHaveBeenCalledWith('');
    });

    it('should proxy GET request and return the response', async () => {
      // Mock the axios response
      const mockResponse = {
        status: 200,
        headers: {
          'content-type': 'application/json'
        },
        data: Buffer.from(JSON.stringify({ success: true, data: 'test' }))
      };
      mockedAxios.mockResolvedValueOnce(mockResponse);

      // Create a mock request
      const req = {
        method: 'GET',
        query: { 'url': 'https://example.com/api' },
        headers: {
          'authorization': 'Bearer token123',
          'content-type': 'application/json'
        },
        body: null
      };

      // Create a mock response
      const res = {
        set: jest.fn(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        send: jest.fn()
      };

      // Call the function
      await corsProxy(req as any, res as any);

      // Check that axios was called correctly
      expect(mockedAxios).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'GET',
          url: 'https://example.com/api'
        })
      );

      // Check that the correct response was returned
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ success: true, data: 'test' });
    });

    it('should handle proxy errors gracefully', async () => {
      // Mock axios to throw an error
      mockedAxios.mockRejectedValueOnce(new Error('Network error'));

      // Create a mock request
      const req = {
        method: 'GET',
        query: { 'url': 'https://example.com/api' },
        headers: {}
      };

      // Create a mock response
      const res = {
        set: jest.fn(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // Call the function
      await corsProxy(req as any, res as any);

      // Check that the correct error response was returned
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Failed to proxy request',
          message: 'Network error'
        })
      );
    });
  });
}); 