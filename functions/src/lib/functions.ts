import * as functions from 'firebase-functions';
import axios from 'axios';

/**
 * Sample HTTP function that responds with a greeting
 */
export const helloWorld = functions.https.onRequest((request, response) => {
  functions.logger.info('Hello logs!', {structuredData: true});
  response.json({
    message: 'Hello from Firebase Functions!',
    timestamp: new Date().toISOString()
  });
});

/**
 * Echo function that returns the request body
 */
export const echo = functions.https.onRequest((request, response) => {
  const echo = {
    method: request.method,
    headers: request.headers,
    body: request.body || {},
    query: request.query || {},
    params: request.params || {},
    timestamp: new Date().toISOString()
  };
  
  functions.logger.info('Echo request', echo);
  response.json(echo);
});

/**
 * CORS Proxy function to make requests to external APIs from the UI
 * This helps avoid CORS issues when making direct API calls from the browser
 * 
 * Request format:
 * - URL: Required query parameter with the target URL
 * - Method: Uses the HTTP method of the incoming request (GET, POST, etc.)
 * - Headers: Headers from the request are forwarded (except those specifically excluded)
 * - Body: The body of the request is forwarded as-is
 */
export const corsProxy = functions.https.onRequest(async (request, response) => {
  try {
    // Set CORS headers to allow requests from your application
    response.set('Access-Control-Allow-Origin', '*');
    response.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    // Handle preflight OPTIONS request
    if (request.method === 'OPTIONS') {
      response.status(204).send('');
      return;
    }
    
    // Get the target URL from the query parameter
    const targetUrl = request.query['url'] as string;
    if (!targetUrl) {
      response.status(400).json({ error: 'Missing required URL parameter' });
      return;
    }
    
    functions.logger.info('Proxying request', { 
      targetUrl, 
      method: request.method, 
      headers: request.headers 
    });
    
    // Filter out headers we don't want to forward
    const headers: { [key: string]: string } = {};
    const excludedHeaders = [
      'host', 
      'connection', 
      'content-length', 
      'origin', 
      'referer',
      'accept-encoding',
      'x-forwarded-for',
      'x-forwarded-proto',
      'x-forwarded-host'
    ];
    
    // Copy allowed headers from the original request
    Object.keys(request.headers).forEach(key => {
      if (!excludedHeaders.includes(key.toLowerCase())) {
        headers[key] = request.headers[key] as string;
      }
    });
    
    // Add custom headers if provided in the query params
    const customHeaders = request.query['headers'];
    if (customHeaders && typeof customHeaders === 'string') {
      try {
        const parsedHeaders = JSON.parse(customHeaders);
        Object.assign(headers, parsedHeaders);
      } catch (error) {
        functions.logger.warn('Failed to parse custom headers', error);
      }
    }
    
    // Make the request to the target URL
    const axiosConfig = {
      method: request.method,
      url: targetUrl,
      headers,
      data: request.body,
      responseType: 'arraybuffer' as const, // To handle all response types
      validateStatus: () => true, // Return the response regardless of the status code
    };
    
    const axiosResponse = await axios(axiosConfig);
    
    // Get content type to properly handle the response
    const contentType = axiosResponse.headers['content-type'] || 'application/json';
    
    // Set response headers
    Object.keys(axiosResponse.headers).forEach(key => {
      // Skip setting CORS headers from the target response
      if (!key.toLowerCase().startsWith('access-control-')) {
        response.set(key, axiosResponse.headers[key]);
      }
    });
    
    // Set the status code from the proxied response
    response.status(axiosResponse.status);
    
    // Send the response body
    if (contentType.includes('application/json')) {
      try {
        // Try to parse JSON responses
        const jsonData = JSON.parse(axiosResponse.data.toString());
        response.json(jsonData);
      } catch (error) {
        // If parsing fails, send as raw data
        response.send(axiosResponse.data);
      }
    } else {
      // For non-JSON responses, send as is
      response.send(axiosResponse.data);
    }
    
  } catch (error) {
    functions.logger.error('Proxy error', error);
    response.status(500).json({
      error: 'Failed to proxy request',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}); 