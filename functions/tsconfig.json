{"extends": "./tsconfig.base.json", "compilerOptions": {"module": "commonjs", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noPropertyAccessFromIndexSignature": true, "target": "es2020", "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}]}