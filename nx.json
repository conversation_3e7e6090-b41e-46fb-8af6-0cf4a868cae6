{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)", "!{projectRoot}/.storybook/**/*", "!{projectRoot}/tsconfig.storybook.json"], "sharedGlobals": []}, "targetDefaults": {"@angular-devkit/build-angular:application": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/eslint:lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.js"]}, "@nx/jest:jest": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/angular:package": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "build-storybook": {"cache": true, "inputs": ["default", "^production", "{projectRoot}/.storybook/**/*", "{projectRoot}/tsconfig.storybook.json"]}, "e2e-ci--**/*": {"dependsOn": ["^build"]}}, "plugins": [{"plugin": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}], "generators": {"@nx/angular:application": {"e2eTestRunner": "playwright", "linter": "eslint", "style": "scss", "unitTestRunner": "jest"}, "@nx/angular:library": {"linter": "eslint", "unitTestRunner": "jest"}, "@nx/angular:component": {"style": "scss", "changeDetection": "OnPush", "displayBlock": true, "skipTests": false, "inlineStyle": false, "inlineTemplate": false, "standalone": true, "flat": false}}, "defaultBase": "main"}