{"name": "ha-dashboard", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/ha-dashboard/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/ha-dashboard", "index": "apps/ha-dashboard/src/index.html", "browser": "apps/ha-dashboard/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/ha-dashboard/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["apps/ha-dashboard/src/favicon.ico", "apps/ha-dashboard/src/assets", {"glob": "**/*", "input": "apps/ha-dashboard/src/public", "output": "/"}, {"glob": "floorplan.svg", "input": "apps/ha-dashboard/src/app/tiles/floorplan", "output": "/"}, {"glob": "**/*", "input": "apps/ha-dashboard/src/assets/images/rooms", "output": "/assets/images/rooms"}], "styles": ["apps/ha-dashboard/src/styles.scss", "node_modules/@fortawesome/fontawesome-free/css/all.min.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "18kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "ha-dashboard:build:production"}, "development": {"buildTarget": "ha-dashboard:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "ha-dashboard:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/ha-dashboard/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "ha-dashboard:build", "port": 4200, "staticFilePath": "dist/apps/ha-dashboard/browser", "spa": true}}, "e2e": {"executor": "@nx/playwright:playwright", "outputs": ["{workspaceRoot}/dist/.playwright/apps/ha-dashboard"], "options": {"config": "apps/ha-dashboard/playwright.config.ts"}, "configurations": {"production": {"webServerTarget": "ha-dashboard:serve:production"}, "ci": {"webServerTarget": "ha-dashboard:serve-static"}}}}}