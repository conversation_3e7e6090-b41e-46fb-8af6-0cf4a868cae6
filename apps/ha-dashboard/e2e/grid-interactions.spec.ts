import { test, expect } from './fixtures/ha-dashboard-fixture';
import { ensureDashboardIsEditable, lockDashboard, waitForAutoSave, dragTileUsingHandle } from './utils/test-utils';

test.describe('Dashboard Grid Interactions', () => {
  // Setup for all tests in this group - login before tests
  test.beforeEach(async ({ page, setupMockData, loginToHA }) => {
    await setupMockData();
    await loginToHA();
    
    // Navigate to the dashboard page
    await page.goto('/');
    
    // Wait for the dashboard to be visible and fully loaded
    await expect(page.locator('.dashboard-grid')).toBeVisible();
    
    // Ensure dashboard is in edit mode (unlocked)
    await ensureDashboardIsEditable(page);
  });

  test('Drag handles have correct styling and cursor properties', async ({ page }) => {
    // Get the first gridster item
    const tile = page.locator('gridster-item').first();
    await expect(tile).toBeVisible();
    
    // Check for specific drag handles or common drag areas
    const specificDragHandles = tile.locator('.gridster-item-handle, .drag-handle, .gridster-item-drag-handler');
    const hasSpecificHandles = await specificDragHandles.count() > 0;
    
    if (hasSpecificHandles) {
      console.log('Found specific drag handles');
      const dragHandle = specificDragHandles.first();
      
      // Verify the handle is visible
      await expect(dragHandle).toBeVisible();
      
      // Verify the handle has the correct cursor style
      const cursor = await dragHandle.evaluate(el => window.getComputedStyle(el).cursor);
      expect(['grab', 'move', 'pointer'].includes(cursor)).toBeTruthy();
      
      // Check for any specific styling that indicates it's a drag handle
      const backgroundColor = await dragHandle.evaluate(el => window.getComputedStyle(el).backgroundColor);
      const border = await dragHandle.evaluate(el => window.getComputedStyle(el).border);
      
      console.log(`Drag handle styling - cursor: ${cursor}, background: ${backgroundColor}, border: ${border}`);
    } else {
      // Check tile header as a common drag area
      const tileHeader = tile.locator('.tile-header, .item-content > div > :first-child');
      
      if (await tileHeader.isVisible()) {
        console.log('Using tile header as drag area');
        
        // Verify cursor style on hover
        await tileHeader.hover();
        const cursor = await tileHeader.evaluate(el => window.getComputedStyle(el).cursor);
        expect(['grab', 'move', 'pointer', 'default'].includes(cursor)).toBeTruthy();
        
        console.log(`Tile header cursor style: ${cursor}`);
      } else {
        // If no specific handles are found, the entire tile should be draggable
        console.log('No specific drag handles found - entire tile should be draggable');
        
        // Check cursor on the tile itself
        await tile.hover();
        const cursor = await tile.evaluate(el => window.getComputedStyle(el).cursor);
        expect(['grab', 'move', 'pointer', 'default'].includes(cursor)).toBeTruthy();
        
        console.log(`Tile cursor style: ${cursor}`);
      }
    }
    
    // Verify that dragging works using our utility
    const initialRect = await tile.boundingBox();
    expect(initialRect).not.toBeNull();
    
    const targetX = initialRect!.x + initialRect!.width / 2 + 100;
    const targetY = initialRect!.y + initialRect!.height / 2 + 50;
    
    const dragSuccessful = await dragTileUsingHandle(page, tile, targetX, targetY);
    expect(dragSuccessful).toBe(true);
  });

  test('Drag handles are visible and functional on tiles', async ({ page }) => {
    // Get the first gridster item
    const tile = page.locator('gridster-item').first();
    await expect(tile).toBeVisible();
    
    // Verify that the tile header is visible - typically this serves as the drag handle area
    const tileHeader = tile.locator('.tile-header, .item-content > div > :first-child');
    await expect(tileHeader).toBeVisible();
    
    // Check if there are specific drag handles - in some gridster implementations,
    // there might be specific drag handle elements like .gridster-item-drag-handle
    const specificDragHandles = tile.locator('.gridster-item-handle, .drag-handle');
    const hasSpecificHandles = await specificDragHandles.count() > 0;
    
    // Get initial position
    const initialRect = await tile.boundingBox();
    expect(initialRect).not.toBeNull();
    
    // Perform drag using the appropriate handle
    if (hasSpecificHandles) {
      // If there are specific drag handles, use the first one
      console.log('Using specific drag handle');
      await specificDragHandles.first().hover();
    } else {
      // Otherwise use the tile header as the drag area
      console.log('Using tile header as drag area');
      await tileHeader.hover();
    }
    
    // Begin the drag operation
    await page.mouse.down();
    
    // Move to a new position (100px to the right and 50px down)
    await page.mouse.move(
      initialRect!.x + initialRect!.width / 2 + 100,
      initialRect!.y + initialRect!.height / 2 + 50,
      { steps: 10 }
    );
    await page.mouse.up();
    
    // Let the animation complete
    await page.waitForTimeout(500);
    
    // Get new position
    const newRect = await tile.boundingBox();
    expect(newRect).not.toBeNull();
    
    // Verify tile has moved
    expect(newRect!.x).not.toEqual(initialRect!.x);
    expect(newRect!.y).not.toEqual(initialRect!.y);
    
    // Wait for autosave to complete
    await waitForAutoSave(page);
  });

  test('When dashboard is locked, drag handles are disabled', async ({ page }) => {
    // Get the first gridster item
    const tile = page.locator('gridster-item').first();
    await expect(tile).toBeVisible();
    
    // Check for specific drag handles or use tile header
    const tileHeader = tile.locator('.tile-header, .item-content > div > :first-child');
    await expect(tileHeader).toBeVisible();
    
    const specificDragHandles = tile.locator('.gridster-item-handle, .drag-handle');
    const hasSpecificHandles = await specificDragHandles.count() > 0;
    
    // Get initial position
    const initialRect = await tile.boundingBox();
    expect(initialRect).not.toBeNull();
    
    // Lock the dashboard
    await lockDashboard(page);
    
    // Try to drag from the handle
    if (hasSpecificHandles) {
      await specificDragHandles.first().hover();
    } else {
      await tileHeader.hover();
    }
    
    await page.mouse.down();
    await page.mouse.move(
      initialRect!.x + initialRect!.width / 2 + 100,
      initialRect!.y + initialRect!.height / 2 + 50,
      { steps: 10 }
    );
    await page.mouse.up();
    
    // Let any animation complete
    await page.waitForTimeout(500);
    
    // Get new position
    const newRect = await tile.boundingBox();
    expect(newRect).not.toBeNull();
    
    // Verify tile has NOT moved when locked
    expect(newRect!.x).toBeCloseTo(initialRect!.x, 0);
    expect(newRect!.y).toBeCloseTo(initialRect!.y, 0);
  });

  test('Can drag and drop a tile to a new position', async ({ page }) => {
    // Get the first gridster item
    const tile = page.locator('gridster-item').first();
    await expect(tile).toBeVisible();
    
    // Get initial position
    const initialRect = await tile.boundingBox();
    expect(initialRect).not.toBeNull();
    
    // Use our utility function to drag the tile using the appropriate handle
    const targetX = initialRect!.x + initialRect!.width / 2 + 100;
    const targetY = initialRect!.y + initialRect!.height / 2 + 50;
    
    const dragSuccessful = await dragTileUsingHandle(page, tile, targetX, targetY);
    expect(dragSuccessful).toBe(true);
    
    // Get new position
    const newRect = await tile.boundingBox();
    expect(newRect).not.toBeNull();
    
    // Verify tile has moved
    expect(newRect!.x).not.toEqual(initialRect!.x);
    expect(newRect!.y).not.toEqual(initialRect!.y);
    
    // Wait for autosave to complete
    await waitForAutoSave(page);
  });

  test('Can resize a tile', async ({ page }) => {
    // Get the first gridster item
    const tile = page.locator('gridster-item').first();
    await expect(tile).toBeVisible();
    
    // Get initial size
    const initialRect = await tile.boundingBox();
    expect(initialRect).not.toBeNull();
    
    // Find a resize handle (bottom-right corner)
    // In gridster2, resizable handles have class name like "gridster-item-resizable-handler handle-se"
    const resizeHandle = tile.locator('.gridster-item-resizable-handler.handle-se');
    await expect(resizeHandle).toBeVisible();
    
    // Perform resize operation
    await resizeHandle.hover();
    await page.mouse.down();
    
    // Resize by moving 50px down and 50px to the right
    await page.mouse.move(
      initialRect!.x + initialRect!.width + 50,
      initialRect!.y + initialRect!.height + 50,
      { steps: 10 }
    );
    await page.mouse.up();
    
    // Let the animation complete
    await page.waitForTimeout(500);
    
    // Get new size
    const newRect = await tile.boundingBox();
    expect(newRect).not.toBeNull();
    
    // Verify tile has been resized
    expect(newRect!.width).toBeGreaterThan(initialRect!.width);
    expect(newRect!.height).toBeGreaterThan(initialRect!.height);
    
    // Wait for autosave to complete
    await waitForAutoSave(page);
  });

  test('Can drag a tile to the trash can to delete it', async ({ page }) => {
    // Count initial number of tiles
    const initialTileCount = await page.locator('gridster-item').count();
    expect(initialTileCount).toBeGreaterThan(0);
    
    // Get the first gridster item to delete
    const tile = page.locator('gridster-item').first();
    await expect(tile).toBeVisible();
    
    // Start dragging the tile
    const tileRect = await tile.boundingBox();
    expect(tileRect).not.toBeNull();
    
    await tile.hover();
    await page.mouse.down();
    
    // Wait for trash can to appear
    await expect(page.locator('.trash-can-popup')).toBeVisible();
    
    // Get trash can position
    const trashCan = page.locator('.trash-can-popup');
    const trashCanRect = await trashCan.boundingBox();
    expect(trashCanRect).not.toBeNull();
    
    // Move to the center of the trash can
    await page.mouse.move(
      trashCanRect!.x + trashCanRect!.width / 2,
      trashCanRect!.y + trashCanRect!.height / 2,
      { steps: 10 }
    );
    
    // Drop the tile
    await page.mouse.up();
    
    // Wait for the operation to complete
    await page.waitForTimeout(500);
    
    // Verify the tile has been deleted
    const newTileCount = await page.locator('gridster-item').count();
    expect(newTileCount).toBe(initialTileCount - 1);
    
    // Wait for autosave to complete
    await waitForAutoSave(page);
  });

  test('Cannot resize tiles when dashboard is locked', async ({ page }) => {
    // Get the first gridster item
    const tile = page.locator('gridster-item').first();
    await expect(tile).toBeVisible();
    
    // Get initial size
    const initialRect = await tile.boundingBox();
    expect(initialRect).not.toBeNull();
    
    // Find a resize handle to verify it exists before locking
    const resizeHandle = tile.locator('.gridster-item-resizable-handler.handle-se');
    await expect(resizeHandle).toBeVisible();
    
    // Lock the dashboard
    await lockDashboard(page);
    
    // Check that resizing is disabled - in gridster2, the resize handlers are not removed
    // but the resize option is disabled, so we should check if we can resize in practice
    // We'll try to resize and verify the size hasn't changed
    
    // Try to resize anyway (this shouldn't work)
    await resizeHandle.hover();
    await page.mouse.down();
    
    // Try to resize by moving 50px down and 50px to the right
    await page.mouse.move(
      initialRect!.x + initialRect!.width + 50,
      initialRect!.y + initialRect!.height + 50,
      { steps: 10 }
    );
    await page.mouse.up();
    
    // Let the animation complete
    await page.waitForTimeout(500);
    
    // Get new size
    const newRect = await tile.boundingBox();
    expect(newRect).not.toBeNull();
    
    // Verify tile has NOT been resized
    expect(newRect!.width).toBeCloseTo(initialRect!.width, 1); // Allow small tolerance for browser rendering differences
    expect(newRect!.height).toBeCloseTo(initialRect!.height, 1);
  });
  
  test('Can add a new tile', async ({ page }) => {
    // Count initial number of tiles
    const initialTileCount = await page.locator('gridster-item').count();
    
    // Click the add tile button
    await page.locator('button[matTooltip="Add Tile"]').click();
    
    // Wait for the dialog to appear
    const dialog = page.locator('.mat-dialog-container');
    await expect(dialog).toBeVisible();
    
    // Select a tile type (e.g., entity-browser)
    // Note: This selector may need adjustment based on actual implementation
    const tileTypeOption = dialog.locator('button:has-text("Entity Browser")');
    if (await tileTypeOption.isVisible()) {
      await tileTypeOption.click();
    } else {
      // Alternative approach: try selecting from a dropdown or other UI element
      const typeSelector = dialog.locator('mat-select[formControlName="type"]');
      await typeSelector.click();
      await page.locator('mat-option:has-text("Entity Browser")').click();
    }
    
    // Enter a title for the new tile
    const titleInput = dialog.locator('input[formControlName="title"]');
    if (await titleInput.isVisible()) {
      await titleInput.fill('Test Entity Browser');
    }
    
    // Click the Add/Create button
    const addButton = dialog.locator('button[type="submit"], button:has-text("Add"), button:has-text("Create")');
    await addButton.click();
    
    // Wait for the dialog to close
    await expect(dialog).not.toBeVisible({ timeout: 5000 });
    
    // Wait for the new tile to appear
    await page.waitForTimeout(1000);
    
    // Verify a new tile was added
    const newTileCount = await page.locator('gridster-item').count();
    expect(newTileCount).toBe(initialTileCount + 1);
    
    // Verify the new tile has the correct title
    const newTileTitle = page.locator('gridster-item:has-text("Test Entity Browser")');
    await expect(newTileTitle).toBeVisible();
    
    // Wait for autosave to complete
    await waitForAutoSave(page);
  });
}); 