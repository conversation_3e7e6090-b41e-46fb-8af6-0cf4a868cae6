import { test as base, expect, Page } from '@playwright/test';
import * as dotenv from 'dotenv';
import path from 'path';

// Load environment variables from .env file if it exists
dotenv.config({ path: path.join(__dirname, '../.env') });

// Define custom fixture type
type HADashboardFixture = {
  loginToHA: () => Promise<void>;
  setupMockData: () => Promise<void>;
  page: Page;
};

// Extend the base test with custom fixtures
export const test = base.extend<HADashboardFixture>({
  // Define the loginToHA fixture
  loginToHA: async ({ page }, use) => {
    const login = async (): Promise<void> => {
      // Navigate to login page
      await page.goto('/login');
      
      // Get test credentials from environment variables
      const haUrl = process.env.TEST_HA_URL || 'http://homeassistant.local:8123';
      
      // Fill in the login form
      await page.fill('input[placeholder*="Home Assistant URL"]', haUrl);
      
      // If there are additional fields, fill those too
      // For example, if token is needed
      if (await page.locator('input[type="password"]').isVisible()) {
        const token = process.env.TEST_HA_TOKEN || '';
        await page.fill('input[type="password"]', token);
      }
      
      // Submit the form
      await page.click('button[type="submit"]');
      
      // Wait for navigation to complete - adjust timeout as needed
      await page.waitForURL('**/dashboard', { timeout: 10000 }).catch(() => {
        console.warn('Navigation after login may have timed out');
      });
    };
    
    await use(login);
  },
  
  // Setup mock data for testing
  setupMockData: async ({ page }, use) => {
    const setup = async (): Promise<void> => {
      // This would typically set up any mock data your app needs
      // For example, you might use browser localStorage or mock API responses
      
      // Example: Set mock data in localStorage
      await page.evaluate(() => {
        localStorage.setItem('ha-config', JSON.stringify({
          haUrl: 'http://mock-ha-server.local:8123',
          authenticated: true
        }));
      });
    };
    
    await use(setup);
  }
});

export { expect }; 