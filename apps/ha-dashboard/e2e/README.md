# Home Assistant Dashboard E2E Tests

This directory contains end-to-end tests for the Home Assistant Dashboard application using Playwright.

## Test Structure

- `authenticated.spec.ts`: Tests for authenticated user flows, including dashboard navigation
- `grid-interactions.spec.ts`: Tests for dashboard grid functionality, including drag & drop and resizing of tiles

### Grid Interaction Tests

The `grid-interactions.spec.ts` tests validate the Gridster dashboard functionality:

- Drag handle detection and functionality
- Drag and drop operations
- Resize operations
- Tile deletion via drag to trash
- Locking/unlocking behavior
- Adding new tiles

## Running Tests

To run all e2e tests:

```bash
nx e2e ha-dashboard-e2e
```

To run a specific test file:

```bash
nx e2e ha-dashboard-e2e --testFile=grid-interactions.spec.ts
```

## Debugging Tests

You can run tests in debug mode to see the browser:

```bash
nx e2e ha-dashboard-e2e --headed
```

Or with slower execution:

```bash
nx e2e ha-dashboard-e2e --headed --debug
```

## Test Fixtures

The tests use custom fixtures defined in `fixtures/ha-dashboard-fixture.ts` to handle:

- Authentication
- Mock data setup
- Common page operations

## Test Utilities

The `utils/test-utils.ts` file provides reusable functions for:

- Managing dashboard editing mode (locking/unlocking)
- Detecting and using appropriate drag handles for tile movement
- Adding new tiles
- Waiting for autosave operations
- Manually saving dashboard layouts

## Test Environment

The tests require environment variables for authentication:

- `E2E_USERNAME`: Username for Home Assistant
- `E2E_PASSWORD`: Password for Home Assistant

These can be set in a `.env.local` file in the root directory or passed directly when running tests.

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up environment variables (optional but recommended for auth testing):
   ```bash
   cp .env.example .env
   ```
   
   Then edit the `.env` file with your test environment values.

## Running Tests

### Run all tests

```bash
npx nx e2e ha-dashboard
```

### Run tests in UI mode for debugging

```bash
npx nx e2e ha-dashboard --ui
```

### Run specific test file

```bash
npx nx e2e ha-dashboard --test-files=example.spec.ts
```

### Run tests with specific browser

```bash
npx nx e2e ha-dashboard --browser=firefox
```

Available browsers: `chromium`, `firefox`, `webkit`

### Run in headless mode

```bash
npx nx e2e ha-dashboard --headed
```

### Run tests in CI mode

```bash
npx nx e2e ha-dashboard --configuration=ci
```

## Debugging

- Use `page.pause()` in your test to pause execution and open the Playwright inspector
- Run tests with the `--debug` flag to run in debug mode
- Use the `--ui` flag to run tests with the Playwright Test UI

## Useful Playwright Commands

- Generate a screenshot: `await page.screenshot({ path: 'screenshot.png' });`
- Record test interactions: `npx playwright codegen http://localhost:4200`
- View HTML structure: `await page.locator('selector').evaluate(node => node.outerHTML)`

## Viewing Test Reports

After running tests, you can view the HTML report with:

```bash
npx playwright show-report
```

## Recording Test Traces

You can record traces for all tests by adding the trace flag:

```bash
npx nx e2e ha-dashboard --trace=on
```

Options for trace are:
- `on`: Record traces for all tests
- `off`: Do not record traces
- `retain-on-failure`: Record traces only for failed tests (default)

## CI Integration

The Playwright tests are configured to run in CI environments. The configuration in `playwright.config.ts` automatically detects CI environments and adjusts settings accordingly. 