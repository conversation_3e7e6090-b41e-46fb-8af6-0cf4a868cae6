import { Page, expect } from '@playwright/test';

/**
 * Ensures the dashboard is in editing mode (unlocked)
 * @param page The Playwright page object
 */
export async function ensureDashboardIsEditable(page: Page): Promise<void> {
  // Check if lock icon is present and shows "lock" (which means dashboard is locked)
  const lockButton = page.locator('button[matTooltip*="Lock Dashboard"], button[matTooltip*="Unlock Dashboard"]');
  
  if (await lockButton.isVisible()) {
    const lockIconText = await page.locator('button[matTooltip*="Lock Dashboard"] mat-icon, button[matTooltip*="Unlock Dashboard"] mat-icon').textContent();
    
    // If dashboard is locked (icon shows 'lock'), unlock it
    if (lockIconText?.trim() === 'lock') {
      await lockButton.click();
      
      // Wait for the dashboard to become editable (icon shows 'lock_open')
      await expect(page.locator('button[matTooltip*="Lock Dashboard"] mat-icon, button[matTooltip*="Unlock Dashboard"] mat-icon')).toHaveText('lock_open');
    }
  }
}

/**
 * Locks the dashboard to disable editing
 * @param page The Playwright page object
 */
export async function lockDashboard(page: Page): Promise<void> {
  // Find the lock/unlock button
  const lockButton = page.locator('button[matTooltip*="Lock Dashboard"]');
  
  // Click it to lock the dashboard
  if (await lockButton.isVisible()) {
    await lockButton.click();
    
    // Wait for the lock icon to change to 'lock'
    await expect(page.locator('button[matTooltip*="Unlock Dashboard"] mat-icon')).toHaveText('lock');
  }
}

/**
 * Drag a gridster tile using the appropriate drag handle
 * @param page The Playwright page object 
 * @param tile The tile element to drag
 * @param targetX The target X coordinate
 * @param targetY The target Y coordinate
 * @param steps Number of steps for the drag motion (higher = smoother)
 * @returns Boolean indicating whether the drag was successful
 */
export async function dragTileUsingHandle(page: Page, tile: any, targetX: number, targetY: number, steps = 10): Promise<boolean> {
  // Get initial position
  const initialRect = await tile.boundingBox();
  if (!initialRect) return false;
  
  // First check for specific drag handles that might be configured
  const specificDragHandles = tile.locator('.gridster-item-handle, .drag-handle, .gridster-item-drag-handler');
  const hasSpecificHandles = await specificDragHandles.count() > 0;
  
  // If specific handles exist, use them
  if (hasSpecificHandles) {
    console.log('Using specific drag handle for tile');
    await specificDragHandles.first().hover();
  } else {
    // Otherwise, look for a tile header which is commonly used as a drag area
    const tileHeader = tile.locator('.tile-header, .item-content > div > :first-child');
    
    if (await tileHeader.isVisible()) {
      console.log('Using tile header as drag handle');
      await tileHeader.hover();
    } else {
      // If no header, use the tile itself
      console.log('Using tile itself as drag area');
      await tile.hover();
    }
  }
  
  // Perform the drag operation
  await page.mouse.down();
  await page.mouse.move(targetX, targetY, { steps });
  await page.mouse.up();
  
  // Wait for animation to complete
  await page.waitForTimeout(500);
  
  // Check if the tile actually moved
  const newRect = await tile.boundingBox();
  return newRect && (newRect.x !== initialRect.x || newRect.y !== initialRect.y);
}

/**
 * Adds a new tile to the dashboard
 * @param page The Playwright page object
 * @param tileType The type of tile to add
 * @param title Optional title for the tile
 */
export async function addNewTile(page: Page, tileType: string, title?: string): Promise<void> {
  // Click the add tile button
  await page.locator('button[matTooltip="Add Tile"]').click();
  
  // Wait for the dialog to appear
  const dialog = page.locator('.mat-dialog-container');
  await expect(dialog).toBeVisible();
  
  // Select the tile type (assuming it's a list of options)
  await dialog.locator(`button[aria-label="Add ${tileType}"]`).click();
  
  // If title is provided, update the title
  if (title) {
    await dialog.locator('input[formControlName="title"]').fill(title);
  }
  
  // Click the Add button to create the tile
  await dialog.locator('button[type="submit"]').click();
  
  // Wait for the dialog to close and tile to be added
  await expect(dialog).not.toBeVisible();
}

/**
 * Waits for auto-save to complete
 * @param page The Playwright page object
 */
export async function waitForAutoSave(page: Page): Promise<void> {
  // Check if autosave indicator appears and wait for it to disappear
  const autosaveIndicator = page.locator('.autosave-indicator');
  
  if (await autosaveIndicator.isVisible()) {
    await expect(autosaveIndicator).not.toBeVisible({ timeout: 5000 });
  } else {
    // If no indicator, just wait a reasonable time for background save
    await page.waitForTimeout(3500);
  }
}

/**
 * Manually save the dashboard layout
 * @param page The Playwright page object
 */
export async function saveDashboardLayout(page: Page): Promise<void> {
  // Open the menu
  await page.locator('app-hamburger-menu button').click();
  
  // Wait for menu to open
  const menu = page.locator('.hamburger-menu-panel');
  await expect(menu).toBeVisible();
  
  // Click save layout option
  await menu.locator('button:has-text("Save Layout")').click();
  
  // Wait for the success notification if any
  const notification = page.locator('.notification-container');
  if (await notification.isVisible()) {
    await expect(notification).not.toBeVisible({ timeout: 5000 });
  }
}

/**
 * Get all current dashboard tiles
 * @param page The Playwright page object
 * @returns Promise resolving to an array of tile elements
 */
export async function getAllTiles(page: Page) {
  return page.locator('gridster-item').all();
} 