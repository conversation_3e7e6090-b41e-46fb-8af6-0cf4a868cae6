import { test, expect } from './fixtures/ha-dashboard-fixture';

test.describe('Authenticated Home Assistant Dashboard Tests', () => {
  // Setup for all tests in this group - login before tests
  test.beforeEach(async ({ page, setupMockData }) => {
    await setupMockData();
  });

  test('Dashboard shows entity cards when authenticated', async ({ page }) => {
    // Go to dashboard after setting up mock data
    await page.goto('/dashboard');
    
    // Check if dashboard contains expected elements
    // These selectors should be adjusted based on your actual application
    await expect(page.locator('.dashboard-container')).toBeVisible();
  });

  test('Can navigate to device test page when authenticated', async ({ page }) => {
    await page.goto('/');
    
    // Find and click on device test navigation link
    // Replace with actual navigation selector
    await page.getByRole('link', { name: /device test/i }).click();
    
    // Check we're on the device test page
    await expect(page.url()).toContain('/device-test');
    await expect(page.locator('h1')).toContainText('Home Assistant API Test');
  });

  test('Can fetch device data when authenticated', async ({ page }) => {
    await page.goto('/device-test');
    
    // Click the fetch data button
    await page.getByRole('button', { name: /fetch all data/i }).click();
    
    // Check for loading state first
    await expect(page.locator('.loading-container')).toBeVisible();
    
    // Wait for loading to complete (may need to be adjusted based on your app)
    await expect(page.locator('.loading-container')).toBeHidden({ timeout: 10000 });
    
    // Now check for presence of data (this is a loose check since we're using mock data)
    await expect(page.locator('mat-tab-group')).toBeVisible();
  });
});

// Test with actual login if credentials are available
test.describe('Actual Authentication Test', () => {
  test('Can login with credentials from .env', async ({ page, loginToHA }) => {
    // Skip test if no credentials
    test.skip(!process.env.TEST_HA_URL, 'No test credentials provided in .env file');
    
    // Perform login
    await loginToHA();
    
    // Check that we're logged in
    await expect(page.url()).toContain('/dashboard');
  });
}); 