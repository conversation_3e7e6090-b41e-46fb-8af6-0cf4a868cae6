import { test, expect } from '@playwright/test';

test('User can login with test credentials', async ({ page }) => {
  // Navigate to the login page
  await page.goto('/login');
  
  // Verify the page content
  await expect(page.getByRole('heading', { name: 'Login' })).toBeVisible();
  
  // Click on the "Use Email Login" button to switch to email login
  await page.getByRole('button', { name: 'Use Email Login' }).click();
  
  // Fill in the test credentials
  await page.getByLabel('Email').fill('<EMAIL>');
  await page.getByLabel('Password').fill('testtest');
  
  // Click the login button
  await page.getByRole('button', { name: 'Login' }).click();
  
  // Wait for navigation to complete
  await page.waitForURL('/dashboard');
  
  // Verify that we're on the dashboard page
  await expect(page.getByRole('heading', { name: '<PERSON> Devi<PERSON>' })).toBeVisible();
  
  // Check if entities are displayed
  await expect(page.locator('.entity-card')).toBeVisible();
});

test('Invalid login shows error message', async ({ page }) => {
  // Navigate to the login page
  await page.goto('/login');
  
  // Click on the "Use Email Login" button
  await page.getByRole('button', { name: 'Use Email Login' }).click();
  
  // Fill in invalid credentials
  await page.getByLabel('Email').fill('<EMAIL>');
  await page.getByLabel('Password').fill('wrongpassword');
  
  // Click the login button
  await page.getByRole('button', { name: 'Login' }).click();
  
  // Check for error message
  await expect(page.locator('.error-message')).toBeVisible();
  await expect(page.locator('.error-message')).toContainText('Invalid email or password');
});

test('User can navigate to device test page', async ({ page }) => {
  // Login first
  await page.goto('/login');
  await page.getByRole('button', { name: 'Use Email Login' }).click();
  await page.getByLabel('Email').fill('<EMAIL>');
  await page.getByLabel('Password').fill('testtest');
  await page.getByRole('button', { name: 'Login' }).click();
  
  // Wait for navigation to dashboard
  await page.waitForURL('/dashboard');
  
  // Open user menu
  await page.getByRole('button', { name: '' }).click();
  
  // Click on Device Test menu item
  await page.getByRole('menuitem', { name: 'Device Test' }).click();
  
  // Verify we're on the device test page
  await page.waitForURL('/device-test');
  await expect(page.locator('h1')).toContainText('Home Assistant API Test');
  
  // Check if the Fetch All Data button is visible
  await expect(page.getByRole('button', { name: 'Fetch All Data' })).toBeVisible();
}); 