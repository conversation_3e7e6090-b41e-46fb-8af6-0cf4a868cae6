# Example environment variables for E2E tests
# Copy this file to .env and modify as needed

# Base URL for tests
BASE_URL=http://localhost:4200

# Test Home Assistant instance details
TEST_HA_URL=http://homeassistant.local:8123
TEST_HA_TOKEN=your_long_lived_access_token_here

# Test user credentials (if needed)
TEST_USERNAME=test_user
TEST_PASSWORD=test_password

# Browser configuration
# Set to 'true' to run browsers in headful mode
DEBUG=false 