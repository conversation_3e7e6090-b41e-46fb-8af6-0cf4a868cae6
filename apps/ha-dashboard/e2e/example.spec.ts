import { test, expect } from '@playwright/test';

test('Home Assistant Dashboard - Page loads with header', async ({ page }) => {
  await page.goto('/');

  // Expect to find the Home Assistant header
  expect(await page.locator('h1').first().innerText()).toContain('Home Assistant');
});

test('Home Assistant Dashboard - Login form is present', async ({ page }) => {
  await page.goto('/login');

  // Check login page elements
  await expect(page.getByRole('heading')).toContainText('Login');
  await expect(page.getByLabel('Home Assistant URL')).toBeVisible();
  
  // Additional testing for login form
  await expect(page.getByRole('button', { name: /Login|Connect/i })).toBeVisible();
});

test('Home Assistant Dashboard - Navigation works', async ({ page }) => {
  // Start at the home page
  await page.goto('/');
  
  // Navigate to different pages (if available in the app without auth)
  // These will need to be modified based on the actual structure of your app
  
  // For example, checking if navigation elements exist
  const navigation = page.locator('nav').first();
  await expect(navigation).toBeVisible();
});

// Device test page is accessible
test('Home Assistant Dashboard - Device Test page', async ({ page }) => {
  await page.goto('/device-test');
  
  // Check for device test page elements
  await expect(page.locator('h1')).toContainText('Home Assistant API Test');
  
  // Check for fetch data button
  await expect(page.getByRole('button', { name: /Fetch All Data/i })).toBeVisible();
  
  // Expect empty state when no data is fetched
  await expect(page.getByText('No data available')).toBeVisible();
});
