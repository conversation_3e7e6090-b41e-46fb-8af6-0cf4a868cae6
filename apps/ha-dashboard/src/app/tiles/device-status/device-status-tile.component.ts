import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HomeAssistantService, HAEntity } from '../../services/home-assistant.service';
import { Subscription, interval } from 'rxjs';
import { startWith, switchMap, catchError } from 'rxjs/operators';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DashboardItem } from '../../models/dashboard.models';

@Component({
  selector: 'app-device-status-tile',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="device-status-container">
      <div class="device-header">
        <h3>{{ title || 'Device Status' }}</h3>
        <div class="device-state" *ngIf="entity">
          <span class="state-indicator" [class.active]="isActive"></span>
          <span class="state-text">{{ getStateText() }}</span>
        </div>
      </div>
      
      <div class="device-content">
        <div *ngIf="loading" class="loading-state">
          <mat-spinner diameter="40"></mat-spinner>
          <span>Loading device status...</span>
        </div>
        
        <div *ngIf="error" class="error-state">
          <mat-icon>error</mat-icon>
          <span>{{ error }}</span>
        </div>
        
        <div *ngIf="!loading && !error && !entity" class="empty-state">
          <mat-icon>device_unknown</mat-icon>
          <p>No device data available</p>
          <p class="entity-id" *ngIf="entityId">Entity: {{ entityId }}</p>
        </div>
        
        <div *ngIf="!loading && entity" class="device-details">
          <!-- Device type specific UI -->
          <ng-container [ngSwitch]="deviceType">
            <!-- Light device UI -->
            <div *ngSwitchCase="'light'" class="light-controls">
              <div class="light-status">
                <div class="light-indicator" [style.background-color]="getLightColor()"></div>
                <div class="light-info">
                  <div class="light-name">{{ entity.attributes['friendly_name'] || entity.entity_id }}</div>
                  <div class="light-state">{{ entity.state }}</div>
                </div>
              </div>
              
              <div class="light-actions">
                <button mat-raised-button color="primary" (click)="toggleDevice()">
                  {{ isActive ? 'TURN OFF' : 'TURN ON' }}
                </button>
              </div>
            </div>
            
            <!-- Switch device UI -->
            <div *ngSwitchCase="'switch'" class="switch-controls">
              <div class="switch-status">
                <div class="switch-indicator" [class.active]="isActive"></div>
                <div class="switch-info">
                  <div class="switch-name">{{ entity.attributes['friendly_name'] || entity.entity_id }}</div>
                  <div class="switch-state">{{ entity.state }}</div>
                </div>
              </div>
              
              <div class="switch-actions">
                <button mat-raised-button color="primary" (click)="toggleDevice()">
                  {{ isActive ? 'TURN OFF' : 'TURN ON' }}
                </button>
              </div>
            </div>
            
            <!-- Sensor device UI -->
            <div *ngSwitchCase="'sensor'" class="sensor-display">
              <div class="sensor-value">{{ getSensorValue() }}</div>
              <div class="sensor-unit">{{ getSensorUnit() }}</div>
              <div class="sensor-name">{{ entity.attributes['friendly_name'] || entity.entity_id }}</div>
              <div class="sensor-updated">Last updated: {{ entity.last_updated | date:'medium' }}</div>
            </div>
            
            <!-- Camera device UI -->
            <div *ngSwitchCase="'camera'" class="camera-display">
              <div class="camera-stream">
                <img *ngIf="entity.attributes['entity_picture']" 
                     [src]="getCameraImageUrl()" 
                     alt="Camera feed"
                     (error)="handleImageError()">
                <div *ngIf="!entity.attributes['entity_picture'] || cameraError" class="camera-error">
                  <mat-icon>videocam_off</mat-icon>
                  <span>Camera feed not available</span>
                </div>
              </div>
              <div class="camera-name">{{ entity.attributes['friendly_name'] || entity.entity_id }}</div>
            </div>
            
            <!-- Default device UI for other types -->
            <div *ngSwitchDefault class="generic-display">
              <div class="entity-icon">
                <mat-icon>{{ getEntityIcon() }}</mat-icon>
              </div>
              <div class="entity-info">
                <div class="entity-name">{{ entity.attributes['friendly_name'] || entity.entity_id }}</div>
                <div class="entity-state">
                  {{ entity.state }}
                  <span *ngIf="entity.attributes['unit_of_measurement']">
                    {{ entity.attributes['unit_of_measurement'] }}
                  </span>
                </div>
                <div class="entity-updated">Last updated: {{ entity.last_updated | date:'short' }}</div>
              </div>
              
              <div class="entity-actions" *ngIf="canToggle()">
                <button mat-raised-button color="primary" (click)="toggleDevice()">
                  TOGGLE
                </button>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./device-status-tile.component.scss']
})
export class DeviceStatusTileComponent implements OnInit, OnDestroy {
  @Input() entityId?: string;
  @Input() deviceType: 'light' | 'sensor' | 'switch' | 'climate' | 'media_player' | 'camera' = 'light';
  @Input() title?: string;
  @Input() isEditingEnabled = false;
  
  // Backward compatibility
  @Input() set item(val: DashboardItem) {
    if (val && val.settings) {
      this.entityId = val.settings['entityId'] as string;
      this.deviceType = (val.settings['deviceType'] as any) || 'light';
      this.title = val.title;
    }
  }
  
  entity: HAEntity | null = null;
  loading = true;
  error: string | null = null;
  refreshInterval = 30000; // 30 seconds refresh by default
  isActive = false;
  cameraError = false;
  
  private subscription = new Subscription();
  
  constructor(private haService: HomeAssistantService) {}
  
  ngOnInit(): void {
    this.loadEntity();
    
    // Set up refresh interval
    const refreshSub = interval(this.refreshInterval).pipe(
      startWith(0),
      switchMap(() => {
        // If we don't have an entity ID, don't try to load
        if (!this.entityId) {
          return [];
        }
        
        return this.haService.getEntity(this.entityId).pipe(
          catchError(err => {
            console.error('Error loading entity:', err);
            this.error = 'Failed to load entity data';
            return [];
          })
        );
      })
    ).subscribe(entity => {
      if (entity) {
        this.entity = entity;
        this.error = null;
        this.updateDeviceState();
      }
      this.loading = false;
    });
    
    this.subscription.add(refreshSub);
  }
  
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  
  loadEntity(): void {
    this.loading = true;
    this.error = null;
    
    if (!this.entityId) {
      this.loading = false;
      this.error = 'No entity ID specified';
      return;
    }
    
    const entitySub = this.haService.getEntity(this.entityId).subscribe({
      next: (entity) => {
        this.entity = entity;
        this.updateDeviceState();
        this.loading = false;
      },
      error: (err) => {
        console.error('Error fetching entity:', err);
        this.loading = false;
        this.error = 'Failed to load entity: ' + this.entityId;
      }
    });
    
    this.subscription.add(entitySub);
  }
  
  toggleDevice(): void {
    if (!this.entityId) return;
    
    this.loading = true;
    
    if (this.deviceType === 'light' || this.deviceType === 'switch') {
      const action = this.isActive ? 'turnOff' : 'turnOn';
      
      const toggleSub = this.haService[action](this.entityId).subscribe({
        next: () => {
          // After toggling, reload the entity to get the updated state
          this.loadEntity();
        },
        error: (err) => {
          console.error(`Error toggling device (${action}):`, err);
          this.loading = false;
          this.error = 'Failed to toggle device';
        }
      });
      
      this.subscription.add(toggleSub);
    } else {
      // For other device types, use the generic toggle
      const toggleSub = this.haService.toggleEntity(this.entityId).subscribe({
        next: () => {
          // After toggling, reload the entity to get the updated state
          this.loadEntity();
        },
        error: (err) => {
          console.error('Error toggling entity:', err);
          this.loading = false;
          this.error = 'Failed to toggle entity';
        }
      });
      
      this.subscription.add(toggleSub);
    }
  }
  
  updateDeviceState(): void {
    if (!this.entity) return;
    
    // Determine active state based on device type
    switch (this.deviceType) {
      case 'light':
      case 'switch':
        this.isActive = this.entity.state === 'on';
        break;
      case 'climate':
        this.isActive = this.entity.state !== 'off';
        break;
      case 'media_player':
        this.isActive = this.entity.state === 'playing' || this.entity.state === 'on';
        break;
      default:
        this.isActive = this.entity.state !== 'off' && this.entity.state !== 'unavailable' && this.entity.state !== 'unknown';
    }
  }
  
  getStateText(): string {
    if (!this.entity) return 'Unknown';
    
    switch (this.deviceType) {
      case 'light':
      case 'switch':
        return this.isActive ? 'On' : 'Off';
      case 'sensor':
        return `${this.entity.state} ${this.getSensorUnit()}`;
      default:
        return this.entity.state;
    }
  }
  
  getSensorValue(): string {
    if (!this.entity) return '';
    return this.entity.state;
  }
  
  getSensorUnit(): string {
    if (!this.entity || !this.entity.attributes['unit_of_measurement']) return '';
    return this.entity.attributes['unit_of_measurement'] as string;
  }
  
  getCameraImageUrl(): string {
    if (!this.entity || !this.entity.attributes['entity_picture']) return '';
    
    // If the URL is relative, prefix with HA instance URL
    const picture = this.entity.attributes['entity_picture'] as string;
    if (picture.startsWith('/')) {
      // TODO: Get the HA URL from the service instead of hardcoding
      return `/api/camera_proxy/${this.entityId}`;
    }
    
    return picture;
  }
  
  handleImageError(): void {
    this.cameraError = true;
  }
  
  getLightColor(): string {
    if (!this.entity || !this.isActive) return '#888888';
    
    // Check if the light has RGB color
    const rgb = this.entity.attributes['rgb_color'];
    if (rgb && Array.isArray(rgb) && rgb.length >= 3) {
      return `rgb(${rgb[0]}, ${rgb[1]}, ${rgb[2]})`;
    }
    
    // Otherwise return a default color
    return '#ffcc88';
  }
  
  getEntityIcon(): string {
    if (!this.entity) return 'device_unknown';
    
    // Get domain of entity
    const domain = this.entityId?.split('.')[0] || '';
    
    switch (domain) {
      case 'light': return 'lightbulb';
      case 'switch': return 'toggle_on';
      case 'sensor': return 'sensors';
      case 'binary_sensor': return 'fiber_manual_record';
      case 'climate': return 'thermostat';
      case 'camera': return 'videocam';
      case 'media_player': return 'play_circle';
      case 'cover': return 'blinds';
      case 'fan': return 'mode_fan';
      default: return 'device_hub';
    }
  }
  
  canToggle(): boolean {
    if (!this.entity || !this.entityId) return false;
    
    const domain = this.entityId.split('.')[0];
    const toggleableDomains = ['light', 'switch', 'input_boolean', 'automation', 'fan'];
    
    return toggleableDomains.includes(domain);
  }
} 