.device-status-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: var(--background-secondary, #ffffff);
  border-radius: 8px;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.device-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color, rgba(0, 0, 0, 0.87));
}

.device-state {
  display: flex;
  align-items: center;
  gap: 8px;
}

.state-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.38);
}

.state-indicator.active {
  background-color: #4caf50;
}

.state-text {
  font-size: 14px;
  color: var(--text-color-secondary, rgba(0, 0, 0, 0.6));
}

.device-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
  color: var(--text-color-secondary, rgba(0, 0, 0, 0.6));
  text-align: center;
}

.error-state {
  color: var(--error-color, #f44336);
}

.entity-id {
  font-family: monospace;
  font-size: 12px;
  margin-top: 8px;
}

.device-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Light specific styles */
.light-controls, .switch-controls {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
}

.light-status, .switch-status {
  display: flex;
  align-items: center;
  gap: 16px;
}

.light-indicator, .switch-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #888888;
}

.switch-indicator {
  background-color: rgba(0, 0, 0, 0.12);
}

.switch-indicator.active {
  background-color: #4caf50;
}

.light-info, .switch-info {
  flex: 1;
}

.light-name, .switch-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.light-state, .switch-state {
  font-size: 14px;
  color: var(--text-color-secondary, rgba(0, 0, 0, 0.6));
}

.light-actions, .switch-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: auto;
}

/* Sensor specific styles */
.sensor-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.sensor-value {
  font-size: 48px;
  font-weight: 300;
  margin-bottom: 8px;
}

.sensor-unit {
  font-size: 16px;
  color: var(--text-color-secondary, rgba(0, 0, 0, 0.6));
  margin-bottom: 16px;
}

.sensor-name {
  font-weight: 500;
  margin-bottom: 8px;
}

.sensor-updated {
  font-size: 12px;
  color: var(--text-color-secondary, rgba(0, 0, 0, 0.6));
}

/* Camera specific styles */
.camera-display {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.camera-stream {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #000;
  border-radius: 4px;
  margin-bottom: 16px;
}

.camera-stream img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.camera-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 100%;
  color: rgba(255, 255, 255, 0.7);
}

.camera-name {
  text-align: center;
  font-weight: 500;
}

/* Generic display */
.generic-display {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;
}

.entity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  width: 48px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  margin: 0 auto;
}

.entity-icon mat-icon {
  font-size: 28px;
  height: 28px;
  width: 28px;
}

.entity-info {
  text-align: center;
  flex: 1;
}

.entity-name {
  font-weight: 500;
  margin-bottom: 8px;
}

.entity-state {
  font-size: 16px;
  margin-bottom: 8px;
}

.entity-updated {
  font-size: 12px;
  color: var(--text-color-secondary, rgba(0, 0, 0, 0.6));
}

.entity-actions {
  display: flex;
  justify-content: center;
  margin-top: auto;
} 