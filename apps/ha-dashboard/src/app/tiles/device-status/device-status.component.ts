import { Component, OnInit, OnD<PERSON>roy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HaDeviceIntegrationService } from '../../services/ha-device-integration.service';
import { HAEntity, HAServiceCallResult } from '../../services/home-assistant.service';
import { BehaviorSubject, Subscription, interval } from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatListModule } from '@angular/material/list';

@Component({
  selector: 'app-device-status',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatDividerModule,
    MatProgressBarModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatListModule
  ],
  template: `
    <mat-card class="device-status-container">
      <mat-card-header>
        <mat-card-title>{{ title || 'Device Status' }}</mat-card-title>
        <button 
          *ngIf="refreshable" 
          mat-icon-button 
          class="refresh-button" 
          matTooltip="Refresh data"
          (click)="refreshData()">
          <mat-icon [class.rotate]="isLoading">refresh</mat-icon>
        </button>
      </mat-card-header>
      
      <mat-card-content class="device-content" [class.loading]="isLoading">
        <div *ngIf="isLoading" class="loading-indicator">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
        
        <ng-container *ngIf="!isLoading && entities.length === 0">
          <div class="empty-state">
            <mat-icon>device_unknown</mat-icon>
            <p>No {{ deviceType }} devices found</p>
          </div>
        </ng-container>
        
        <!-- Light devices -->
        <ng-container *ngIf="deviceType === 'light' && !isLoading">
          <div *ngFor="let entity of entities" class="device-item light-item" 
               [class.active]="entity.state === 'on'"
               (click)="toggleLight(entity)">
            <mat-icon color="{{entity.state === 'on' ? 'primary' : ''}}">
              lightbulb
            </mat-icon>
            <div class="device-info">
              <span class="device-name">{{ getName(entity) }}</span>
              <span class="device-state">{{ formatState(entity) }}</span>
              <mat-progress-bar 
                *ngIf="entity.state === 'on' && entity.attributes['brightness']" 
                mode="determinate" 
                [value]="(entity.attributes['brightness'] / 255) * 100"
                color="primary"
                class="brightness-bar">
              </mat-progress-bar>
            </div>
          </div>
        </ng-container>
        
        <!-- Sensor devices -->
        <ng-container *ngIf="deviceType === 'sensor' && !isLoading">
          <div *ngFor="let entity of entities" class="device-item sensor-item">
            <mat-icon>{{ getSensorIconName(entity) }}</mat-icon>
            <div class="device-info">
              <span class="device-name">{{ getName(entity) }}</span>
              <span class="device-state">{{ formatState(entity) }}</span>
            </div>
          </div>
        </ng-container>
        
        <!-- Switch devices -->
        <ng-container *ngIf="deviceType === 'switch' && !isLoading">
          <mat-list>
            <mat-list-item *ngFor="let entity of entities" class="device-item switch-item">
              <mat-icon matListItemIcon [color]="entity.state === 'on' ? 'primary' : ''">
                power_settings_new
              </mat-icon>
              <div matListItemTitle class="device-name">{{ getName(entity) }}</div>
              <div matListItemLine class="device-state">{{ formatState(entity) }}</div>
              <mat-slide-toggle 
                [checked]="entity.state === 'on'" 
                (change)="toggleSwitch(entity)" 
                color="primary">
              </mat-slide-toggle>
            </mat-list-item>
          </mat-list>
        </ng-container>
        
        <!-- Climate devices -->
        <ng-container *ngIf="deviceType === 'climate' && !isLoading">
          <div *ngFor="let entity of entities" class="device-item climate-item"
               [class.active]="entity.state !== 'off'">
            <mat-icon [color]="entity.state !== 'off' ? 'primary' : ''">
              {{ getClimateIconName(entity) }}
            </mat-icon>
            <div class="device-info">
              <span class="device-name">{{ getName(entity) }}</span>
              <span class="device-state">{{ formatClimateState(entity) }}</span>
              <div *ngIf="entity.attributes['temperature']" class="temperature">
                {{ entity.attributes['temperature'] }}°
              </div>
            </div>
          </div>
        </ng-container>
        
        <!-- Media player devices -->
        <ng-container *ngIf="deviceType === 'media_player' && !isLoading">
          <div *ngFor="let entity of entities" class="device-item media-item"
               [class.active]="entity.state === 'playing'">
            <mat-icon [color]="entity.state === 'playing' ? 'primary' : ''">music_note</mat-icon>
            <div class="device-info">
              <span class="device-name">{{ getName(entity) }}</span>
              <span class="device-state">{{ formatState(entity) }}</span>
              <div *ngIf="entity.attributes['media_title']" class="media-title">
                {{ entity.attributes['media_title'] }}
              </div>
            </div>
            <div class="media-controls" *ngIf="entity.state !== 'off'">
              <button mat-mini-fab color="primary" (click)="playPauseMedia(entity, $event)">
                <mat-icon>{{ entity.state === 'playing' ? 'pause' : 'play_arrow' }}</mat-icon>
              </button>
              <button mat-mini-fab (click)="stopMedia(entity, $event)">
                <mat-icon>stop</mat-icon>
              </button>
            </div>
          </div>
        </ng-container>
        
        <!-- Camera devices -->
        <ng-container *ngIf="deviceType === 'camera' && !isLoading">
          <div *ngFor="let entity of entities" class="device-item camera-item">
            <mat-icon>videocam</mat-icon>
            <div class="device-info">
              <span class="device-name">{{ getName(entity) }}</span>
              <span class="device-state">{{ formatState(entity) }}</span>
            </div>
            <button mat-mini-fab color="primary" 
                   (click)="fetchCameraSnapshot(entity.entity_id)"
                   matTooltip="Refresh snapshot">
              <mat-icon>refresh</mat-icon>
            </button>
          </div>
          
          <div *ngFor="let entity of entities" class="camera-preview">
            <img *ngIf="getCameraImageUrl(entity)" 
                 [src]="getCameraImageUrl(entity)" 
                 [alt]="getName(entity)"
                 class="camera-image mat-elevation-z2">
            <div *ngIf="!getCameraImageUrl(entity)" class="no-preview">
              <mat-icon>no_photography</mat-icon>
              <p>No preview available</p>
            </div>
          </div>
        </ng-container>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .device-status-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      overflow: hidden;
      padding: 0;
    }
    
    mat-card-header {
      background-color: var(--background-color);
      border-bottom: 1px solid var(--border-color);
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    mat-card-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: var(--text-color);
    }
    
    .refresh-button {
      color: var(--text-secondary);
    }
    
    .rotate {
      animation: spin 1s linear infinite;
    }
    
    .device-content {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
      color: var(--text-color);
      position: relative;
    }
    
    .loading-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
    }
    
    .device-content.loading {
      opacity: 0.6;
    }
    
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: var(--text-secondary);
      padding: 24px;
      text-align: center;
    }
    
    .empty-state mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
    }
    
    .device-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 16px;
      background-color: var(--background-color);
      border: 1px solid var(--border-color);
      transition: all 0.2s ease;
    }
    
    .device-item mat-icon {
      margin-right: 16px;
      font-size: 24px;
      height: 24px;
      width: 24px;
    }
    
    .device-item.active {
      box-shadow: 0 2px 8px var(--box-shadow);
    }
    
    .device-info {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .device-name {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .device-state {
      font-size: 13px;
      color: var(--text-secondary);
    }
    
    /* Light controls */
    .light-item {
      cursor: pointer;
    }
    
    .brightness-bar {
      margin-top: 12px;
    }
    
    /* Switch controls */
    .switch-item {
      cursor: default;
    }
    
    /* Climate controls */
    .temperature {
      font-size: 16px;
      font-weight: bold;
      color: var(--primary-color);
      margin-top: 6px;
    }
    
    /* Media player controls */
    .media-title {
      margin-top: 4px;
      font-style: italic;
      font-size: 12px;
    }
    
    .media-controls {
      display: flex;
      gap: 8px;
    }
    
    /* Camera styles */
    .camera-item {
      margin-bottom: 8px;
    }
    
    .camera-preview {
      margin-top: 8px;
      margin-bottom: 24px;
      display: flex;
      justify-content: center;
    }
    
    .camera-image {
      max-width: 100%;
      border-radius: 8px;
      border: 1px solid var(--border-color);
    }
    
    .no-preview {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      width: 100%;
      background-color: var(--background-color);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      color: var(--text-secondary);
    }
    
    .no-preview mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
    }
    
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `]
})
export class DeviceStatusComponent implements OnInit, OnDestroy {
  @Input() deviceType: 'light' | 'sensor' | 'switch' | 'climate' | 'media_player' | 'camera' = 'light';
  @Input() title?: string;
  @Input() area?: string;
  @Input() deviceClass?: string;
  @Input() refreshable = true;
  @Input() refreshInterval = 30000; // 30 seconds

  entities: HAEntity[] = [];
  isLoading = true;
  private refresh$ = new BehaviorSubject<void>(undefined);
  private subscriptions = new Subscription();

  constructor(private deviceService: HaDeviceIntegrationService) { }

  ngOnInit(): void {
    // Initial load
    this.loadData();

    // Set up auto-refresh if enabled
    if (this.refreshInterval > 0) {
      const intervalSub = interval(this.refreshInterval).subscribe(() => {
        if (this.refreshable) {
          this.refreshData();
        }
      });
      this.subscriptions.add(intervalSub);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  refreshData(): void {
    this.refresh$.next();
  }

  private loadData(): void {
    // Set up refresh subscription
    const refreshSub = this.refresh$.pipe(
      tap(() => this.isLoading = true),
      switchMap(() => {
        // Select the appropriate method based on deviceType
        switch (this.deviceType) {
          case 'light':
            return this.deviceService.getAllLights();
          case 'sensor':
            return this.deviceClass 
              ? this.deviceService.getSensorsByDeviceClass(this.deviceClass)
              : this.deviceService.getAllSensors();
          case 'switch':
            return this.deviceService.getAllSwitches();
          case 'climate':
            return this.deviceService.getAllClimateDevices();
          case 'media_player':
            return this.deviceService.getAllMediaPlayers();
          case 'camera':
            return this.deviceService.getAllCameras();
          default:
            return this.deviceService.getAllLights();
        }
      }),
      tap(entities => {
        // Filter by area if specified
        if (this.area) {
          this.entities = entities.filter(entity => 
            entity.attributes['friendly_name']?.toLowerCase().includes(this.area!.toLowerCase())
          );
        } else {
          this.entities = entities;
        }
        this.isLoading = false;
      })
    ).subscribe();

    this.subscriptions.add(refreshSub);
  }

  // Get a friendly name for display
  getName(entity: HAEntity): string {
    if (!entity) return 'Unknown';
    
    const friendlyName = entity.attributes['friendly_name'];
    if (friendlyName) {
      // Remove the area name if it's the first word and we're already filtered by area
      if (this.area && friendlyName.toLowerCase().startsWith(this.area.toLowerCase())) {
        const parts = friendlyName.split(' ');
        return parts.slice(1).join(' ');
      }
      return friendlyName;
    }
    
    // Fallback to entity ID if no friendly name
    const parts = entity.entity_id.split('.');
    const id = parts.length > 1 ? parts[1] : parts[0];
    return id.replace(/_/g, ' ');
  }

  // Format the state for display
  formatState(entity: HAEntity): string {
    if (!entity) return 'Unknown';
    
    const state = entity.state;
    const unit = entity.attributes['unit_of_measurement'];
    
    // For switches, lights
    if (['switch', 'light'].includes(this.deviceType)) {
      return state === 'on' ? 'On' : 'Off';
    }
    
    // For sensors
    if (this.deviceType === 'sensor') {
      // Binary sensors
      if (entity.entity_id.startsWith('binary_sensor.')) {
        return state === 'on' ? 'Active' : 'Inactive';
      }
      
      // Regular sensors
      return `${state}${unit ? ' ' + unit : ''}`;
    }
    
    // For media players
    if (this.deviceType === 'media_player') {
      switch (state) {
        case 'playing':
          return 'Playing';
        case 'paused':
          return 'Paused';
        case 'idle':
          return 'Idle';
        case 'off':
          return 'Off';
        default:
          return state;
      }
    }
    
    return state;
  }

  // Format the climate state for display
  formatClimateState(entity: HAEntity): string {
    if (!entity) return 'Unknown';
    
    const state = entity.state;
    const mode = entity.attributes['hvac_mode'];
    const action = entity.attributes['hvac_action'];
    
    if (state === 'off') {
      return 'Off';
    }
    
    if (action) {
      switch (action) {
        case 'heating':
          return 'Heating';
        case 'cooling':
          return 'Cooling';
        case 'idle':
          return `${mode} (Idle)`;
        default:
          return action;
      }
    }
    
    return mode || state;
  }

  // Get the appropriate sensor icon based on type
  getSensorIconName(entity: HAEntity): string {
    if (!entity) return 'sensors';
    
    // Check device class
    const deviceClass = entity.attributes['device_class'];
    
    // Binary sensors
    if (entity.entity_id.startsWith('binary_sensor.')) {
      switch (deviceClass) {
        case 'motion':
          return entity.state === 'on' ? 'directions_run' : 'accessibility_new';
        case 'door':
          return entity.state === 'on' ? 'door_front' : 'door_back';
        case 'window':
          return entity.state === 'on' ? 'window' : 'window_closed';
        case 'presence':
          return entity.state === 'on' ? 'person' : 'person_off';
        case 'battery':
          return entity.state === 'on' ? 'battery_full' : 'battery_alert';
        default:
          return entity.state === 'on' ? 'toggle_on' : 'toggle_off';
      }
    }
    
    // Regular sensors
    switch (deviceClass) {
      case 'temperature':
        return 'thermostat';
      case 'humidity':
        return 'water_drop';
      case 'pressure':
        return 'speed';
      case 'illuminance':
        return 'light_mode';
      case 'battery':
        return 'battery_std';
      case 'power':
        return 'bolt';
      case 'energy':
        return 'electric_meter';
      default:
        return 'sensors';
    }
  }

  // Get climate icon
  getClimateIconName(entity: HAEntity): string {
    if (!entity) return 'thermostat';
    
    const mode = entity.attributes['hvac_mode'];
    const action = entity.attributes['hvac_action'];
    
    if (action === 'heating') {
      return 'whatshot';
    }
    
    if (action === 'cooling') {
      return 'ac_unit';
    }
    
    if (mode === 'heat') {
      return 'local_fire_department';
    }
    
    if (mode === 'cool') {
      return 'ac_unit';
    }
    
    if (mode === 'auto') {
      return 'thermostat';
    }
    
    if (mode === 'off') {
      return 'power_settings_new';
    }
    
    return 'home';
  }

  // Toggle light state
  toggleLight(entity: HAEntity, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }
    
    if (entity.state === 'on') {
      this.deviceService.turnOffLight(entity.entity_id).subscribe({
        next: this.handleServiceResult,
        error: err => console.error('Error turning off light:', err)
      });
    } else {
      this.deviceService.turnOnLight(entity.entity_id).subscribe({
        next: this.handleServiceResult,
        error: err => console.error('Error turning on light:', err)
      });
    }
  }

  // Toggle switch state
  toggleSwitch(entity: HAEntity, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }
    
    if (entity.state === 'on') {
      this.deviceService.turnOffSwitch(entity.entity_id).subscribe({
        next: this.handleServiceResult,
        error: err => console.error('Error turning off switch:', err)
      });
    } else {
      this.deviceService.turnOnSwitch(entity.entity_id).subscribe({
        next: this.handleServiceResult,
        error: err => console.error('Error turning on switch:', err)
      });
    }
  }

  // Play/pause media
  playPauseMedia(entity: HAEntity, event: MouseEvent): void {
    event.stopPropagation();
    
    if (entity.state === 'playing') {
      this.deviceService.pauseMedia(entity.entity_id).subscribe({
        next: this.handleServiceResult,
        error: err => console.error('Error pausing media:', err)
      });
    } else {
      this.deviceService.playMedia(entity.entity_id).subscribe({
        next: this.handleServiceResult,
        error: err => console.error('Error playing media:', err)
      });
    }
  }

  // Stop media
  stopMedia(entity: HAEntity, event: MouseEvent): void {
    event.stopPropagation();
    
    this.deviceService.stopMedia(entity.entity_id).subscribe({
      next: this.handleServiceResult,
      error: err => console.error('Error stopping media:', err)
    });
  }

  // Generic handler for service responses
  private handleServiceResult = (result: HAServiceCallResult): void => {
    if (result.success) {
      // Refresh data after successful service call
      setTimeout(() => this.refreshData(), 500);
    } else {
      console.error('Service call failed:', result.error);
    }
  }

  // Handle camera functionality
  fetchCameraSnapshot(entityId: string): void {
    // We need to refresh the entity to get the updated snapshot URL
    this.refreshData();
    
    // Inform the user that we're refreshing
    console.log(`Requesting updated snapshot for camera: ${entityId}`);
  }

  // Generate a camera snapshot URL
  getCameraImageUrl(entity: HAEntity): string | null {
    if (!entity || !entity.entity_id) {
      return null;
    }

    // Either use the entity_picture attribute or generate one via the service
    if (entity.attributes && entity.attributes['entity_picture']) {
      return entity.attributes['entity_picture'] as string;
    }
    
    return this.deviceService.getCameraSnapshotUrl(entity.entity_id);
  }
} 