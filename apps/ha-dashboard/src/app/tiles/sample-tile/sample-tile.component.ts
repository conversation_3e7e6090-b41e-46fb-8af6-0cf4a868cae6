import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';

import { BaseTileComponent } from '../base-tile.component';
import { DashboardItem } from '../../models/dashboard.models';

@Component({
  selector: 'app-sample-tile',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    BaseTileComponent
  ],
  template: `
    <app-base-tile 
      [item]="item" 
      [isEditingEnabled]="isEditingEnabled"
      [isLocked]="isLocked"
      (delete)="onDeleteTile($event)"
      (lockChange)="onLockChange($event)">
      
      <!-- Custom tile actions that will appear in the header -->
      <div tileActions>
        <button mat-icon-button matTooltip="Refresh" (click)="refreshData()">
          <mat-icon>refresh</mat-icon>
        </button>
        <button mat-icon-button matTooltip="Settings" (click)="openSettings()">
          <mat-icon>settings</mat-icon>
        </button>
      </div>
      
      <!-- Tile content -->
      <div class="sample-tile-content">
        <h3>Sample Tile Content</h3>
        <p>This is a sample tile that demonstrates how to use the base tile component.</p>
        
        <div class="tile-data">
          <div class="data-point">
            <div class="data-label">Temperature</div>
            <div class="data-value">72°F</div>
          </div>
          
          <div class="data-point">
            <div class="data-label">Humidity</div>
            <div class="data-value">45%</div>
          </div>
          
          <div class="data-point">
            <div class="data-label">Status</div>
            <div class="data-value">
              <span class="status-indicator"></span>
              Active
            </div>
          </div>
        </div>
        
        <div class="tile-actions">
          <button mat-raised-button color="primary">Primary Action</button>
          <button mat-button color="accent">Secondary Action</button>
        </div>
      </div>
    </app-base-tile>
  `,
  styles: [`
    .sample-tile-content {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 500;
    }
    
    p {
      margin-bottom: 24px;
      color: var(--text-secondary);
    }
    
    .tile-data {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 24px;
    }
    
    .data-point {
      background-color: var(--background-color);
      padding: 12px;
      border-radius: 8px;
      min-width: 120px;
      flex: 1;
    }
    
    .data-label {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }
    
    .data-value {
      font-size: 18px;
      font-weight: 500;
      display: flex;
      align-items: center;
    }
    
    .status-indicator {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: var(--success-color);
      margin-right: 8px;
    }
    
    .tile-actions {
      margin-top: auto;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  `]
})
export class SampleTileComponent implements OnInit {
  @Input() item!: DashboardItem;
  @Input() isEditingEnabled = false;
  @Input() isLocked = false;
  
  @Output() lockChange = new EventEmitter<{item: DashboardItem, locked: boolean}>();
  
  ngOnInit(): void {
    console.log('Sample tile initialized with item:', this.item);
  }
  
  onDeleteTile(item: DashboardItem): void {
    console.log('Delete tile requested for item:', item);
    // In a real implementation, you would emit an event to the parent component
    // or use a service to handle the deletion
  }
  
  onLockChange(event: {item: DashboardItem, locked: boolean}): void {
    console.log('Lock state changed:', event);
    this.isLocked = event.locked;
    // Forward the lock change event to the parent component
    this.lockChange.emit(event);
  }
  
  refreshData(): void {
    console.log('Refreshing data...');
    // In a real implementation, you would reload data from a service
  }
  
  openSettings(): void {
    console.log('Opening settings...');
    // In a real implementation, you would open a settings dialog
  }
} 