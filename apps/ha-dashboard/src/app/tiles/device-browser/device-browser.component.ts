import { Component, OnInit, OnD<PERSON>roy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule, Sort } from '@angular/material/sort';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { MatListModule } from '@angular/material/list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { HomeAssistantService, HAEntity } from '../../services/home-assistant.service';
import { HaDeviceIntegrationService } from '../../services/ha-device-integration.service';
import { BehaviorSubject, Subscription, Observable, combineLatest } from 'rxjs';
import { map, switchMap, tap, startWith } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';

// Device interface based on Home Assistant API
export interface HADevice {
  id: string;
  name?: string;
  name_by_user?: string;
  model?: string;
  manufacturer?: string;
  identifiers?: string[];
  sw_version?: string;
  hw_version?: string;
  via_device_id?: string;
  area_id?: string;
  configuration_url?: string;
  disabled_by?: string;
  entry_type?: string;
  connections?: [string, string][];
}

@Component({
  selector: 'app-device-browser',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatDividerModule,
    MatExpansionModule,
    MatChipsModule,
    MatListModule,
    MatProgressSpinnerModule
  ],
  template: `
    <mat-card class="device-browser-container">
      <mat-card-header>
        <mat-card-title>{{ title || 'Device Browser' }}</mat-card-title>
        <div class="header-actions">
          <span class="device-count" matTooltip="Total devices">
            {{ devices.length }} devices
          </span>
          <button mat-icon-button (click)="refreshDevices()" matTooltip="Refresh devices">
            <mat-icon [class.rotating]="isLoading">refresh</mat-icon>
          </button>
        </div>
      </mat-card-header>
      
      <mat-card-content>
        <!-- Loading indicator -->
        <div *ngIf="isLoading" class="loading-overlay">
          <mat-spinner diameter="50"></mat-spinner>
        </div>
        
        <!-- Filters -->
        <div class="filter-container">
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Search devices</mat-label>
            <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilters()">
            <button *ngIf="searchTerm" matSuffix mat-icon-button (click)="clearSearch()">
              <mat-icon>close</mat-icon>
            </button>
            <mat-icon matPrefix>search</mat-icon>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="area-filter" *ngIf="areas.length > 0">
            <mat-label>Filter by area</mat-label>
            <mat-select [(ngModel)]="selectedArea" (selectionChange)="applyFilters()">
              <mat-option value="">All areas</mat-option>
              <mat-option *ngFor="let area of areas" [value]="area.id">
                {{ area.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="manufacturer-filter" *ngIf="manufacturers.length > 0">
            <mat-label>Filter by manufacturer</mat-label>
            <mat-select [(ngModel)]="selectedManufacturer" (selectionChange)="applyFilters()">
              <mat-option value="">All manufacturers</mat-option>
              <mat-option *ngFor="let manufacturer of manufacturers" [value]="manufacturer">
                {{ manufacturer }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        
        <!-- No devices message -->
        <div *ngIf="devices.length === 0 && !isLoading" class="no-devices">
          <mat-icon>devices</mat-icon>
          <p>No devices found</p>
        </div>
        
        <!-- No results message -->
        <div *ngIf="filteredDevices.length === 0 && devices.length > 0 && !isLoading" class="no-results">
          <mat-icon>search_off</mat-icon>
          <p>No devices found matching your filters.</p>
          <button mat-button color="primary" (click)="resetFilters()">Reset filters</button>
        </div>
        
        <!-- Device list -->
        <mat-list *ngIf="filteredDevices.length > 0 && !isLoading" class="device-list">
          <div *ngFor="let device of paginatedDevices; let last = last">
            <mat-list-item class="device-item" (click)="viewDeviceDetails(device)">
              <mat-icon matListItemIcon [color]="isDeviceActive(device) ? 'primary' : ''">
                {{ getDeviceIcon(device) }}
              </mat-icon>
              <div matListItemTitle class="device-name">
                {{ device.name_by_user || device.name || 'Unknown Device' }}
              </div>
              <div matListItemLine class="device-meta">
                <span *ngIf="device.manufacturer" class="manufacturer">{{ device.manufacturer }}</span>
                <span *ngIf="device.model" class="model">{{ device.model }}</span>
                <span *ngIf="getAreaName(device.area_id)" class="area">
                  <mat-icon class="small-icon">location_on</mat-icon> {{ getAreaName(device.area_id) }}
                </span>
              </div>
              
              <div matListItemMeta>
                <button mat-icon-button matTooltip="View details">
                  <mat-icon>chevron_right</mat-icon>
                </button>
              </div>
            </mat-list-item>
            
            <!-- Device entities preview -->
            <div class="device-entities-preview" *ngIf="getEntitiesForDevice(device.id).length > 0">
              <mat-chip-set>
                <mat-chip *ngFor="let entity of getEntitiesForDevice(device.id).slice(0, 3)"
                          [matTooltip]="entity.entity_id"
                          [color]="entity.state === 'on' ? 'primary' : undefined">
                  {{ formatEntityName(entity) }}
                </mat-chip>
                <mat-chip *ngIf="getEntitiesForDevice(device.id).length > 3">
                  +{{ getEntitiesForDevice(device.id).length - 3 }} more
                </mat-chip>
              </mat-chip-set>
            </div>
            
            <mat-divider *ngIf="!last"></mat-divider>
          </div>
        </mat-list>
        
        <!-- Pagination -->
        <mat-paginator 
          *ngIf="filteredDevices.length > itemsPerPage && !isLoading"
          [length]="filteredDevices.length"
          [pageSize]="itemsPerPage"
          [pageSizeOptions]="[5, 10, 25, 50]"
          (page)="onPageChange($event)">
        </mat-paginator>
        
        <!-- Device detail expansion panel -->
        <mat-expansion-panel 
          *ngIf="selectedDevice"
          [expanded]="detailsExpanded"
          (closed)="closeDetails()"
          class="device-details-panel">
          <mat-expansion-panel-header>
            <mat-panel-title>
              <mat-icon>{{ getDeviceIcon(selectedDevice) }}</mat-icon>
              {{ selectedDevice.name_by_user || selectedDevice.name || 'Device Details' }}
            </mat-panel-title>
            <mat-panel-description *ngIf="selectedDevice.manufacturer">
              {{ selectedDevice.manufacturer }} {{ selectedDevice.model ? '- ' + selectedDevice.model : '' }}
            </mat-panel-description>
          </mat-expansion-panel-header>
          
          <div class="device-details-content">
            <!-- Basic information -->
            <div class="details-section">
              <h3>Basic Information</h3>
              <div class="detail-row" *ngIf="selectedDevice.id">
                <span class="detail-label">ID:</span>
                <span class="detail-value">{{ selectedDevice.id }}</span>
              </div>
              <div class="detail-row" *ngIf="getAreaName(selectedDevice.area_id)">
                <span class="detail-label">Area:</span>
                <span class="detail-value">{{ getAreaName(selectedDevice.area_id) }}</span>
              </div>
              <div class="detail-row" *ngIf="selectedDevice.manufacturer">
                <span class="detail-label">Manufacturer:</span>
                <span class="detail-value">{{ selectedDevice.manufacturer }}</span>
              </div>
              <div class="detail-row" *ngIf="selectedDevice.model">
                <span class="detail-label">Model:</span>
                <span class="detail-value">{{ selectedDevice.model }}</span>
              </div>
              <div class="detail-row" *ngIf="selectedDevice.sw_version">
                <span class="detail-label">Firmware:</span>
                <span class="detail-value">{{ selectedDevice.sw_version }}</span>
              </div>
              <div class="detail-row" *ngIf="selectedDevice.hw_version">
                <span class="detail-label">Hardware:</span>
                <span class="detail-value">{{ selectedDevice.hw_version }}</span>
              </div>
            </div>
            
            <!-- Entities list -->
            <div class="details-section" *ngIf="getEntitiesForDevice(selectedDevice.id).length > 0">
              <h3>Entities ({{ getEntitiesForDevice(selectedDevice.id).length }})</h3>
              <mat-list dense>
                <mat-list-item *ngFor="let entity of getEntitiesForDevice(selectedDevice.id)" class="entity-item">
                  <mat-icon matListItemIcon>{{ getDomainIcon(entity) }}</mat-icon>
                  <div matListItemTitle>{{ formatEntityName(entity) }}</div>
                  <div matListItemLine>{{ entity.entity_id }}</div>
                  <div matListItemMeta class="entity-state" [ngClass]="getStateClass(entity)">
                    {{ entity.state }}
                    <span *ngIf="entity.attributes['unit_of_measurement']">
                      {{ entity.attributes['unit_of_measurement'] }}
                    </span>
                  </div>
                </mat-list-item>
              </mat-list>
            </div>
            
            <!-- Actions -->
            <div class="details-actions">
              <button mat-button color="primary" *ngIf="selectedDevice.configuration_url" 
                      (click)="openConfigUrl(selectedDevice)">
                <mat-icon>settings</mat-icon>
                Configuration
              </button>
              <button mat-button color="primary" (click)="closeDetails()">
                <mat-icon>close</mat-icon>
                Close
              </button>
            </div>
          </div>
        </mat-expansion-panel>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .device-browser-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      margin-left: auto;
      gap: 8px;
    }
    
    .device-count {
      font-size: 14px;
      color: var(--mat-text-secondary-color);
    }
    
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.7);
      z-index: 10;
    }
    
    .filter-container {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }
    
    .search-field {
      flex: 2;
      min-width: 200px;
    }
    
    .area-filter, .manufacturer-filter {
      flex: 1;
      min-width: 150px;
    }
    
    .no-devices, .no-results {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px 24px;
      text-align: center;
    }
    
    .no-devices mat-icon, .no-results mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
      color: var(--mat-text-hint-color);
    }
    
    .device-list {
      margin-bottom: 16px;
    }
    
    .device-item {
      cursor: pointer;
      transition: background-color 0.2s;
      height: auto !important;
      padding: 8px 0;
    }
    
    .device-item:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
    
    .device-meta {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }
    
    .device-entities-preview {
      padding-left: 56px;
      margin-bottom: 8px;
    }
    
    .small-icon {
      font-size: 16px;
      height: 16px;
      width: 16px;
      vertical-align: middle;
    }
    
    .device-details-panel {
      margin-top: 16px;
    }
    
    .device-details-content {
      display: flex;
      flex-direction: column;
      gap: 24px;
      padding: 16px 0;
    }
    
    .details-section {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .details-section h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
    
    .detail-row {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 4px;
    }
    
    .detail-label {
      font-weight: 500;
      min-width: 100px;
      color: var(--mat-text-secondary-color);
    }
    
    .entity-state {
      font-weight: 500;
    }
    
    .state-on {
      color: var(--mat-primary-color);
    }
    
    .state-off {
      color: var(--mat-text-hint-color);
    }
    
    .details-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 16px;
    }
    
    .rotating {
      animation: spin 1s infinite linear;
    }
    
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
    
    @media (max-width: 600px) {
      .filter-container {
        flex-direction: column;
      }
      
      .search-field, .area-filter, .manufacturer-filter {
        width: 100%;
      }
      
      .device-meta {
        flex-direction: column;
        gap: 4px;
      }
    }
  `]
})
export class DeviceBrowserComponent implements OnInit, OnDestroy {
  @Input() title?: string;
  @Input() area?: string;
  @Input() refreshInterval = 30000; // refresh every 30 seconds by default
  
  // Data
  devices: HADevice[] = [];
  filteredDevices: HADevice[] = [];
  paginatedDevices: HADevice[] = [];
  entities: HAEntity[] = [];
  areas: { id: string, name: string }[] = [];
  manufacturers: string[] = [];
  
  // UI state
  isLoading = true;
  searchTerm = '';
  selectedArea = '';
  selectedManufacturer = '';
  selectedDevice: HADevice | null = null;
  detailsExpanded = false;
  
  // Pagination
  currentPage = 0;
  itemsPerPage = 10;
  
  // Refresh control
  private refresh$ = new BehaviorSubject<void>(undefined);
  private subscriptions = new Subscription();
  
  constructor(
    private haService: HomeAssistantService,
    private deviceService: HaDeviceIntegrationService,
    private http: HttpClient
  ) {}
  
  ngOnInit(): void {
    // Initialize from provided area if any
    if (this.area) {
      this.selectedArea = this.area;
    }
    
    // Load data on component initialization and when refresh is triggered
    this.subscriptions.add(
      this.refresh$.pipe(
        switchMap(() => this.loadData())
      ).subscribe()
    );
    
    // Set up periodic refresh
    if (this.refreshInterval > 0) {
      const refreshTimer = setInterval(() => {
        this.refreshDevices();
      }, this.refreshInterval);
      
      this.subscriptions.add({
        unsubscribe: () => clearInterval(refreshTimer)
      });
    }
  }
  
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  
  private loadData(): Observable<any> {
    this.isLoading = true;
    
    // Fetch entities to correlate with devices
    const entities$ = this.haService.entityStates$;
    
    // Fetch areas
    const areas$ = this.fetchAreas();
    
    // Fetch devices
    const devices$ = this.fetchDevices();
    
    // Combine all data
    return combineLatest([entities$, areas$, devices$]).pipe(
      tap(([entities, areas, devices]) => {
        this.entities = entities;
        this.areas = areas;
        this.devices = devices;
        
        // Extract unique manufacturers
        if (Array.isArray(devices)) {
          this.manufacturers = [...new Set(
            devices
              .filter(d => d && d.manufacturer)
              .map(d => d.manufacturer as string)
          )].sort();
        } else {
          console.warn('Devices response is not an array:', devices);
          this.manufacturers = [];
          this.devices = [];
        }
        
        this.applyFilters();
        this.isLoading = false;
      })
    );
  }
  
  fetchDevices(): Observable<HADevice[]> {
    return this.haService.fetchDevices();
  }
  
  fetchAreas(): Observable<{ id: string, name: string }[]> {
    return this.haService.fetchAreas().pipe(
      map(areas => {
        // Ensure areas is an array before trying to use map
        if (!Array.isArray(areas)) {
          console.warn('Areas response is not an array:', areas);
          return [];
        }
        
        return areas.map(area => {
          // Ensure area has the expected properties
          if (!area || typeof area !== 'object') {
            console.warn('Invalid area object:', area);
            return null;
          }
          
          return {
            id: area.area_id || '',
            name: area.name || 'Unknown area'
          };
        }).filter((area): area is { id: string, name: string } => area !== null); // Type-safe filter
      })
    );
  }
  
  refreshDevices(): void {
    this.refresh$.next();
  }
  
  applyFilters(): void {
    // Check if devices is an array before proceeding
    if (!Array.isArray(this.devices)) {
      console.warn('Cannot apply filters: devices is not an array');
      this.filteredDevices = [];
      this.updatePagination();
      return;
    }
    
    let filtered = [...this.devices];
    
    // Apply search filter
    if (this.searchTerm) {
      const search = this.searchTerm.toLowerCase();
      filtered = filtered.filter(device => 
        device && (
          (device.name_by_user?.toLowerCase().includes(search) || false) ||
          (device.name?.toLowerCase().includes(search) || false) ||
          (device.manufacturer?.toLowerCase().includes(search) || false) ||
          (device.model?.toLowerCase().includes(search) || false)
        )
      );
    }
    
    // Apply area filter
    if (this.selectedArea) {
      filtered = filtered.filter(device => device && device.area_id === this.selectedArea);
    }
    
    // Apply manufacturer filter
    if (this.selectedManufacturer) {
      filtered = filtered.filter(device => device && device.manufacturer === this.selectedManufacturer);
    }
    
    this.filteredDevices = filtered;
    this.updatePagination();
  }
  
  resetFilters(): void {
    this.searchTerm = '';
    this.selectedArea = this.area || ''; // Preserve input area property
    this.selectedManufacturer = '';
    this.applyFilters();
  }
  
  clearSearch(): void {
    this.searchTerm = '';
    this.applyFilters();
  }
  
  updatePagination(): void {
    // Ensure we have a valid array of filtered devices
    if (!Array.isArray(this.filteredDevices)) {
      console.warn('Cannot update pagination: filteredDevices is not an array');
      this.paginatedDevices = [];
      return;
    }
    
    const startIndex = this.currentPage * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedDevices = this.filteredDevices.slice(startIndex, endIndex);
  }
  
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.itemsPerPage = event.pageSize;
    this.updatePagination();
  }
  
  getDeviceIcon(device: HADevice): string {
    if (!device || !device.id) return 'device_unknown';
    
    const entities = this.getEntitiesForDevice(device.id);
    if (!entities.length) return 'devices_other';
    
    const entityDomains = new Set(
      entities
        .filter(e => e && e.entity_id)
        .map(e => e.entity_id.split('.')[0])
    );
    
    if (entityDomains.has('light')) return 'lightbulb';
    if (entityDomains.has('switch')) return 'power_settings_new';
    if (entityDomains.has('sensor')) return 'sensors';
    if (entityDomains.has('binary_sensor')) return 'fiber_manual_record';
    if (entityDomains.has('climate')) return 'thermostat';
    if (entityDomains.has('camera')) return 'videocam';
    if (entityDomains.has('media_player')) return 'music_note';
    if (entityDomains.has('fan')) return 'mode_fan';
    if (entityDomains.has('cover')) return 'vertical_shades';
    if (entityDomains.has('lock')) return 'lock';
    if (entityDomains.has('vacuum')) return 'robot';
    
    return 'devices_other';
  }
  
  getDomainIcon(entity: HAEntity): string {
    if (!entity || !entity.entity_id) return 'help';
    
    const domain = entity.entity_id.split('.')[0];
    
    switch (domain) {
      case 'light': return 'lightbulb';
      case 'switch': return 'power_settings_new';
      case 'sensor': return 'sensors';
      case 'binary_sensor': return this.getBinarySensorIcon(entity);
      case 'climate': return 'thermostat';
      case 'camera': return 'videocam';
      case 'media_player': return 'music_note';
      case 'fan': return 'mode_fan';
      case 'cover': return 'vertical_shades';
      case 'lock': return 'lock';
      case 'vacuum': return 'robot';
      default: return 'device_unknown';
    }
  }
  
  getBinarySensorIcon(entity: HAEntity): string {
    if (!entity || !entity.attributes) return 'fiber_manual_record';
    
    const deviceClass = entity.attributes['device_class'];
    
    switch (deviceClass) {
      case 'motion': return 'directions_run';
      case 'door': return 'door_front';
      case 'window': return 'window';
      case 'garage_door': return 'garage';
      case 'presence': return 'person';
      case 'connectivity': return 'wifi';
      case 'power': return 'power';
      case 'battery': return 'battery_full';
      default: return 'fiber_manual_record';
    }
  }
  
  isDeviceActive(device: HADevice): boolean {
    if (!device) return false;
    
    const entities = this.getEntitiesForDevice(device.id);
    return entities.some(entity => 
      entity.state && ['on', 'open', 'unlocked', 'playing', 'heating', 'cooling'].includes(entity.state)
    );
  }
  
  getEntitiesForDevice(deviceId: string): HAEntity[] {
    if (!deviceId || !this.entities || !Array.isArray(this.entities)) {
      return [];
    }
    
    return this.entities.filter(entity => 
      entity && entity.attributes && entity.attributes['device_id'] === deviceId
    );
  }
  
  getAreaName(areaId: string | undefined): string {
    if (!areaId || !this.areas || !Array.isArray(this.areas)) {
      return '';
    }
    
    const area = this.areas.find(a => a && a.id === areaId);
    return area ? area.name : '';
  }
  
  formatEntityName(entity: HAEntity): string {
    if (!entity) {
      return 'Unknown Entity';
    }
    
    // First try to get friendly name from attributes
    if (entity.attributes && entity.attributes['friendly_name']) {
      return entity.attributes['friendly_name'];
    }
    
    // Otherwise format the entity_id
    if (!entity.entity_id) {
      return 'Unknown Entity';
    }
    
    const parts = entity.entity_id.split('.');
    if (parts.length !== 2) return entity.entity_id;
    
    // Convert snake_case to Title Case
    return parts[1]
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
  
  getStateClass(entity: HAEntity): string {
    if (!entity || !entity.state) return '';
    
    if (['on', 'open', 'unlocked', 'playing', 'heating', 'cooling'].includes(entity.state)) {
      return 'state-on';
    }
    
    if (['off', 'closed', 'locked', 'idle', 'standby'].includes(entity.state)) {
      return 'state-off';
    }
    
    // For numeric values, no special class
    return '';
  }
  
  viewDeviceDetails(device: HADevice): void {
    this.selectedDevice = device;
    this.detailsExpanded = true;
  }
  
  closeDetails(): void {
    this.detailsExpanded = false;
    setTimeout(() => {
      this.selectedDevice = null;
    }, 300); // Wait for animation to complete
  }
  
  openConfigUrl(device: HADevice): void {
    if (device.configuration_url) {
      window.open(device.configuration_url, '_blank');
    }
  }
} 