import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardItem } from '../../models/dashboard.models';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { HaExplorerModalComponent } from '../../shared/components/ha-explorer-modal/ha-explorer-modal.component';

@Component({
  selector: 'app-device-browser-tile',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule
  ],
  template: `
    <div class="device-browser-container">
      <div class="device-browser-header">
        <h3>{{ title || 'Device Browser' }}</h3>
      </div>
      
      <div class="device-browser-content">
        <div class="browser-placeholder">
          <mat-icon>devices</mat-icon>
          <p>View and manage Home Assistant devices</p>
          <button mat-raised-button color="primary" (click)="openDeviceExplorer()">
            O<PERSON><PERSON> EXPLORER
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .device-browser-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      background-color: var(--background-secondary, #ffffff);
      border-radius: 8px;
      overflow: hidden;
    }
    
    .device-browser-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }
    
    .device-browser-header h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: var(--text-color, rgba(0, 0, 0, 0.87));
    }
    
    .device-browser-content {
      flex: 1;
      padding: 16px;
      display: flex;
      flex-direction: column;
    }
    
    .browser-placeholder {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      color: var(--text-color-secondary, rgba(0, 0, 0, 0.6));
      text-align: center;
    }
    
    .browser-placeholder mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
    }
  `]
})
export class DeviceBrowserTileComponent {
  @Input() title?: string;
  @Input() isEditingEnabled = false;
  
  // Backwards compatibility
  @Input() set item(val: DashboardItem) {
    if (val && val.title) {
      this.title = val.title;
    }
  }

  constructor(private dialog: MatDialog) { }
  
  openDeviceExplorer(): void {
    const dialogRef = this.dialog.open(HaExplorerModalComponent, {
      width: '90vw',
      maxWidth: '1200px',
      height: '90vh',
      maxHeight: '90vh',
      panelClass: 'ha-explorer-dialog',
      data: {
        initialTab: 'devices'
      }
    });
  }
} 