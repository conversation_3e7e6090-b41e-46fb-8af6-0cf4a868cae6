import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-floorplan-placeholder',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="floorplan-placeholder">
      <svg width="100%" height="100%" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg" class="placeholder-svg">
        <!-- Background -->
        <rect width="600" height="400" class="svg-background" />
        
        <!-- House outline -->
        <rect x="100" y="50" width="400" height="300" class="svg-house" />
        
        <!-- Roof -->
        <polygon points="100,50 300,10 500,50" class="svg-roof" />
        
        <!-- Door -->
        <rect x="280" y="280" width="40" height="70" class="svg-door" />
        <circle cx="290" cy="320" r="3" class="svg-doorknob" />
        
        <!-- Windows -->
        <rect x="150" y="120" width="60" height="40" class="svg-window" />
        <rect x="390" y="120" width="60" height="40" class="svg-window" />
        
        <!-- Rooms -->
        <line x1="300" y1="50" x2="300" y2="350" class="svg-line" />
        <line x1="100" y1="200" x2="500" y2="200" class="svg-line" />
        
        <!-- Text -->
        <text x="300" y="380" font-family="Arial" font-size="14" class="svg-text" text-anchor="middle">Floorplan Placeholder</text>
      </svg>
    </div>
  `,
  styles: [`
    .floorplan-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--background-secondary);
      border-radius: 4px;
    }
    
    svg {
      max-width: 100%;
      max-height: 100%;
    }

    .svg-background {
      fill: var(--background-color);
    }

    .svg-house {
      fill: var(--background-secondary);
      stroke: var(--border-color);
      stroke-width: 2;
    }

    .svg-roof {
      fill: var(--background-color);
      stroke: var(--border-color);
      stroke-width: 2;
    }

    .svg-door {
      fill: var(--secondary-color);
      stroke: var(--text-secondary);
      stroke-width: 2;
    }

    .svg-doorknob {
      fill: var(--text-color);
    }

    .svg-window {
      fill: var(--primary-color);
      opacity: 0.3;
      stroke: var(--text-secondary);
      stroke-width: 2;
    }

    .svg-line {
      stroke: var(--border-color);
      stroke-width: 1;
      stroke-dasharray: 5,5;
    }

    .svg-text {
      fill: var(--text-secondary);
    }
  `]
})
export class FloorplanPlaceholderComponent {} 