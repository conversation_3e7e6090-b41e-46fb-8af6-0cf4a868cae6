# Floorplan Component Refactor Plan

## Overview

This document outlines the comprehensive refactoring plan for the floorplan component, breaking down the monolithic component into smaller, more maintainable pieces.

## Current Status: Phase 1 Complete ✅

### What We've Accomplished

1. **Component Architecture Refactoring**
   - ✅ Created `FloorplanViewerComponent` for SVG/image loading and display
   - ✅ Created `MarkerOverlayComponent` for device markers and overlays
   - ✅ Reduced main component from 1452 lines to ~800 lines
   - ✅ Updated template to use new component structure

2. **Service Layer**
   - ✅ Enhanced `FloorplanLoaderService` for robust floorplan loading
   - ✅ Created `RoomManagerService` for room operations (ready for extraction)
   - ✅ Existing `SvgHelperService` for SVG manipulation

3. **Documentation**
   - ✅ Comprehensive README with architecture overview
   - ✅ Examples and troubleshooting guides
   - ✅ Migration guide for breaking changes
   - ✅ Consolidated room documentation

4. **Template Modernization**
   - ✅ Replaced inline SVG handling with dedicated viewer component
   - ✅ Extracted marker rendering to overlay component
   - ✅ Cleaner event handling and data flow

## Phase 2: Advanced Component Extraction 🚧

### Next Components to Extract

#### 1. RoomManagerComponent
**Purpose**: Handle all room-related operations
**Responsibilities**:
- Room creation and editing
- Multi-room selection
- Room validation and sanitization
- Custom room polygon creation

**Files to Create**:
```
components/room-manager/
├── room-manager.component.ts
├── room-manager.component.html
├── room-manager.component.scss
└── room-creation-modal/
    ├── room-creation-modal.component.ts
    ├── room-creation-modal.component.html
    └── room-creation-modal.component.scss
```

#### 2. FloorplanControlsComponent
**Purpose**: Edit mode controls and toolbar
**Responsibilities**:
- Edit mode toggle buttons
- Room creation controls
- Multi-select mode controls
- Save/cancel operations

**Files to Create**:
```
components/floorplan-controls/
├── floorplan-controls.component.ts
├── floorplan-controls.component.html
└── floorplan-controls.component.scss
```

#### 3. HomeAssistantIntegrationComponent
**Purpose**: Manage HA device integration
**Responsibilities**:
- Device state monitoring
- Entity management
- Service calls
- Connection status

**Files to Create**:
```
components/ha-integration/
├── ha-integration.component.ts
├── ha-integration.component.html
├── ha-integration.component.scss
└── device-marker/
    ├── device-marker.component.ts
    ├── device-marker.component.html
    └── device-marker.component.scss
```

## Phase 3: Enhanced Features 🔮

### New Features to Implement

1. **Drag & Drop Interface**
   - Drag markers to reposition
   - Drag rooms to move
   - Visual feedback during operations

2. **Advanced Room Management**
   - Room grouping and categories
   - Room templates
   - Bulk operations

3. **Export/Import System**
   - Export floorplan configurations
   - Import from various formats
   - Backup and restore

4. **Enhanced Accessibility**
   - Keyboard navigation
   - Screen reader support
   - High contrast mode

## Implementation Strategy

### Step 1: Extract RoomManagerComponent (Next Priority)

1. **Create the component structure**
   ```bash
   mkdir -p apps/ha-dashboard/src/app/tiles/floorplan/components/room-manager
   ```

2. **Move room-related methods from main component**
   - `toggleRoomCreationMode()`
   - `openRoomCreationModal()`
   - `saveNewRoom()`
   - `editRoomDetails()`
   - Multi-select methods

3. **Update main component**
   - Remove extracted methods
   - Add RoomManagerComponent to template
   - Set up event communication

4. **Test integration**
   - Ensure all room operations work
   - Verify event flow
   - Test edge cases

### Step 2: Extract FloorplanControlsComponent

1. **Move control-related template sections**
   - Header controls
   - Edit mode buttons
   - Multi-select controls

2. **Create component logic**
   - Button state management
   - Event emission
   - Conditional rendering

### Step 3: Extract HomeAssistantIntegrationComponent

1. **Move HA-related methods**
   - Device management
   - Entity state handling
   - Service calls

2. **Create dedicated service**
   - `FloorplanHAService`
   - Device mapping logic
   - State synchronization

## Benefits of This Approach

### Maintainability
- **Single Responsibility**: Each component has one clear purpose
- **Easier Testing**: Smaller components are easier to unit test
- **Reduced Complexity**: Main component becomes orchestrator only

### Reusability
- **Component Reuse**: Components can be used in other contexts
- **Service Reuse**: Services can be shared across features
- **Template Reuse**: Consistent UI patterns

### Performance
- **Change Detection**: Smaller components optimize Angular's change detection
- **Lazy Loading**: Components can be loaded on demand
- **Memory Usage**: Better garbage collection with smaller scopes

### Developer Experience
- **Code Navigation**: Easier to find and modify specific functionality
- **Parallel Development**: Multiple developers can work on different components
- **Debugging**: Isolated components are easier to debug

## Migration Checklist

### For Each New Component

- [ ] Create component files
- [ ] Move relevant methods from main component
- [ ] Update imports and dependencies
- [ ] Create component template
- [ ] Add component styles
- [ ] Update main component template
- [ ] Set up event communication
- [ ] Add unit tests
- [ ] Update documentation
- [ ] Test integration

### Quality Gates

- [ ] All existing functionality works
- [ ] No performance regressions
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Documentation is updated
- [ ] Code review completed

## Timeline Estimate

- **Phase 2 (Component Extraction)**: 2-3 weeks
- **Phase 3 (Enhanced Features)**: 4-6 weeks
- **Testing & Polish**: 1-2 weeks

**Total Estimated Time**: 7-11 weeks

## Success Metrics

- **Code Reduction**: Main component under 500 lines
- **Test Coverage**: >90% for all components
- **Performance**: No regression in rendering time
- **Maintainability**: Cyclomatic complexity under 10 per method
- **Documentation**: Complete API documentation for all components

## Risks and Mitigation

### Risk: Breaking Existing Functionality
**Mitigation**: Comprehensive testing at each step, feature flags for new components

### Risk: Performance Regression
**Mitigation**: Performance testing, profiling, and optimization

### Risk: Increased Complexity
**Mitigation**: Clear documentation, consistent patterns, code reviews

## Conclusion

This refactoring plan transforms the floorplan component from a monolithic structure into a modern, maintainable, and extensible architecture. The phased approach ensures minimal disruption while delivering significant improvements in code quality and developer experience.
