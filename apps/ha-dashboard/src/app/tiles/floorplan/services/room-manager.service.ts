import { Injectable } from '@angular/core';
import { Room, Point } from '../../../models/dashboard.models';

/**
 * Service to manage room operations for floorplan components
 * This service will be used to extract room management logic from the main component
 */
@Injectable({
  providedIn: 'root'
})
export class RoomManagerService {
  
  constructor() {}
  
  /**
   * Generate a readable room name from an ID
   */
  generateRoomName(roomId: string): string {
    // Convert kebab-case or snake_case to title case
    return roomId
      .replace(/[-_]/g, ' ')
      .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase());
  }
  
  /**
   * Validate room data before saving
   */
  validateRoom(room: Partial<Room>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!room.id || room.id.trim() === '') {
      errors.push('Room ID is required');
    }
    
    if (!room.name || room.name.trim() === '') {
      errors.push('Room name is required');
    }
    
    if (!room.type) {
      errors.push('Room type is required');
    }
    
    if (room.customPolygon && room.customPolygon.length < 3) {
      errors.push('Custom room polygon must have at least 3 points');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Create a default room object
   */
  createDefaultRoom(id: string): Room {
    return {
      id,
      name: this.generateRoomName(id),
      type: 'room',
      imagePath: `/assets/images/rooms/${id}.png`
    };
  }
  
  /**
   * Get room colors based on type
   */
  getRoomColor(roomType: Room['type']): string {
    const roomColors: Record<string, string> = {
      'room': 'rgba(var(--primary-color-rgb), 0.4)',
      'bathroom': 'rgba(77, 195, 236, 0.4)',
      'bedroom': 'rgba(139, 195, 74, 0.4)',
      'kitchen': 'rgba(255, 193, 7, 0.4)',
      'living': 'rgba(255, 152, 0, 0.4)',
      'hallway': 'rgba(189, 189, 189, 0.4)',
      'other': 'rgba(156, 39, 176, 0.4)'
    };
    
    return roomColors[roomType] || roomColors['room'];
  }
  
  /**
   * Calculate the center point of a polygon
   */
  calculatePolygonCenter(points: Point[]): Point {
    if (points.length === 0) {
      return { x: 0, y: 0 };
    }
    
    const sum = points.reduce(
      (acc, point) => ({
        x: acc.x + point.x,
        y: acc.y + point.y
      }),
      { x: 0, y: 0 }
    );
    
    return {
      x: sum.x / points.length,
      y: sum.y / points.length
    };
  }
  
  /**
   * Calculate the area of a polygon using the shoelace formula
   */
  calculatePolygonArea(points: Point[]): number {
    if (points.length < 3) {
      return 0;
    }
    
    let area = 0;
    for (let i = 0; i < points.length; i++) {
      const j = (i + 1) % points.length;
      area += points[i].x * points[j].y;
      area -= points[j].x * points[i].y;
    }
    
    return Math.abs(area) / 2;
  }
  
  /**
   * Check if a point is inside a polygon
   */
  isPointInPolygon(point: Point, polygon: Point[]): boolean {
    let inside = false;
    
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      if (
        polygon[i].y > point.y !== polygon[j].y > point.y &&
        point.x < ((polygon[j].x - polygon[i].x) * (point.y - polygon[i].y)) / (polygon[j].y - polygon[i].y) + polygon[i].x
      ) {
        inside = !inside;
      }
    }
    
    return inside;
  }
  
  /**
   * Simplify a polygon by removing unnecessary points
   */
  simplifyPolygon(points: Point[], tolerance: number = 1): Point[] {
    if (points.length <= 3) {
      return points;
    }
    
    // Douglas-Peucker algorithm implementation
    const simplified: Point[] = [];
    
    // Find the point with the maximum distance from the line between first and last points
    let maxDistance = 0;
    let maxIndex = 0;
    
    for (let i = 1; i < points.length - 1; i++) {
      const distance = this.distanceFromLine(points[i], points[0], points[points.length - 1]);
      if (distance > maxDistance) {
        maxDistance = distance;
        maxIndex = i;
      }
    }
    
    // If the maximum distance is greater than tolerance, recursively simplify
    if (maxDistance > tolerance) {
      const left = this.simplifyPolygon(points.slice(0, maxIndex + 1), tolerance);
      const right = this.simplifyPolygon(points.slice(maxIndex), tolerance);
      
      // Combine the results
      simplified.push(...left.slice(0, -1), ...right);
    } else {
      // If the maximum distance is within tolerance, return the endpoints
      simplified.push(points[0], points[points.length - 1]);
    }
    
    return simplified;
  }
  
  /**
   * Calculate the distance from a point to a line
   */
  private distanceFromLine(point: Point, lineStart: Point, lineEnd: Point): number {
    const A = point.x - lineStart.x;
    const B = point.y - lineStart.y;
    const C = lineEnd.x - lineStart.x;
    const D = lineEnd.y - lineStart.y;
    
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) {
      // Line start and end are the same point
      return Math.sqrt(A * A + B * B);
    }
    
    const param = dot / lenSq;
    
    let xx: number, yy: number;
    
    if (param < 0) {
      xx = lineStart.x;
      yy = lineStart.y;
    } else if (param > 1) {
      xx = lineEnd.x;
      yy = lineEnd.y;
    } else {
      xx = lineStart.x + param * C;
      yy = lineStart.y + param * D;
    }
    
    const dx = point.x - xx;
    const dy = point.y - yy;
    
    return Math.sqrt(dx * dx + dy * dy);
  }
  
  /**
   * Generate a unique room ID
   */
  generateUniqueRoomId(existingIds: string[], baseName: string = 'room'): string {
    let counter = 1;
    let id = baseName;
    
    while (existingIds.includes(id)) {
      id = `${baseName}-${counter}`;
      counter++;
    }
    
    return id;
  }
  
  /**
   * Sanitize room data for storage
   */
  sanitizeRoom(room: Room): Room {
    return {
      id: room.id.trim(),
      name: room.name.trim(),
      type: room.type,
      imagePath: room.imagePath?.trim(),
      description: room.description?.trim(),
      devices: room.devices?.filter(device => device.trim() !== ''),
      customPolygon: room.customPolygon,
      isCustomShape: room.isCustomShape
    };
  }
}
