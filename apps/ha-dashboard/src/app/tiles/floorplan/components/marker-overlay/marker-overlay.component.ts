import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FloorplanMarker } from '../../../../models/dashboard.models';
import { HAEntity } from '../../../../services/home-assistant.service';

@Component({
  selector: 'app-marker-overlay',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div *ngIf="markers.length > 0" class="floorplan-markers">
      <div
        *ngFor="let marker of markers"
        class="floorplan-marker"
        [ngClass]="[marker.type, marker.entityId ? 'ha-device' : '', getMarkerStateClass(marker)]"
        [style.left.%]="marker.x"
        [style.top.%]="marker.y"
        [style.background-color]="marker.color"
        [title]="getMarkerTooltip(marker)"
        (click)="onMarkerClick(marker)"
        [attr.aria-label]="getMarkerAriaLabel(marker)">

        <!-- Marker icon -->
        <i *ngIf="marker.icon" [class]="marker.icon" class="marker-icon"></i>

        <!-- Marker label -->
        <span *ngIf="marker.label && showLabels" class="marker-label">
          {{ marker.label }}
        </span>

        <!-- Entity state display -->
        <span *ngIf="marker.showState && marker.entityId" class="marker-state">
          {{ getEntityState(marker.entityId) }}
        </span>

        <!-- Loading indicator for HA devices -->
        <div *ngIf="marker.entityId && isEntityLoading(marker.entityId)" class="marker-loading">
          <i class="fas fa-spinner fa-spin"></i>
        </div>

        <!-- Error indicator for HA devices -->
        <div *ngIf="marker.entityId && hasEntityError(marker.entityId)" class="marker-error">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .floorplan-markers {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 10;
    }

    .floorplan-marker {
      position: absolute;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      pointer-events: auto;
      transform: translate(-50%, -50%);
      transition: all 0.2s ease;
      border: 2px solid rgba(255, 255, 255, 0.8);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      font-size: 12px;
      color: white;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .floorplan-marker:hover {
      transform: translate(-50%, -50%) scale(1.1);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      z-index: 20;
    }

    .floorplan-marker.light {
      background-color: #ffcc00;
    }

    .floorplan-marker.light.on {
      background-color: #ffd700;
      box-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
    }

    .floorplan-marker.light.off {
      background-color: #666666;
    }

    .floorplan-marker.sensor {
      background-color: #3366ff;
    }

    .floorplan-marker.sensor.active {
      background-color: #ff3366;
    }

    .floorplan-marker.door {
      background-color: #8b4513;
    }

    .floorplan-marker.door.open {
      background-color: #ff6b35;
    }

    .floorplan-marker.door.closed {
      background-color: #4a4a4a;
    }

    .floorplan-marker.window {
      background-color: #87ceeb;
    }

    .floorplan-marker.switch {
      background-color: #32cd32;
    }

    .floorplan-marker.switch.on {
      background-color: #00ff00;
    }

    .floorplan-marker.switch.off {
      background-color: #666666;
    }

    .floorplan-marker.device {
      background-color: #9370db;
    }

    .floorplan-marker.custom {
      background-color: #ff69b4;
    }

    .marker-icon {
      font-size: 14px;
      line-height: 1;
    }

    .marker-label {
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      white-space: nowrap;
      margin-top: 4px;
      pointer-events: none;
    }

    .marker-state {
      position: absolute;
      top: -100%;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      white-space: nowrap;
      margin-top: -4px;
      pointer-events: none;
    }

    .marker-loading {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 12px;
      height: 12px;
      background-color: #007bff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 8px;
      color: white;
    }

    .marker-error {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 12px;
      height: 12px;
      background-color: #dc3545;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 8px;
      color: white;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .floorplan-marker {
        width: 28px;
        height: 28px;
        font-size: 14px;
      }

      .marker-icon {
        font-size: 16px;
      }
    }
  `]
})
export class MarkerOverlayComponent {
  @Input() markers: FloorplanMarker[] = [];
  @Input() haEntities: HAEntity[] = [];
  @Input() showLabels = true;
  @Input() isEditingEnabled = false;
  @Input() loadingEntityIds: string[] = [];
  @Input() errorEntityIds: string[] = [];

  @Output() markerClick = new EventEmitter<FloorplanMarker>();
  @Output() deviceMarkerClick = new EventEmitter<FloorplanMarker>();

  constructor() {}

  onMarkerClick(marker: FloorplanMarker): void {
    if (marker.entityId) {
      this.deviceMarkerClick.emit(marker);
    } else {
      this.markerClick.emit(marker);
    }
  }

  /**
   * Get CSS classes for marker based on entity state
   */
  getMarkerStateClass(marker: FloorplanMarker): string {
    if (!marker.entityId) return '';

    const entity = this.getEntity(marker.entityId);
    if (!entity) return '';

    const domain = entity.entity_id.split('.')[0];
    const state = entity.state;

    switch (domain) {
      case 'light':
      case 'switch':
        return state === 'on' ? 'on' : 'off';
      case 'binary_sensor':
        return state === 'on' ? 'active' : 'inactive';
      case 'lock':
        return state === 'locked' ? 'locked' : 'unlocked';
      case 'cover':
        return state === 'open' ? 'open' : 'closed';
      case 'door':
        return state === 'open' ? 'open' : 'closed';
      default:
        return state;
    }
  }

  /**
   * Get tooltip text for marker
   */
  getMarkerTooltip(marker: FloorplanMarker): string {
    if (marker.entityId) {
      const entity = this.getEntity(marker.entityId);
      if (entity) {
        const friendlyName = entity.attributes['friendly_name'] || entity.entity_id;
        const state = this.getEntityState(marker.entityId);
        return `${friendlyName}${state ? ': ' + state : ''}`;
      }
    }

    return marker.label || marker.id;
  }

  /**
   * Get aria label for accessibility
   */
  getMarkerAriaLabel(marker: FloorplanMarker): string {
    if (marker.entityId) {
      const entity = this.getEntity(marker.entityId);
      if (entity) {
        const friendlyName = entity.attributes['friendly_name'] || entity.entity_id;
        const state = this.getEntityState(marker.entityId);
        const domain = entity.entity_id.split('.')[0];
        return `${domain} ${friendlyName}, current state: ${state}`;
      }
    }

    return `Marker ${marker.label || marker.id}`;
  }

  /**
   * Get formatted entity state for display
   */
  getEntityState(entityId: string): string {
    if (!entityId || !this.haEntities || this.haEntities.length === 0) {
      return '';
    }

    const entity = this.getEntity(entityId);
    if (!entity) {
      return '';
    }

    // Return formatted state based on domain
    const domain = entityId.split('.')[0];

    switch (domain) {
      case 'sensor':
        // For sensors, include the unit of measurement if available
        if (entity.attributes['unit_of_measurement']) {
          return `${entity.state}${entity.attributes['unit_of_measurement']}`;
        }
        return entity.state;

      case 'binary_sensor':
      case 'light':
      case 'switch':
        // For binary devices, return a more readable state
        return entity.state === 'on' ? 'ON' : 'OFF';

      case 'lock':
        return entity.state === 'locked' ? 'LOCKED' : 'UNLOCKED';

      case 'cover':
        return entity.state.toUpperCase();

      default:
        return entity.state;
    }
  }

  /**
   * Check if entity is currently loading
   */
  isEntityLoading(entityId: string): boolean {
    return this.loadingEntityIds.includes(entityId);
  }

  /**
   * Check if entity has an error
   */
  hasEntityError(entityId: string): boolean {
    return this.errorEntityIds.includes(entityId);
  }

  /**
   * Get entity by ID
   */
  private getEntity(entityId: string): HAEntity | undefined {
    return this.haEntities.find(e => e.entity_id === entityId);
  }
}