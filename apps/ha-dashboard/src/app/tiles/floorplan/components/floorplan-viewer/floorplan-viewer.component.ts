import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, OnInit, OnDestroy, NgZone } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SafeHtmlPipe } from '../../../../shared/pipes/safe-html.pipe';
import { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';
import { EmptyStateComponent } from '../../../../shared/components/empty-state/empty-state.component';
import { FloorplanPlaceholderComponent } from '../../floorplan-placeholder.component';
import { FloorplanLoaderService } from '../../../../services/floorplan-loader.service';
import { SvgHelperService } from '../../../../services/svg-helper.service';
import { NotificationService } from '../../../../services/notification.service';
import { Room } from '../../../../models/dashboard.models';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-floorplan-viewer',
  standalone: true,
  imports: [
    CommonModule,
    SafeHtmlPipe,
    LoadingSpinnerComponent,
    EmptyStateComponent,
    FloorplanPlaceholderComponent
  ],
  template: `
    <div class="floorplan-content">
      <!-- Loading state -->
      <app-loading-spinner *ngIf="isLoading" [overlay]="true" message="Loading floorplan..."></app-loading-spinner>

      <!-- Error state (if completely failed) -->
      <app-empty-state
        *ngIf="!isLoading && hasError && !usePlaceholder"
        title="Image Not Found"
        [description]="errorMessage"
        icon="fa fa-exclamation-triangle"
        actionText="Reload"
        (actionClick)="reloadImage()">
      </app-empty-state>
      
      <!-- Placeholder floorplan (if real image failed to load) -->
      <app-floorplan-placeholder *ngIf="!isLoading && usePlaceholder"></app-floorplan-placeholder>
      
      <!-- Floorplan image when loaded successfully -->
      <div *ngIf="!isLoading && !hasError" class="floorplan-display-container">
        <!-- SVG floorplan - directly embedded if it's an SVG -->
        <div *ngIf="isSvgFile" #svgContainer class="floorplan-svg-container"
             [innerHTML]="svgContent | safeHtml"
             (click)="onSvgContainerClick($event)"></div>

        <!-- Regular image floorplan (for PNG, JPG, etc.) -->
        <img *ngIf="!isSvgFile" [src]="floorplanImage" alt="Floor Plan" class="floorplan-image">

        <!-- View mode indicator - shown only when dashboard is locked -->
        <div *ngIf="!isEditingEnabled && interactive" class="view-mode-indicator">
          <i class="fa fa-search-plus"></i> Click on rooms to view details
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      width: 100%;
      height: 100%;
    }

    .floorplan-content {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      background-color: var(--background-secondary);
      display: flex;
      flex-direction: column;
    }

    .floorplan-display-container {
      flex: 1;
      width: 100%;
      height: 100%;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .floorplan-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .floorplan-svg-container {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;

      ::ng-deep svg {
        width: 100%;
        height: 100%;
        object-fit: contain;
        object-position: center;
      }
    }
    
    .view-mode-indicator {
      position: absolute;
      bottom: 10px;
      right: 10px;
      background-color: rgba(0, 0, 0, 0.6);
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
      pointer-events: none;
    }
  `]
})
export class FloorplanViewerComponent implements OnInit, OnDestroy {
  @Input() imagePath = '/floorplan.svg';
  @Input() isEditingEnabled = false;
  @Input() interactive = false;
  @Input() svgContent = '';
  
  @Output() svgContentChange = new EventEmitter<string>();
  @Output() svgContainerClick = new EventEmitter<MouseEvent>();
  @Output() roomClick = new EventEmitter<{roomId: string, event: MouseEvent}>();
  @Output() svgLoaded = new EventEmitter<ElementRef>();
  
  @ViewChild('svgContainer') svgContainer?: ElementRef;
  
  floorplanImage = '/floorplan.svg';
  isSvgFile = false;
  isLoading = true;
  hasError = false;
  errorMessage = '';
  usePlaceholder = false;
  
  private loadingTimeout: ReturnType<typeof setTimeout> | null = null;
  private subscriptions = new Subscription();
  
  constructor(
    private floorplanLoader: FloorplanLoaderService,
    private svgHelper: SvgHelperService,
    private ngZone: NgZone,
    private notificationService: NotificationService
  ) {}
  
  ngOnInit(): void {
    this.floorplanImage = this.imagePath;
    this.isSvgFile = this.floorplanImage.toLowerCase().endsWith('.svg');
    
    // Load the floorplan
    this.loadFloorplan();
  }
  
  ngOnDestroy(): void {
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }
    this.subscriptions.unsubscribe();
  }
  
  loadFloorplan(): void {
    this.isLoading = true;
    this.hasError = false;
    
    // Get possible paths for loading the floorplan
    const possiblePaths = this.floorplanLoader.getPossibleFloorplanPaths(this.floorplanImage);
    console.log('Initial load: Trying these paths in order:', possiblePaths);
    
    // Give a small delay before starting load attempts
    this.loadingTimeout = setTimeout(() => {
      // Try each path in sequence until one works
      this.tryLoadingFromPaths(possiblePaths);
    }, 800);
  }
  
  reloadImage(): void {
    this.isLoading = true;
    this.hasError = false;
    this.usePlaceholder = false;
    
    // Log for debugging
    console.log(`Attempting to reload floorplan from path: ${this.floorplanImage}`);
    
    // Get possible paths for loading the floorplan
    const possiblePaths = this.floorplanLoader.getPossibleFloorplanPaths(this.floorplanImage);
    console.log('Reload: Trying these paths in order:', possiblePaths);
    
    // Try each path in sequence until one works
    this.tryLoadingFromPaths(possiblePaths);
  }
  
  private tryLoadingFromPaths(paths: string[], index = 0): void {
    if (index >= paths.length) {
      // All paths failed, show error
      this.isLoading = false;
      this.hasError = true;
      this.errorMessage = 'Could not load floorplan from any known location. Using placeholder instead.';
      this.usePlaceholder = true;
      this.notificationService.error('Failed to load floorplan from any available location');
      return;
    }
    
    // Try current path
    this.floorplanImage = paths[index];
    console.log(`Trying path ${index + 1}/${paths.length}: ${this.floorplanImage}`);
    
    // Update SVG flag based on current path
    this.isSvgFile = this.floorplanImage.toLowerCase().endsWith('.svg');
    
    // Create a new image object to test the path
    const img = new Image();
    img.onload = () => {
      console.log(`Successfully loaded from path: ${this.floorplanImage}`);
      this.continueReloading();
    };
    img.onerror = () => {
      console.log(`Failed to load from path: ${this.floorplanImage}`);
      // Try the next path
      this.tryLoadingFromPaths(paths, index + 1);
    };
    
    // Set crossorigin attribute to handle CORS issues
    img.crossOrigin = 'anonymous';
    
    // Add cache-busting parameter to prevent browser caching
    const cacheBuster = `?t=${new Date().getTime()}`;
    img.src = this.floorplanImage + cacheBuster;
  }
  
  private continueReloading(): void {
    this.hasError = false;
    this.usePlaceholder = false;
    
    // Small delay to prevent loading flicker
    this.loadingTimeout = setTimeout(() => {
      if (this.isSvgFile) {
        console.log(`Loading SVG content from: ${this.floorplanImage}`);
        this.loadSvgContent();
      } else {
        console.log(`Loading regular image from: ${this.floorplanImage}`);
        this.isLoading = false;
      }
    }, 500);
  }
  
  private loadSvgContent(): void {
    this.svgHelper.loadSvgFromUrl(this.floorplanImage)
      .subscribe(svgContent => {
        if (svgContent) {
          this.svgContent = svgContent;
          this.svgContentChange.emit(this.svgContent);
          this.hasError = false;
          this.usePlaceholder = false;
          this.isLoading = false;

          // Setup interactivity after the SVG is loaded and rendered
          setTimeout(() => {
            this.setupSvgInteractivity();
            if (this.svgContainer) {
              this.svgLoaded.emit(this.svgContainer);
            }
          }, 100);
        } else {
          this.hasError = true;
          this.errorMessage = 'Failed to load SVG floorplan. Using placeholder instead.';
          this.usePlaceholder = true;
          this.isLoading = false;
          this.notificationService.error('Failed to load SVG floorplan content');
        }
      });
  }
  
  private setupSvgInteractivity(): void {
    if (!this.svgContainer || !this.interactive) return;
    
    // Extract all room IDs from the SVG
    const roomIds = this.svgHelper.extractRoomIds(this.svgContent);
    
    // Run outside Angular zone for better performance
    this.ngZone.runOutsideAngular(() => {
      // Enhanced room element interactivity
      setTimeout(() => {
        // Select all room elements in the SVG
        this.svgContainer?.nativeElement.querySelectorAll('[id^="room-"]').forEach((roomElement: SVGElement) => {
          // Setup click handler for each room
          roomElement.addEventListener('click', (event) => {
            // Run back in Angular zone when handling events
            this.ngZone.run(() => {
              const roomId = roomElement.id.replace('room-', '');
              this.roomClick.emit({ roomId, event: event as MouseEvent });
            });
          });
        });
      }, 100);
    });
  }
  
  onSvgContainerClick(event: MouseEvent): void {
    this.svgContainerClick.emit(event);
  }
}