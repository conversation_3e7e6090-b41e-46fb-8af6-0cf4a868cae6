:host {
  display: block;
  width: 100%;
  height: 100%;
}

.floorplan-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--background-secondary);
  border-radius: 4px;
  box-shadow: 0 2px 8px var(--box-shadow);
}

.floorplan-header {
  padding: 10px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
  }
  
  // Room controls
  .floorplan-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .room-creation-btn, .detect-rooms-btn, .multi-select-btn, 
    .edit-selected-btn, .save-selected-btn, .clear-selection-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 5px 10px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      
      &:hover {
        background-color: var(--primary-hover);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background-color: var(--secondary-color);
      }
      
      i {
        font-size: 14px;
      }
      
      &.active {
        background-color: var(--success-color);
        
        &:hover {
          background-color: var(--success-color);
          opacity: 0.9;
        }
      }
    }
    
    .multi-select-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-left: 8px;
      padding-left: 8px;
      border-left: 1px solid var(--border-color);
      
      .selection-count {
        font-size: 12px;
        font-weight: 500;
        color: var(--text-color);
        background-color: var(--background-color);
        padding: 4px 8px;
        border-radius: 4px;
      }
      
      .edit-selected-btn {
        background-color: var(--success-color);
      }
      
      .save-selected-btn {
        background-color: var(--primary-color);
      }
      
      .clear-selection-btn {
        background-color: var(--error-color);
      }
    }
    
    .detect-rooms-btn {
      background-color: var(--primary-color);
      border: 1px solid var(--primary-hover);
      
      &:hover {
        background-color: var(--primary-hover);
      }
    }
  }
}

.floorplan-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.floorplan-viewer-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: var(--background-secondary);
  min-height: 0; // Important for flex child to shrink

  // Ensure the floorplan viewer component fills the wrapper
  app-floorplan-viewer {
    display: block;
    width: 100%;
    height: 100%;
  }
}

  // Regular image styles
  .floorplan-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  // SVG container styles
  .floorplan-svg-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    ::ng-deep svg {
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: center;
      
      // Room highlighting styles
      [id^="room-"] {
        cursor: pointer;
        transition: fill 0.3s ease, stroke 0.3s ease, stroke-width 0.3s ease;
        
        &:hover {
          fill-opacity: 0.8;
        }
        
        &.highlighted {
          fill: rgba(var(--primary-color-rgb), 0.3);
          stroke-width: 2px;
          stroke: var(--primary-color);
          fill-opacity: 0.7;
        }
        
        &.room-active {
          fill: rgba(var(--success-color), 0.3);
          stroke-width: 2px;
          stroke: var(--success-color);
        }
        
        // Selected room style for multi-select
        &.selected {
          stroke-width: 3px;
          stroke: var(--primary-color);
          stroke-dasharray: 5,3;
          filter: drop-shadow(0 0 5px rgba(var(--primary-color-rgb), 0.5));
        }
        
        // Custom room styles
        &.custom-room {
          fill: rgba(var(--primary-color-rgb), 0.2);
          stroke: var(--primary-color);
          stroke-width: 1.5px;
          stroke-dasharray: none;
          transition: all 0.2s ease-in-out;
          
          &:hover {
            fill: rgba(var(--primary-color-rgb), 0.4);
            stroke-width: 2px;
            filter: drop-shadow(0 0 3px rgba(var(--primary-color-rgb), 0.5));
          }
          
          &.highlighted {
            fill: rgba(var(--primary-color-rgb), 0.4);
            stroke-width: 2.5px;
            filter: drop-shadow(0 0 5px rgba(var(--primary-color-rgb), 0.7));
          }
          
          &.room-active {
            fill: rgba(var(--success-color), 0.4);
            stroke: var(--success-color);
            stroke-width: 2px;
          }
          
          &.selected {
            stroke-width: 3px;
            stroke: var(--primary-color);
            stroke-dasharray: 5,3;
            filter: drop-shadow(0 0 5px rgba(var(--primary-color-rgb), 0.5));
          }
        }
      }
      
      // Custom polygon creation styles
      #polygon-in-progress {
        cursor: crosshair;
        pointer-events: none;
      }
      
      // Detected rooms styles
      .detected-room {
        animation: pulse 2s infinite alternate;
        cursor: pointer;
        
        &:hover {
          fill-opacity: 0.7;
          stroke-width: 2px;
        }
        
        &.editable {
          stroke-dasharray: 5,3;
          stroke: var(--text-color);
        }
        
        &.auto-detected {
          stroke: var(--primary-color);
          stroke-width: 1.5px;
        }
        
        &.selected {
          stroke-width: 3px;
          stroke: var(--primary-color);
          filter: drop-shadow(0 0 5px rgba(var(--primary-color-rgb), 0.5));
        }
      }
      
      .detected-room-label {
        pointer-events: none;
        user-select: none;
        font-family: Roboto, sans-serif;
      }
      
      // Group for detected rooms
      #detected-rooms-preview {
        pointer-events: all;
      }
      
      // Add different colors for different room types using theme variables
      [data-room-type="bathroom"] {
        fill: rgba(var(--primary-color-rgb), 0.4);
      }
      
      [data-room-type="bedroom"] {
        fill: rgba(var(--success-color), 0.4);
      }
      
      [data-room-type="kitchen"] {
        fill: rgba(var(--primary-color-rgb), 0.4);
      }
      
      [data-room-type="living"] {
        fill: rgba(var(--success-color), 0.4);
      }
      
      [data-room-type="hallway"] {
        fill: rgba(var(--text-secondary), 0.4);
      }
    }
  }
  
  // SVG container styles when dashboard is locked (view mode)
  .dashboard-locked .floorplan-svg-container {
    ::ng-deep svg {
      [id^="room-"] {
        // Make rooms more visibly clickable in view mode
        &:hover {
          fill: rgba(var(--primary-color-rgb), 0.3);
          stroke: var(--primary-color);
          stroke-width: 2px;
          filter: drop-shadow(0 0 3px rgba(var(--primary-color-rgb), 0.5));
        }
      }
    }
  }
  
  // Room creation indicator
  .room-creation-indicator {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    pointer-events: none;
    z-index: 10;
    
    .indicator-message {
      background-color: var(--background-secondary);
      color: var(--text-color);
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 6px;
      box-shadow: 0 2px 8px var(--box-shadow);
      
      i {
        color: var(--primary-color);
      }
    }
  }
  
  // Error notification styles
  .error-notification {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--background-secondary);
    padding: 20px;
    border-radius: 8px;
    color: var(--text-color);
    text-align: center;
    max-width: 80%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    box-shadow: 0 2px 8px var(--box-shadow);
    
    .reload-button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      
      &:hover {
        background-color: var(--primary-hover);
      }
    }
  }
  
  // View mode indicator
  .view-mode-indicator {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    pointer-events: none;
    z-index: 5;
    
    background-color: rgba(var(--background-secondary), 0.7);
    color: var(--text-color);
    padding: 8px;
    border-radius: 4px;
    font-size: 14px;
    margin: 0 auto;
    width: fit-content;
    box-shadow: 0 2px 4px var(--box-shadow);
    
    i {
      margin-right: 6px;
      color: var(--primary-color);
    }
  }
  
  // HA status indicator for when Home Assistant is offline
  .ha-status-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--background-secondary);
    color: var(--text-color);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px var(--box-shadow);
    
    &.offline {
      background-color: var(--error-color);
      color: white;
      
      i {
        margin-right: 6px;
      }
    }
  }
  
  // Marker styles for interactive points on the floorplan
  .floorplan-markers {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    
    .floorplan-marker {
      position: absolute;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: var(--background-secondary);
      color: var(--text-color);
      display: flex;
      align-items: center;
      justify-content: center;
      transform: translate(-50%, -50%);
      cursor: pointer;
      pointer-events: auto;
      box-shadow: 0 2px 4px var(--box-shadow);
      
      &:hover {
        transform: translate(-50%, -50%) scale(1.2);
      }
      
      i {
        font-size: 14px;
      }
      
      // Specific marker type styles using theme variables
      &.light {
        background-color: var(--primary-color);
        color: white;
      }
      
      &.sensor {
        background-color: var(--primary-color);
        color: white;
      }
      
      &.door {
        background-color: var(--primary-color);
        color: white;
      }
      
      &.window {
        background-color: var(--success-color);
        color: white;
      }
      
      &.device {
        background-color: var(--error-color);
        color: white;
      }
      
      &.custom {
        background-color: var(--primary-color);
        color: white;
      }
      
      // Style for Home Assistant integrated devices
      &.ha-device {
        border: 2px solid white;
      }
      
      // State indicator for entities with state
      .marker-state {
        position: absolute;
        bottom: -18px;
        left: 50%;
        transform: translateX(-50%);
        background-color: var(--background-secondary);
        color: var(--text-color);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
        white-space: nowrap;
        box-shadow: 0 2px 4px var(--box-shadow);
      }
    }
  }

/* Room detail modal styles */
.room-detail-modal-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
}

// View mode indicator when dashboard is locked
.view-mode-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: var(--primary-color);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 5px var(--box-shadow);
  z-index: 5;
  animation: fadeIn 0.3s ease;
  
  i {
    font-size: 14px;
  }
}

// Animations
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse {
  from {
    fill-opacity: 0.3;
    stroke-opacity: 0.7;
  }
  to {
    fill-opacity: 0.5;
    stroke-opacity: 1;
  }
}

// Add styles for the fullscreen modal
::ng-deep .fullscreen-modal {
  .mat-dialog-container {
    padding: 0;
    border-radius: 0;
    overflow: hidden;
    background-color: var(--background-color);
  }
}

// Multi-select mode indicator
.multi-select-indicator {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  pointer-events: none;
  z-index: 10;
  
  background-color: rgba(var(--primary-color-rgb), 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  margin: 0 auto;
  width: fit-content;
  box-shadow: 0 2px 8px var(--box-shadow);
  
  i {
    margin-right: 8px;
  }
}

// Rooms Display Section
.rooms-display {
  border-top: 1px solid var(--border-color);
  background-color: var(--background-color);

  .rooms-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--background-secondary);
    border-bottom: 1px solid var(--border-color);

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color);
      display: flex;
      align-items: center;
      gap: 6px;

      i {
        color: var(--primary-color);
        font-size: 12px;
      }
    }

    .toggle-rooms-btn {
      background: none;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--background-secondary);
        color: var(--text-color);
      }

      i {
        font-size: 12px;
      }
    }
  }

  .rooms-grid {
    max-height: 120px;
    overflow-y: auto;
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;

    &.expanded {
      max-height: 200px;
    }

    .room-card {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px;
      background-color: var(--background-secondary);
      border: 1px solid var(--border-color);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--background-hover);
        border-color: var(--primary-color);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px var(--box-shadow);
      }

      &.selected {
        border-color: var(--primary-color);
        background-color: rgba(var(--primary-color-rgb), 0.1);
      }

      &.highlighted {
        border-color: var(--success-color);
        background-color: rgba(var(--success-color), 0.1);
        box-shadow: 0 0 0 2px rgba(var(--success-color), 0.2);
      }

      .room-icon {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        flex-shrink: 0;
      }

      .room-info {
        flex: 1;
        min-width: 0;

        .room-name {
          font-weight: 500;
          font-size: 13px;
          color: var(--text-color);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .room-type {
          font-size: 11px;
          color: var(--text-secondary);
          text-transform: capitalize;
        }

        .room-devices {
          font-size: 10px;
          color: var(--text-secondary);
          display: flex;
          align-items: center;
          gap: 4px;
          margin-top: 2px;

          i {
            font-size: 9px;
          }
        }
      }

      .room-actions {
        display: flex;
        gap: 4px;
        opacity: 0;
        transition: opacity 0.2s ease;

        .edit-room-btn {
          background: none;
          border: none;
          color: var(--text-secondary);
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          font-size: 12px;
          transition: all 0.2s ease;

          &:hover {
            background-color: var(--primary-color);
            color: white;
          }
        }
      }

      &:hover .room-actions {
        opacity: 1;
      }
    }

    .empty-rooms {
      grid-column: 1 / -1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 20px;
      color: var(--text-secondary);
      text-align: center;

      i {
        font-size: 24px;
        color: var(--text-secondary);
        opacity: 0.5;
      }

      span {
        font-size: 14px;
      }

      .create-first-room-btn {
        background-color: var(--primary-color);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 6px;
        transition: all 0.2s ease;

        &:hover {
          background-color: var(--primary-hover);
          transform: translateY(-1px);
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .rooms-display {
    .rooms-grid {
      grid-template-columns: 1fr;
      max-height: 100px;

      &.expanded {
        max-height: 150px;
      }

      .room-card {
        .room-info {
          .room-name {
            font-size: 12px;
          }

          .room-type {
            font-size: 10px;
          }
        }
      }
    }
  }
}