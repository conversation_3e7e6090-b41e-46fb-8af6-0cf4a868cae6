import { Component, Input, OnInit, OnDestroy, AfterViewInit, ElementRef, ViewChild, OnChanges, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { FloorplanTile, FloorplanMarker, Room, HADeviceMapping, Point } from '../../models/dashboard.models';
import { LoadingSpinnerComponent } from '../../shared/components/loading-spinner/loading-spinner.component';
import { EmptyStateComponent } from '../../shared/components/empty-state/empty-state.component';
import { FloorplanPlaceholderComponent } from './floorplan-placeholder.component';
import { SafeHtmlPipe } from '../../shared/pipes/safe-html.pipe';
import { SvgHelperService, SvgRoomElement } from '../../services/svg-helper.service';
import { RoomDetailComponent } from '../../shared/components/room-detail/room-detail.component';
import { HomeAssistantService, HAEntity } from '../../services/home-assistant.service';
import { catchError, of, Subscription, take } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { MatDialogModule, MatDialog, MatDialogConfig, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { RoomDetailsDialogComponent } from '../../shared/components/room-details-dialog/room-details-dialog.component';
import { RoomCreationDialogComponent } from '../../shared/components/room-creation-dialog/room-creation-dialog.component';
import { FloorplanViewerComponent } from './components/floorplan-viewer/floorplan-viewer.component';
import { MarkerOverlayComponent } from './components/marker-overlay/marker-overlay.component';

@Component({
  selector: 'app-floorplan-tile',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    EmptyStateComponent,
    RoomDetailComponent,
    MatDialogModule,
    MatButtonModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    FloorplanViewerComponent,
    MarkerOverlayComponent
  ],
  templateUrl: './floorplan-tile.component.html',
  styleUrls: ['./floorplan-tile.component.scss']
})
export class FloorplanTileComponent implements OnInit, OnDestroy, AfterViewInit, OnChanges {
  @Input() tile!: FloorplanTile;
  @Input() isEditingEnabled = false;
  @Output() saveChanges = new EventEmitter<FloorplanTile>();
  
  // Add backwards compatibility for existing code
  @Input() set item(value: FloorplanTile) {
    this.tile = value;
  }
  
  @Input() set dashboardLocked(value: boolean) {
    this.isEditingEnabled = !value;
  }

  @ViewChild('svgContainer') svgContainer?: ElementRef;
  
  // Default image path (from root since file is in public folder)
  floorplanImage = '/floorplan.svg';
  markers: FloorplanMarker[] = [];
  
  // SVG support
  isSvgFile = false;
  svgContent = '';
  svgDimensions = { width: 0, height: 0 };
  
  // Loading and error states
  isLoading = true;
  hasError = false;
  errorMessage = '';
  usePlaceholder = false;
  
  // Room/area highlighting functionality
  highlightedAreaId: string | null = null;
  
  // Room detail view
  selectedRoom: Room | undefined;
  showRoomDetail = false;
  
  // Home Assistant integration
  haConnected = false;
  haSubscriptions: Subscription = new Subscription();
  haEntities: HAEntity[] = [];
  generatedMarkers: FloorplanMarker[] = [];
  loadingEntityIds: string[] = [];
  errorEntityIds: string[] = [];
  
  // Timeout reference for cleanup
  private loadingTimeout: ReturnType<typeof setTimeout> | null = null;
  private refreshTimeout: ReturnType<typeof setTimeout> | null = null;
  
  // New custom room creation properties
  isCreatingRoom = false;
  currentRoomPoints: Point[] = [];
  currentRoomId = '';
  currentRoomName = '';
  currentRoomType: Room['type'] = 'room';
  currentRoomDescription = '';
  temporaryPolygonId = 'temp-polygon';
  polygonInProgressId = 'polygon-in-progress';
  finalPolygonId = 'custom-room-';
  
  // Multi-room selection
  selectedRooms: Map<string, Room> = new Map();
  isMultiSelectMode = false;

  // Rooms display
  roomsExpanded = true;
  
  // Room colors based on type
  roomColors: Record<string, string> = {
    'room': 'rgba(var(--primary-color-rgb), 0.4)',
    'bathroom': 'rgba(77, 195, 236, 0.4)',
    'bedroom': 'rgba(139, 195, 74, 0.4)',
    'kitchen': 'rgba(255, 193, 7, 0.4)',
    'living': 'rgba(255, 152, 0, 0.4)',
    'hallway': 'rgba(189, 189, 189, 0.4)',
    'other': 'rgba(156, 39, 176, 0.4)'
  };
  
  // Room-specific CSS custom properties
  roomStyles: Record<string, string> = {}; // Will be populated for each room
  
  constructor(
    private http: HttpClient,
    private svgHelper: SvgHelperService,
    private homeAssistantService: HomeAssistantService,
    private dialog: MatDialog
  ) {}

  /**
   * Get all markers (manual + generated from HA)
   */
  get allMarkers(): FloorplanMarker[] {
    return [
      ...(this.markers || []),
      ...(this.generatedMarkers || [])
    ];
  }

  /**
   * Check if there are any rooms
   */
  get hasRooms(): boolean | undefined {
    return this.tile.rooms && Object.keys(this.tile.rooms).length > 0;
  }

  /**
   * Get room count
   */
  get roomCount(): number {
    return this.tile.rooms ? Object.keys(this.tile.rooms).length : 0;
  }

  /**
   * Get rooms as an array
   */
  get roomsList(): Room[] {
    if (!this.tile.rooms) return [];
    return Object.values(this.tile.rooms);
  }
  
  /**
   * Get possible floorplan paths in order of priority
   */
  private getPossibleFloorplanPaths(): string[] {
    const possiblePaths: string[] = [];
    
    // Current path
    possiblePaths.push(this.floorplanImage);
    
    // Try root path (public folder) if not already using it
    if (this.floorplanImage !== '/floorplan.svg') {
      possiblePaths.push('/floorplan.svg');
    }
    
    // Legacy paths for backward compatibility
    if (!this.floorplanImage.includes('/assets/tiles/floorplan/')) {
      possiblePaths.push('/assets/tiles/floorplan/floorplan.svg');
    }
    
    if (!this.floorplanImage.startsWith('/assets/images/')) {
      possiblePaths.push('/assets/images/floorplan.svg');
    }
    
    if (!this.floorplanImage.startsWith('/images/')) {
      possiblePaths.push('/images/floorplan.svg');
    }
    
    return possiblePaths;
  }
  
  ngOnInit(): void {
    console.log('Floorplan tile initialized with item:', this.tile);
    
    // Validate input data
    this.validateInputData();
    
    // Initialize initial path from item
    if (this.tile.imagePath) {
      this.floorplanImage = this.tile.imagePath;
    }
    
    if (this.tile.markers && this.tile.markers.length > 0) {
      // Make a deep copy of markers to avoid mutation issues
      this.markers = JSON.parse(JSON.stringify(this.tile.markers));
    }
    
    // Check if it's an SVG file
    this.isSvgFile = this.floorplanImage.toLowerCase().endsWith('.svg');
    
    // Subscribe to Home Assistant if configured
    this.initializeHomeAssistant();
    
    // Get possible paths for loading the floorplan
    const possiblePaths = this.getPossibleFloorplanPaths();
    console.log('Initial load: Trying these paths in order:', possiblePaths);
    
    // Give a small delay before starting load attempts
    this.loadingTimeout = setTimeout(() => {
      // Try each path in sequence until one works, using the same mechanism as reload
      this.tryLoadingFromPaths(possiblePaths);
    }, 800);
    
    // Initialize room styles with colors
    this.initializeRoomStyles();
  }

  ngAfterViewInit(): void {
    // If we have an SVG and it's already loaded, setup interactive elements
    if (this.isSvgFile && this.svgContent && this.svgContainer) {
      this.setupSvgInteractivity();
    }
  }
  
  ngOnDestroy(): void {
    // Clean up timeouts to prevent memory leaks
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }
    
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
    }
    
    // Clean up Home Assistant subscriptions
    this.haSubscriptions.unsubscribe();
  }
  
  /**
   * Validate the input data to ensure it's properly formed
   */
  private validateInputData(): void {
    if (!this.tile) {
      console.error('Floorplan tile: No item provided');
      this.tile = {
        id: 'placeholder',
        type: 'floorplan',
        imagePath: '/floorplan.svg',
        x: 0,
        y: 0,
        cols: 1,
        rows: 1
      };
    }
    
    // Ensure the item has the correct type
    if (this.tile.type !== 'floorplan') {
      console.warn('Floorplan tile: Item type is not "floorplan"');
      this.tile.type = 'floorplan';
    }
  }
  
  /**
   * Load SVG content from the provided path using the SVG helper service
   */
  private loadSvgContent(): void {
    this.svgHelper.loadSvgFromUrl(this.floorplanImage)
      .subscribe(svgContent => {
        if (svgContent) {
          this.svgContent = svgContent;
          this.hasError = false;
          this.usePlaceholder = false;
          
          // Setup interactivity after the SVG is loaded and rendered
          setTimeout(() => {
            this.setupSvgInteractivity();
          }, 100);
        } else {
          this.hasError = true;
          this.errorMessage = 'Failed to load SVG floorplan. Using placeholder instead.';
          this.usePlaceholder = true;
        }
      });
  }
  
  /**
   * Set up interactive elements within the SVG
   */
  private setupSvgInteractivity(): void {
    if (!this.svgContainer || !this.tile.interactive) return;
    
    const containerId = this.tile.id + '-svg-container';
    
    // Add ID to the container for event handling
    const containerElement = this.svgContainer.nativeElement;
    containerElement.id = containerId;
    
    // Extract all room IDs from the SVG
    const roomIds = this.svgHelper.extractRoomIds(this.svgContent);
    
    // Add any custom rooms that might not be in the SVG yet
    if (this.tile.rooms) {
      Object.keys(this.tile.rooms).forEach(roomId => {
        const room = this.tile.rooms![roomId];
        if (room.isCustomShape && room.customPolygon) {
          const fullRoomId = `room-${roomId}`;
          
          // Check if the room is already in the SVG
          if (!roomIds.includes(fullRoomId)) {
            // Add it to the SVG
            this.addCustomRoomToSvg(fullRoomId, room.customPolygon);
            
            // Add to our list of room IDs
            roomIds.push(fullRoomId);
          }
        }
      });
    }
    
    // Enhanced room element interactivity
    setTimeout(() => {
      // Select all room elements in the SVG
      this.svgContainer?.nativeElement.querySelectorAll('[id^="room-"]').forEach((roomElement: SVGElement) => {
        // Setup click handler for each room
        roomElement.addEventListener('click', (event) => {
          // Don't trigger room clicks when in room creation mode
          if (this.isCreatingRoom) return;
          
          const roomId = roomElement.id.replace('room-', '');
          if (!this.isEditingEnabled) {
            // When not in edit mode, just show the room detail
            this.onRoomClick(roomId);
          } else {
            // In edit mode, handle click for editing
            this.handleDetectedRoomClick(event as MouseEvent, roomId);
          }
        });
        
        // Add hover effect
        roomElement.addEventListener('mouseenter', () => {
          if (this.isEditingEnabled) {
            roomElement.classList.add('highlighted');
          }
        });
        
        roomElement.addEventListener('mouseleave', () => {
          roomElement.classList.remove('highlighted');
        });
      });
      
      // If editing is disabled, add tooltips/indicators for better UX
      if (!this.isEditingEnabled) {
        this.addViewModeIndicators(containerId, roomIds);
      }
    }, 500);
  }
  
  /**
   * Add tooltips/indicators to rooms when in view mode
   */
  private addViewModeIndicators(containerId: string, roomIds: string[]): void {
    const container = document.getElementById(containerId);
    if (!container) return;

    const svgElement = container.querySelector('svg');
    if (!svgElement) return;

    roomIds.forEach(id => {
      const element = svgElement.getElementById(id);
      if (element) {
        // Add title with instructions for accessibility/tooltip
        const roomName = this.getRoomName(id.replace('room-', ''));
        const titleElement = document.createElementNS('http://www.w3.org/2000/svg', 'title');
        titleElement.textContent = `Click to view details of ${roomName}`;
        element.appendChild(titleElement);
        
        // Visual indicator could also be added here if desired
      }
    });
  }
  
  /**
   * Handle room click events
   */
  onRoomClick(roomId: string, event?: MouseEvent): void {
    // If Ctrl key is pressed, activate multi-select mode
    if (event && (event.ctrlKey || event.metaKey)) {
      this.isMultiSelectMode = true;
    }
    
    // Extract the actual room ID (without the "room-" prefix)
    const actualRoomId = roomId.replace('room-', '');
    
    if (this.isMultiSelectMode) {
      // Handle multi-select mode
      if (this.selectedRooms.has(actualRoomId)) {
        // Deselect room
        this.selectedRooms.delete(actualRoomId);
        this.svgContent = this.svgHelper.addClassToElement(this.svgContent, roomId, 'selected');
      } else {
        // Select room
        if (this.tile.rooms && this.tile.rooms[actualRoomId]) {
          this.selectedRooms.set(actualRoomId, this.tile.rooms[actualRoomId]);
          this.svgContent = this.svgHelper.addClassToElement(this.svgContent, roomId, 'selected');
        } else {
          // Create a placeholder room
          const placeholderRoom: Room = {
            id: actualRoomId,
            name: this.getRoomName(actualRoomId),
            imagePath: `/assets/images/rooms/${actualRoomId}.png`,
            type: 'room'
          };
          this.selectedRooms.set(actualRoomId, placeholderRoom);
          this.svgContent = this.svgHelper.addClassToElement(this.svgContent, roomId, 'selected');
        }
      }
      
      // Apply different colors to selected rooms
      this.applySelectionColors();
    } else {
      // Regular single-selection mode
      // Toggle highlighting
      if (this.highlightedAreaId === roomId) {
        this.highlightedAreaId = null;
        this.svgContent = this.svgHelper.addClassToElement(this.svgContent, roomId, 'room-active');
      } else {
        // Remove highlight from previously highlighted room
        if (this.highlightedAreaId) {
          this.svgContent = this.svgHelper.addClassToElement(this.svgContent, this.highlightedAreaId, 'room-active');
        }
        
        this.highlightedAreaId = roomId;
        this.svgContent = this.svgHelper.highlightElement(this.svgContent, roomId);
      }
      
      // Check if we have detailed room information
      if (this.tile.rooms && this.tile.rooms[actualRoomId]) {
        this.selectedRoom = this.tile.rooms[actualRoomId];
        this.showRoomDetail = true;
      } else {
        // If we don't have detailed room info, create a placeholder with generated image path
        // Assuming that room PNGs follow a naming convention
        this.selectedRoom = {
          id: actualRoomId,
          name: this.getRoomName(actualRoomId),
          imagePath: `/assets/images/rooms/${actualRoomId}.png`,
          type: 'room'
        };
        this.showRoomDetail = true;
      }
    }
  }
  
  /**
   * Generate a readable room name from an ID
   */
  private getRoomName(roomId: string): string {
    // Convert kebab-case or snake_case to title case
    return roomId
      .replace(/[-_]/g, ' ')
      .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase());
  }
  
  /**
   * Close the room detail modal
   */
  closeRoomDetail(): void {
    this.showRoomDetail = false;
    this.selectedRoom = undefined;
    
    // Remove highlighting from the selected room
    if (this.highlightedAreaId) {
      this.svgContent = this.svgHelper.addClassToElement(this.svgContent, this.highlightedAreaId, 'room-active');
      this.highlightedAreaId = null;
    }
  }
  
  /**
   * Check if the floorplan image exists
   */
  private checkImageExists(): void {
    const img = new Image();
    img.onload = () => {
      this.hasError = false;
      this.usePlaceholder = false;
    };
    img.onerror = () => {
      this.hasError = true;
      this.errorMessage = 'Failed to load floorplan image. Using placeholder instead.';
      this.usePlaceholder = true;
      
      // Log the error for debugging
      console.error(`Failed to load floorplan image from path: ${this.floorplanImage}`);
    };
    
    // Set crossorigin attribute to handle CORS issues with Firebase Storage
    img.crossOrigin = 'anonymous';
    
    // Add cache-busting parameter to prevent browser caching
    const cacheBuster = `?t=${new Date().getTime()}`;
    img.src = this.floorplanImage + cacheBuster;
  }
  
  /**
   * Handle marker click events
   */
  onMarkerClick(marker: FloorplanMarker): void {
    console.log('Marker clicked:', marker);
    // Implement marker click actions here based on marker.actions
  }
  
  /**
   * Reload the image when there's an error
   */
  reloadImage(): void {
    this.isLoading = true;
    this.hasError = false;
    this.usePlaceholder = false;
    
    // Log for debugging
    console.log(`Attempting to reload floorplan from path: ${this.floorplanImage}`);
    
    // Get possible paths for loading the floorplan
    const possiblePaths = this.getPossibleFloorplanPaths();
    console.log('Reload: Trying these paths in order:', possiblePaths);
    
    // Try each path in sequence until one works
    this.tryLoadingFromPaths(possiblePaths);
  }
  
  /**
   * Try loading the SVG from a sequence of paths
   */
  private tryLoadingFromPaths(paths: string[], index = 0): void {
    if (index >= paths.length) {
      // All paths failed, show placeholder
      this.hasError = true;
      this.errorMessage = 'Failed to load floorplan from any available path. Using placeholder instead.';
      this.usePlaceholder = true;
      this.isLoading = false;
      return;
    }
    
    // Try current path
    this.floorplanImage = paths[index];
    console.log(`Trying path ${index + 1}/${paths.length}: ${this.floorplanImage}`);
    
    // Update SVG flag based on current path
    this.isSvgFile = this.floorplanImage.toLowerCase().endsWith('.svg');
    
    // Create a new image object to test the path
    const img = new Image();
    img.onload = () => {
      console.log(`Successfully loaded from path: ${this.floorplanImage}`);
      this.continueReloading();
    };
    img.onerror = () => {
      console.log(`Failed to load from path: ${this.floorplanImage}`);
      // Try the next path
      this.tryLoadingFromPaths(paths, index + 1);
    };
    
    // Set crossorigin attribute to handle CORS issues
    img.crossOrigin = 'anonymous';
    
    // Add cache-busting parameter to prevent browser caching
    const cacheBuster = `?t=${new Date().getTime()}`;
    img.src = this.floorplanImage + cacheBuster;
  }
  
  /**
   * Continue the reload process after any path adjustments
   */
  private continueReloading(): void {
    this.hasError = false;
    this.usePlaceholder = false;
    
    // Small delay to prevent loading flicker
    this.loadingTimeout = setTimeout(() => {
      if (this.isSvgFile) {
        console.log(`Loading SVG content from: ${this.floorplanImage}`);
        this.loadSvgContent();
      } else {
        console.log(`Loading regular image from: ${this.floorplanImage}`);
        this.checkImageExists();
      }
      this.isLoading = false;
    }, 500);
  }

  ngOnChanges(changes: any): void {
    // If room data changes or visibility changes in the RoomDetailComponent
    if (changes.selectedRoom || changes.showRoomDetail) {
      if (this.selectedRoom && this.showRoomDetail) {
        this.checkImageExists();
      }
    }
    
    // If dashboard locked state changes, update interactivity
    if (changes.isEditingEnabled && this.svgContainer && this.tile.interactive) {
      const containerId = this.tile.id + '-svg-container';
      const roomIds = this.svgHelper.extractRoomIds(this.svgContent);
      
      if (roomIds.length > 0) {
        if (this.isEditingEnabled) {
          // Add tooltips/indicators when dashboard becomes locked
          this.addViewModeIndicators(containerId, roomIds);
        }
      }
    }
  }

  /**
   * Initialize Home Assistant integration
   */
  private initializeHomeAssistant(): void {
    // Check if Home Assistant service is configured and floorplan is set up for HA
    if (!this.homeAssistantService.isConfigured() || !this.tile.haDevices) {
      // No HA configuration or no device mappings
      return;
    }
    
    // Monitor connection status
    const connectionSub = this.homeAssistantService.connectionStatus$.subscribe(connected => {
      this.haConnected = connected;
      
      if (connected) {
        // If we just connected, fetch all entities
        this.fetchHomeAssistantDevices();
      }
    });
    
    // Get all entity states
    const entitiesSub = this.homeAssistantService.entityStates$.subscribe(entities => {
      if (entities && entities.length > 0) {
        this.haEntities = entities;
        this.updateDeviceMarkers();
      }
    });
    
    // Add subscriptions to be cleaned up on destroy
    this.haSubscriptions.add(connectionSub);
    this.haSubscriptions.add(entitiesSub);
    
    // Schedule regular refresh if specified
    if (this.tile.refreshInterval && this.tile.refreshInterval > 0) {
      this.scheduleDeviceRefresh();
    }
  }
  
  /**
   * Fetch Home Assistant devices and update markers
   */
  private fetchHomeAssistantDevices(): void {
    this.homeAssistantService.entityStates$.pipe(take(1)).subscribe((entities: HAEntity[]) => {
      if (entities && entities.length > 0) {
        this.haEntities = entities;
        this.updateDeviceMarkers();
      }
    });
  }
  
  /**
   * Schedule regular refresh of device states
   */
  private scheduleDeviceRefresh(): void {
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
    }
    
    const interval = this.tile.refreshInterval || 60000; // Default to 1 minute
    
    this.refreshTimeout = setTimeout(() => {
      this.fetchHomeAssistantDevices();
      this.scheduleDeviceRefresh(); // Schedule next refresh
    }, interval);
  }
  
  /**
   * Update device markers based on Home Assistant entity states
   */
  private updateDeviceMarkers(): void {
    if (!this.tile.haDevices || !this.haEntities || this.haEntities.length === 0) {
      return;
    }
    
    // Clear previous generated markers
    this.generatedMarkers = [];
    
    // Process each device mapping
    this.tile.haDevices.forEach(deviceMapping => {
      // Find the entity in our entities list
      const entity = this.haEntities.find(e => e.entity_id === deviceMapping.entityId);
      
      if (entity) {
        // Create or update marker for this entity
        const marker = this.createMarkerFromEntity(entity, deviceMapping);
        if (marker) {
          this.generatedMarkers.push(marker);
        }
      }
    });
    
    // Update markers displayed on the floorplan
    this.markers = [
      ...(this.tile.markers || []), // Original manually defined markers
      ...this.generatedMarkers     // Dynamically generated markers from HA
    ];
  }
  
  /**
   * Create a marker from a Home Assistant entity
   */
  private createMarkerFromEntity(entity: HAEntity, mapping: HADeviceMapping): FloorplanMarker | null {
    if (!entity || !mapping) return null;
    
    // Determine marker type from entity domain or mapping
    const domain = entity.entity_id.split('.')[0];
    const markerType = mapping.markerType || this.getMarkerTypeFromDomain(domain);
    
    // Determine position (required for marker)
    if (!mapping.position) {
      console.warn(`No position specified for entity ${entity.entity_id}`);
      return null;
    }
    
    // Create marker
    return {
      id: `ha-${entity.entity_id}`,
      x: mapping.position.x,
      y: mapping.position.y,
      type: markerType as any,
      label: mapping.showLabel ? entity.attributes['friendly_name'] || entity.entity_id : undefined,
      entityId: entity.entity_id,
      showState: mapping.showState || false,
      stateAttribute: mapping.stateAttribute,
      icon: mapping.icon || this.getIconForEntity(entity),
      color: mapping.color || this.getColorForEntityState(entity)
    };
  }
  
  /**
   * Get marker type based on entity domain
   */
  private getMarkerTypeFromDomain(domain: string): string {
    switch (domain) {
      case 'light': return 'light';
      case 'switch': return 'device';
      case 'binary_sensor': return 'sensor';
      case 'sensor': return 'sensor';
      case 'climate': return 'device';
      case 'lock': return 'door';
      case 'cover': return 'window';
      default: return 'custom';
    }
  }
  
  /**
   * Get appropriate icon for an entity
   */
  private getIconForEntity(entity: HAEntity): string {
    // Use a custom icon if specified in attributes
    if (entity.attributes['icon']) {
      return `fas fa-${entity.attributes['icon'].replace('mdi:', '')}`;
    }
    
    // Default icons based on domain
    const domain = entity.entity_id.split('.')[0];
    switch (domain) {
      case 'light': return 'fas fa-lightbulb';
      case 'switch': return 'fas fa-power-off';
      case 'binary_sensor': 
        if (entity.entity_id.includes('motion')) return 'fas fa-running';
        if (entity.entity_id.includes('door')) return 'fas fa-door-open';
        if (entity.entity_id.includes('window')) return 'fas fa-window-maximize';
        return 'fas fa-dot-circle';
      case 'sensor': return 'fas fa-thermometer-half';
      case 'climate': return 'fas fa-temperature-high';
      case 'lock': return entity.state === 'locked' ? 'fas fa-lock' : 'fas fa-lock-open';
      case 'cover': return entity.state === 'open' ? 'fas fa-window-maximize' : 'fas fa-window-close';
      default: return 'fas fa-cube';
    }
  }
  
  /**
   * Get color based on entity state
   */
  private getColorForEntityState(entity: HAEntity): string {
    const domain = entity.entity_id.split('.')[0];
    const state = entity.state;
    
    // Domain-specific colors
    switch (domain) {
      case 'light':
      case 'switch':
        return state === 'on' ? '#ffcc00' : '#666666';
      
      case 'binary_sensor':
        return state === 'on' ? '#ff0000' : '#666666';
      
      case 'lock':
        return state === 'locked' ? '#00cc66' : '#ff0000';
      
      case 'cover':
        return state === 'open' ? '#00ccff' : '#666666';
      
      default:
        // Default colors for other entity types
        if (state === 'on' || state === 'open' || state === 'unlocked') {
          return '#00cc66';
        } else if (state === 'off' || state === 'closed' || state === 'locked') {
          return '#666666';
        }
        return '#3366ff';
    }
  }
  
  /**
   * Handle device marker click based on the marker type and entity ID
   */
  onDeviceMarkerClick(marker: FloorplanMarker): void {
    if (!marker.entityId) return;
    
    // Find the entity
    const entity = this.haEntities.find(e => e.entity_id === marker.entityId);
    if (!entity) return;
    
    // Determine the entity domain
    const domain = entity.entity_id.split('.')[0];
    
    // Handle different types of entities
    switch (domain) {
      case 'light':
      case 'switch':
      case 'automation':
        this.toggleEntity(entity.entity_id);
        break;
      case 'cover':
        this.controlCover(entity.entity_id, entity.state);
        break;
      case 'lock':
        this.controlLock(entity.entity_id, entity.state);
        break;
      default:
        // For sensors and other read-only entities, show detailed info
        this.showEntityDetails(entity);
        break;
    }
  }
  
  /**
   * Toggles an entity's state (on/off)
   * @param entityId The entity to toggle
   */
  private toggleEntity(entityId: string): void {
    const domain = entityId.split('.')[0];
    
    this.homeAssistantService.toggleEntity(entityId)
      .pipe(take(1))
      .subscribe({
        next: () => {
          // Refresh entity status
          setTimeout(() => this.fetchHomeAssistantDevices(), 500);
        },
        error: (err) => {
          console.error('Error toggling entity state', err);
        }
      });
  }
  
  /**
   * Controls a cover entity (open/close/stop)
   * @param entityId The cover entity ID
   * @param currentState The current state of the cover
   */
  private controlCover(entityId: string, currentState: string): void {
    let service = '';
    
    if (currentState === 'closed') {
      service = 'open_cover';
    } else if (currentState === 'open') {
      service = 'close_cover';
    } else {
      service = 'stop_cover';
    }
    
    this.homeAssistantService.callService({
      domain: 'cover',
      service: service,
      target: {
        entity_id: entityId
      }
    })
    .pipe(take(1))
    .subscribe({
      next: () => {
        // Refresh entity status
        setTimeout(() => this.fetchHomeAssistantDevices(), 500);
      },
      error: (err) => {
        console.error('Error controlling cover', err);
      }
    });
  }
  
  /**
   * Controls a lock entity (lock/unlock)
   * @param entityId The lock entity ID
   * @param currentState The current state of the lock
   */
  private controlLock(entityId: string, currentState: string): void {
    let service = '';
    
    if (currentState === 'locked') {
      service = 'unlock';
    } else {
      service = 'lock';
    }
    
    this.homeAssistantService.callService({
      domain: 'lock',
      service: service,
      target: {
        entity_id: entityId
      }
    })
    .pipe(take(1))
    .subscribe({
      next: () => {
        // Refresh entity status
        setTimeout(() => this.fetchHomeAssistantDevices(), 500);
      },
      error: (err) => {
        console.error('Error controlling lock', err);
      }
    });
  }
  
  /**
   * Shows detailed information about an entity
   * @param entity The entity to show details for
   */
  private showEntityDetails(entity: HAEntity): void {
    // You could implement a modal or tooltip here
    console.log('Entity details:', entity);
    
    // For now, just log to console, but we could add a proper UI for this
    // this.dialog.open(EntityDetailsComponent, {
    //   data: { entity }
    // });
  }

  /**
   * Get entity state for display
   */
  getEntityState(entityId: string): string {
    if (!entityId || !this.haEntities || this.haEntities.length === 0) {
      return '';
    }
    
    const entity = this.haEntities.find(e => e.entity_id === entityId);
    if (!entity) {
      return '';
    }
    
    // Return formatted state based on domain
    const domain = entityId.split('.')[0];
    
    switch (domain) {
      case 'sensor':
        // For sensors, include the unit of measurement if available
        if (entity.attributes['unit_of_measurement']) {
          return `${entity.state}${entity.attributes['unit_of_measurement']}`;
        }
        return entity.state;
        
      case 'binary_sensor':
      case 'light':
      case 'switch':
        // For binary devices, return a more readable state
        return entity.state === 'on' ? 'ON' : 'OFF';
        
      case 'lock':
        return entity.state === 'locked' ? 'LOCKED' : 'UNLOCKED';
        
      case 'cover':
        return entity.state.toUpperCase();
        
      default:
        return entity.state;
    }
  }

  /**
   * Toggle room creation mode 
   */
  toggleRoomCreationMode(): void {
    if (!this.isEditingEnabled) {
      console.log('Cannot edit rooms when dashboard is locked');
      return;
    }
    
    // Open the room creation modal
    this.openRoomCreationModal();
  }
  
  /**
   * Open the room creation modal
   */
  private openRoomCreationModal(): void {
    // First check if we have SVG content
    if (!this.svgContent) {
      this.showInstructions('No floorplan loaded. Please load a floorplan first.');
      return;
    }
    
    // Calculate existing room IDs to avoid duplicates
    const existingRoomIds = this.tile.rooms ? Object.keys(this.tile.rooms) : [];
    
    // Open the creation modal
    const dialogRef = this.dialog.open(RoomCreationDialogComponent, {
      width: '100vw',
      height: '100vh',
      maxWidth: '100vw',
      maxHeight: '100vh',
      panelClass: ['fullscreen-modal', 'no-padding'],
      data: {
        svgContent: this.svgContent
      }
    });
    
    // Handle dialog close
    dialogRef.afterClosed().subscribe(result => {
      if (result && result.points && result.points.length >= 3) {
        // Open the room details dialog to get room information
        this.openRoomDetailsDialog(result.points, existingRoomIds);
      }
    });
  }
  
  /**
   * Open dialog to get room details
   */
  private openRoomDetailsDialog(points: Point[], existingRoomIds: string[]): void {
    const dialogRef = this.dialog.open(RoomDetailsDialogComponent, {
      width: '500px',
      data: {
        points,
        existingRoomIds
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.saveNewRoom(result);
      }
    });
  }
  
  /**
   * Save a new room to the floorplan configuration
   */
  private saveNewRoom(room: Room): void {
    // Add generated ID prefix to make it work with the existing system
    const fullRoomId = `room-${room.id}`;
    
    // Ensure the rooms object exists
    if (!this.tile.rooms) {
      this.tile.rooms = {};
    }
    
    // Sanitize room data for Firestore
    const sanitizedRoom = this.sanitizeForFirestore(room);
    
    // Add the new room
    this.tile.rooms[room.id] = sanitizedRoom;
    
    // Create a permanent SVG element for this room
    this.addCustomRoomToSvg(fullRoomId, sanitizedRoom.customPolygon!);
    
    // Show success message
    this.showInstructions(`Room "${sanitizedRoom.name}" created successfully!`);
    
    // Re-setup interactivity to include the new room
    this.setupSvgInteractivity();
    
    // Save the changes to the tile
    this.saveFloorplanChanges();
  }
  
  /**
   * Add a custom room polygon to the SVG
   */
  private addCustomRoomToSvg(roomId: string, points: Point[]): void {
    const container = this.svgContainer?.nativeElement;
    if (!container) return;
    
    const svgElement = container.querySelector('svg');
    if (!svgElement) return;
    
    // Duplicate points array to avoid mutating the original
    const pointsCopy = JSON.parse(JSON.stringify(points));
    
    // Check if room already exists
    const existingRoom = svgElement.getElementById(roomId);
    if (existingRoom) {
      // Update the existing polygon points
      existingRoom.setAttribute('points', pointsCopy.map((p: Point) => `${p.x},${p.y}`).join(' '));
      return;
    }
    
    // Get viewBox for reference
    const viewBox = svgElement.viewBox?.baseVal;
    
    // If viewBox exists, log it for debugging
    if (viewBox) {
      console.log('Main SVG viewBox:', `${viewBox.x} ${viewBox.y} ${viewBox.width} ${viewBox.height}`);
    }
    
    // Format the points string exactly as in the modal
    const pointsString = pointsCopy.map((p: Point) => `${p.x},${p.y}`).join(' ');
    
    // Create a polygon element
    const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
    polygon.setAttribute('id', roomId);
    
    // Set the points attribute using the exact same format as in the modal
    polygon.setAttribute('points', pointsString);
    
    // Add styling for the room
    polygon.setAttribute('fill', 'rgba(49, 119, 255, 0.1)');
    polygon.setAttribute('stroke', 'var(--primary-color)');
    polygon.setAttribute('stroke-width', '1');
    polygon.setAttribute('class', 'custom-room');
    
    // Add data attribute to identify it as a custom room
    polygon.setAttribute('data-custom-room', 'true');
    
    // Add the polygon to the SVG
    svgElement.appendChild(polygon);
    
    // Update the SVG content string for persistence
    this.svgContent = new XMLSerializer().serializeToString(svgElement);
    
    // Log created polygon for verification
    console.log(`Created custom room with ID ${roomId} using points:`, pointsString);
  }
  
  /**
   * Display instructions to the user
   */
  private showInstructions(message: string): void {
    // Implement this according to your UI needs
    console.log('INSTRUCTION:', message);
    // For a real implementation, show a toast or other UI element
  }

  /**
   * Handle click on a detected room in the floorplan
   */
  handleDetectedRoomClick(event: MouseEvent, roomId: string): void {
    if (!this.isEditingEnabled) return;
    
    // Prevent event propagation to avoid triggering other click handlers
    event.stopPropagation();
    event.preventDefault();
    
    // Get the room data
    const room = this.tile.rooms?.[roomId];
    
    if (room) {
      // If room exists, open edit dialog
      this.editRoomDetails(roomId);
    } else {
      // This shouldn't happen, but handle it gracefully
      this.showInstructions('Room not found. It may have been deleted.');
    }
  }

  /**
   * Edit room details
   */
  editRoomDetails(roomId: string): void {
    if (!this.tile.rooms || !this.tile.rooms[roomId]) {
      console.error('Room not found:', roomId);
      return;
    }

    const dialogRef = this.dialog.open(RoomDetailsDialogComponent, {
      width: '500px',
      data: {
        room: this.sanitizeForFirestore(this.tile.rooms[roomId]),
        isEditing: this.isEditingEnabled
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.tile.rooms) {
        // Update the room with sanitized data
        this.tile.rooms[roomId] = this.sanitizeForFirestore(result);
        this.saveFloorplanChanges();
      }
    });
  }

  /**
   * Save the floorplan with any changes (including added rooms and highlights)
   */
  private saveFloorplanChanges(): void {
    if (!this.tile || !this.tile.id) {
      console.error('Cannot save floorplan: missing tile or tile ID');
      return;
    }
    
    // First update the tile with the current SVG content
    this.tile['svgContent'] = this.svgContent;
    
    // Sanitize the entire tile object before saving
    const sanitizedTile = this.sanitizeForFirestore(this.tile);
    
    // Trigger save with the sanitized tile
    this.saveChanges.emit(sanitizedTile);
    
    // Show save confirmation
    this.showInstructions('Floorplan changes saved successfully');
  }

  /**
   * Sanitize an object for Firestore storage
   * Removes invalid data types and ensures structure is compatible
   */
  private sanitizeForFirestore(obj: any): any {
    // Handle null or undefined
    if (obj === null || obj === undefined) {
      return null;
    }
    
    // Handle primitive types (safe for Firestore)
    if (typeof obj !== 'object') {
      return obj;
    }
    
    // Handle Date objects
    if (obj instanceof Date) {
      return obj;
    }
    
    // Handle arrays (ensure no nested arrays)
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeForFirestore(item));
    }
    
    // Handle Maps by converting to objects
    if (obj instanceof Map) {
      const result: Record<string, any> = {};
      obj.forEach((value, key) => {
        if (typeof key === 'string') {
          result[key] = this.sanitizeForFirestore(value);
        }
      });
      return result;
    }
    
    // Handle plain objects
    const result: Record<string, any> = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        // Skip functions, undefined, and symbols
        const value = obj[key];
        if (typeof value !== 'function' && typeof value !== 'undefined' && typeof value !== 'symbol') {
          result[key] = this.sanitizeForFirestore(value);
        }
      }
    }
    
    return result;
  }

  /**
   * Handle room click event
   */
  handleRoomClick(roomId: string, event?: MouseEvent): void {
    if (!this.isEditingEnabled) {
      this.onRoomClick(roomId, event);
      return;
    }
    
    if (this.isMultiSelectMode) {
      this.onRoomClick(roomId, event);
    } else {
      this.editRoomDetails(roomId);
    }
  }

  /**
   * Initialize room styles object with colors for each room
   */
  private initializeRoomStyles(): void {
    if (!this.tile?.rooms) return;
    
    Object.keys(this.tile.rooms).forEach(roomId => {
      const room = this.tile.rooms![roomId];
      const roomType = room.type;
      const baseColor = this.roomColors[roomType] || this.roomColors['room'];
      
      // Store in roomStyles for easy access
      this.roomStyles[roomId] = baseColor;
      
      // Apply color to SVG if content is loaded
      if (this.svgContent) {
        this.applyRoomColor(roomId, baseColor);
      }
    });
  }
  
  /**
   * Apply color to a room in the SVG
   */
  private applyRoomColor(roomId: string, color: string): void {
    const svgElementId = `room-${roomId}`;
    this.svgContent = this.svgHelper.setElementFill(this.svgContent, svgElementId, color);
  }

  /**
   * Toggle multi-select mode
   */
  toggleMultiSelectMode(): void {
    this.isMultiSelectMode = !this.isMultiSelectMode;
    
    // Clear selection when exiting multi-select mode
    if (!this.isMultiSelectMode) {
      this.clearRoomSelection();
    }
  }
  
  /**
   * Clear all room selections
   */
  clearRoomSelection(): void {
    // Remove highlighting from all selected rooms
    this.selectedRooms.forEach((room, roomId) => {
      const svgElementId = `room-${roomId}`;
      this.svgContent = this.svgHelper.addClassToElement(this.svgContent, svgElementId, 'selected');
    });
    
    this.selectedRooms.clear();
    this.highlightedAreaId = null;
  }
  
  /**
   * Apply different colors to selected rooms
   */
  private applySelectionColors(): void {
    // Reset all rooms to their original colors
    if (this.tile.rooms) {
      Object.keys(this.tile.rooms).forEach(roomId => {
        const room = this.tile.rooms![roomId];
        const svgElementId = `room-${roomId}`;
        const baseColor = this.roomStyles[roomId] || this.roomColors[room.type] || this.roomColors['room'];
        this.svgContent = this.svgHelper.setElementFill(this.svgContent, svgElementId, baseColor);
      });
    }
    
    // Apply highlight color to selected rooms
    this.selectedRooms.forEach((room, roomId) => {
      const svgElementId = `room-${roomId}`;
      // Make selected rooms visibly different
      this.svgContent = this.svgHelper.setElementFill(this.svgContent, svgElementId, 'rgba(var(--primary-color-rgb), 0.7)');
      this.svgContent = this.svgHelper.addClassToElement(this.svgContent, svgElementId, 'selected');
    });
  }

  /**
   * Save the selected rooms configuration
   */
  saveSelectedRooms(): void {
    if (!this.tile.rooms) {
      this.tile.rooms = {};
    }
    
    // Merge selected rooms into the tile configuration
    this.selectedRooms.forEach((room, roomId) => {
      // Create a sanitized copy of the room object for Firestore
      const sanitizedRoom = this.sanitizeForFirestore(room);
      this.tile.rooms![roomId] = sanitizedRoom;
    });
    
    // Save changes
    this.saveFloorplanChanges();
    
    // Exit multi-select mode
    this.isMultiSelectMode = false;
    this.clearRoomSelection();
  }

  /**
   * Open room details dialog for all selected rooms
   */
  editSelectedRooms(): void {
    if (this.selectedRooms.size === 0) return;
    
    // If only one room is selected, open the standard details dialog
    if (this.selectedRooms.size === 1) {
      const roomId = Array.from(this.selectedRooms.keys())[0];
      this.editRoomDetails(roomId);
      return;
    }
    
    // Otherwise, open a multi-room edit dialog (to be implemented)
    // For simplicity, we'll just open individual dialogs for each room
    const roomIds = Array.from(this.selectedRooms.keys());
    
    // Open dialogs sequentially to avoid overwhelming the user
    this.openRoomEditDialogSequentially(roomIds, 0);
  }
  
  /**
   * Open room edit dialogs sequentially
   */
  private openRoomEditDialogSequentially(roomIds: string[], index: number): void {
    if (index >= roomIds.length) {
      // All rooms have been processed
      this.saveFloorplanChanges();
      return;
    }
    
    const roomId = roomIds[index];
    const room = this.selectedRooms.get(roomId);
    
    if (!room) {
      // Skip if room doesn't exist
      this.openRoomEditDialogSequentially(roomIds, index + 1);
      return;
    }
    
    // Create the dialog for this room with sanitized room data
    const dialogRef = this.dialog.open(RoomDetailsDialogComponent, {
      width: '500px',
      data: {
        'room': this.sanitizeForFirestore(room),
        'isEditing': this.isEditingEnabled
      }
    });
    
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Sanitize the result before storing
        const sanitizedResult = this.sanitizeForFirestore(result);
        
        // Update the room in both the selected rooms map and tile configuration
        this.selectedRooms.set(roomId, sanitizedResult);
        if (this.tile.rooms) {
          this.tile.rooms[roomId] = sanitizedResult;
        }
      }
      
      // Process the next room
      this.openRoomEditDialogSequentially(roomIds, index + 1);
    });
  }

  /**
   * Handle SVG content changes from the viewer component
   */
  onSvgContentChange(svgContent: string): void {
    this.svgContent = svgContent;
  }

  /**
   * Handle SVG loaded event from the viewer component
   */
  onSvgLoaded(svgContainer: ElementRef): void {
    this.svgContainer = svgContainer;
    this.setupSvgInteractivity();
  }

  /**
   * Toggle rooms display expanded state
   */
  toggleRoomsExpanded(): void {
    this.roomsExpanded = !this.roomsExpanded;
  }

  /**
   * Track by function for room list
   */
  trackByRoomId(index: number, room: Room): string {
    return room.id;
  }

  /**
   * Handle room card click
   */
  onRoomCardClick(room: Room, event: MouseEvent): void {
    if (this.isMultiSelectMode) {
      // Handle multi-select
      if (this.selectedRooms.has(room.id)) {
        this.selectedRooms.delete(room.id);
      } else {
        this.selectedRooms.set(room.id, room);
      }
    } else {
      // Single room selection - highlight on floorplan and show details
      this.onRoomClick(room.id, event);
    }
  }

  /**
   * Get room color based on type
   */
  getRoomColor(roomType: Room['type']): string {
    return this.roomColors[roomType] || this.roomColors['room'];
  }

  /**
   * Get room icon based on type
   */
  getRoomIcon(roomType: Room['type']): string {
    const roomIcons: Record<string, string> = {
      'room': 'fa fa-home',
      'bathroom': 'fa fa-bath',
      'bedroom': 'fa fa-bed',
      'kitchen': 'fa fa-utensils',
      'living': 'fa fa-couch',
      'hallway': 'fa fa-arrows-alt',
      'other': 'fa fa-cube'
    };

    return roomIcons[roomType] || roomIcons['room'];
  }

  /**
   * Get room type label
   */
  getRoomTypeLabel(roomType: Room['type']): string {
    const roomTypeLabels: Record<string, string> = {
      'room': 'Room',
      'bathroom': 'Bathroom',
      'bedroom': 'Bedroom',
      'kitchen': 'Kitchen',
      'living': 'Living Room',
      'hallway': 'Hallway',
      'other': 'Other'
    };

    return roomTypeLabels[roomType] || 'Room';
  }

  /**
   * Handle click events on the SVG container
   * This method detects clicks on rooms and delegates to the appropriate handler
   */
  onSvgContainerClick(event: MouseEvent): void {
    // Find the clicked element
    const clickedElement = event.target as Element;

    // Check if a room was clicked (elements with id starting with "room-")
    if (clickedElement && clickedElement.id && clickedElement.id.startsWith('room-')) {
      // Handle the room click with the event
      this.handleRoomClick(clickedElement.id, event);
    }
  }
} 