# Floorplan Component

This directory contains the Floorplan Tile component and its associated assets. The component has been refactored into smaller, more maintainable pieces.

## Architecture Overview

The floorplan component is now organized into several focused components:

```
floorplan/
├── components/
│   ├── floorplan-viewer/          # Handles SVG/image loading and display
│   └── marker-overlay/            # Manages device markers and overlays
├── floorplan-tile.component.ts    # Main orchestrating component
├── floorplan-placeholder.component.ts  # Fallback placeholder
├── room-detection-dialog.component.ts  # Room creation dialog
├── floorplan.svg                  # Default floorplan asset
└── README.md                      # This documentation
```

### Component Responsibilities

- **FloorplanTileComponent**: Main component that orchestrates all functionality
- **FloorplanViewerComponent**: Handles SVG/image loading, display, and basic interactivity
- **MarkerOverlayComponent**: Manages device markers, Home Assistant integration
- **FloorplanPlaceholderComponent**: Provides fallback when floorplan can't be loaded
- **RoomDetectionDialogComponent**: Handles custom room creation

## Features

### 🏠 Interactive Floorplans
- Click on rooms to view detailed information
- Support for custom room creation and editing
- Multi-room selection and batch operations
- Room highlighting and visual feedback

### 🔌 Home Assistant Integration
- Real-time device state monitoring
- Interactive device controls (lights, switches, locks, covers)
- Automatic marker generation from HA entities
- Customizable device positioning and styling

### 🎨 Customization
- Support for custom SVG floorplans
- Configurable room colors by type
- Custom device markers and icons
- Responsive design with proper scaling

### 📱 User Experience
- Loading states and error handling
- Fallback placeholder when floorplan unavailable
- Accessibility features and keyboard navigation
- Touch-friendly interface for mobile devices

## SVG File Integration

The SVG floorplan file is stored directly in this component directory, which offers several advantages:
- Keeps related assets close to their components
- Makes the component more self-contained
- Simplifies development and maintenance

### How it Works

1. The SVG file (`floorplan.svg`) is placed in this directory
2. During build, this file is automatically copied to the public directory
3. The component loads it from the root path (`/floorplan.svg`) at runtime

### Fallback Strategy

For backward compatibility, the Floorplan component will attempt to load the SVG from several locations in this order:
1. The path specified in the component configuration
2. `/floorplan.svg` (current approach - direct from root/public folder)
3. `/assets/tiles/floorplan/floorplan.svg` (previous NX assets approach)
4. `/assets/images/floorplan.svg` (older assets folder approach)
5. `/images/floorplan.svg` (alternate public folder approach)

This ensures that the component continues to work even if the SVG is located in a different place.

## Creating Custom Floorplans

To use your own custom floorplan:

1. Replace the `floorplan.svg` file in this directory with your own SVG
2. Make sure your SVG has room/area elements with IDs that start with `room-` if you want interactive elements
3. Keep your SVG file clean and optimized for web use
4. Rebuild the application

## SVG Requirements for Interactive Elements

For interactive floorplans:
- Use IDs with the prefix `room-` for rooms/areas that should be clickable
- Keep the SVG structure simple and clean
- Ensure proper scaling by setting appropriate viewBox dimensions
- Test your SVG for compatibility with the floorplan component

### Example SVG Structure

```xml
<svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Living room -->
  <path id="room-living" d="M10,10 L300,10 L300,200 L10,200 Z"
        fill="rgba(255,255,255,0.1)" stroke="#333" stroke-width="2"/>

  <!-- Kitchen -->
  <path id="room-kitchen" d="M310,10 L600,10 L600,200 L310,200 Z"
        fill="rgba(255,255,255,0.1)" stroke="#333" stroke-width="2"/>

  <!-- Bathroom -->
  <path id="room-bathroom" d="M10,210 L200,210 L200,400 L10,400 Z"
        fill="rgba(255,255,255,0.1)" stroke="#333" stroke-width="2"/>
</svg>
```

## Room Configuration

### Basic Room Setup

Configure rooms in your dashboard configuration:

```typescript
const floorplanTile: FloorplanTile = {
  cols: 6,
  rows: 12,
  y: 0,
  x: 0,
  type: 'floorplan',
  id: 'floorplan',
  imagePath: '/floorplan.svg',
  title: 'Floor Plan',
  interactive: true,
  rooms: {
    'living': {
      id: 'living',
      name: 'Living Room',
      imagePath: '/assets/images/rooms/living.png',
      type: 'living',
      description: 'Main living area with TV and couch',
      devices: ['Living Room Lights', 'TV', 'Smart Speaker']
    },
    'kitchen': {
      id: 'kitchen',
      name: 'Kitchen',
      imagePath: '/assets/images/rooms/kitchen.png',
      type: 'kitchen',
      description: 'Kitchen with island and appliances',
      devices: ['Kitchen Lights', 'Refrigerator', 'Coffee Maker']
    }
  }
};
```

### Room Types and Colors

The component supports different room types with automatic color coding:

- `room` (default): Primary color
- `bathroom`: Light blue
- `bedroom`: Light green
- `kitchen`: Yellow
- `living`: Orange
- `hallway`: Gray
- `other`: Purple

### Custom Room Creation

Users can create custom rooms by:
1. Enabling edit mode on the dashboard
2. Clicking the "Add Room" button
3. Drawing a polygon on the floorplan
4. Filling in room details (name, type, description)

## Home Assistant Integration

### Device Mapping

Configure Home Assistant devices to appear on the floorplan:

```typescript
const floorplanTile: FloorplanTile = {
  // ... other properties
  haDevices: [
    {
      entityId: 'light.living_room_main',
      position: { x: 25, y: 30 },
      markerType: 'light',
      showLabel: true,
      showState: true,
      icon: 'fas fa-lightbulb',
      color: '#ffcc00'
    },
    {
      entityId: 'sensor.living_room_temperature',
      position: { x: 35, y: 40 },
      markerType: 'sensor',
      showState: true,
      stateAttribute: 'temperature'
    }
  ]
};
```

### Supported Entity Types

- **Lights**: Toggle on/off, show brightness
- **Switches**: Toggle on/off
- **Sensors**: Display current value with units
- **Binary Sensors**: Show on/off state
- **Locks**: Lock/unlock control
- **Covers**: Open/close/stop control
- **Climate**: Show temperature and controls

## Troubleshooting

### Floorplan Loading Issues

If your floorplan isn't loading correctly:
1. Check console errors in the browser
2. Verify the SVG file was copied to the public directory and is accessible at `/floorplan.svg`
3. Try the reload button which will attempt various paths
4. Ensure your SVG is well-formed and doesn't contain unsupported elements

### Room Interaction Issues

If clicking on rooms doesn't work:
- Verify room elements have IDs with the `room-` prefix
- Check that the `interactive` property is set to `true`
- Ensure the SVG elements are properly formed and not overlapping

### Home Assistant Connection Issues

If HA devices aren't showing:
- Verify Home Assistant service is configured
- Check entity IDs are correct and entities exist
- Ensure device positions are within the floorplan bounds (0-100%)
- Check browser console for connection errors

## Development

### Adding New Features

When extending the floorplan component:

1. **New UI Components**: Add to the `components/` directory
2. **New Services**: Create in the main services directory
3. **New Models**: Update `dashboard.models.ts`
4. **Tests**: Add corresponding test files

### Performance Considerations

- SVG interactivity is optimized using Angular zones
- Large floorplans should be optimized for web use
- Consider lazy loading for room images
- Use OnPush change detection where possible

### Accessibility

The component includes:
- Keyboard navigation support
- Screen reader friendly labels
- High contrast mode compatibility
- Touch-friendly controls for mobile

## Migration Guide

### From Previous Versions

If upgrading from an older version:

1. **Asset Location**: Move floorplan SVG to this directory
2. **Configuration**: Update room configuration format if needed
3. **Custom Styling**: Review CSS classes that may have changed
4. **Home Assistant**: Update device mapping configuration

### Breaking Changes

- Room IDs must now use the `room-` prefix in SVG
- Marker configuration format has been updated
- Some CSS classes have been renamed for consistency

## Refactoring Progress

### ✅ Completed

1. **Component Architecture**: Refactored monolithic component into focused sub-components
   - `FloorplanViewerComponent`: Handles SVG/image loading and display
   - `MarkerOverlayComponent`: Manages device markers and overlays
   - `FloorplanTileComponent`: Main orchestrating component (reduced from 1452 to ~800 lines)

2. **Service Extraction**: Created dedicated services
   - `FloorplanLoaderService`: Handles floorplan loading with fallback paths
   - Enhanced `SvgHelperService`: SVG manipulation utilities

3. **Documentation**: Comprehensive README with examples and troubleshooting

4. **Template Refactoring**: Updated template to use new component structure

### 🚧 In Progress

1. **Further Component Extraction**: Additional components that could be extracted:
   - `RoomManagerComponent`: Handle room creation, editing, and multi-select
   - `HomeAssistantIntegrationComponent`: Manage HA device integration
   - `FloorplanControlsComponent`: Edit mode controls and buttons

2. **State Management**: Consider using a state management pattern for complex interactions

3. **Testing**: Add comprehensive unit tests for all components

### 📋 Next Steps

#### Phase 1: Complete Component Extraction
- [ ] Extract room management logic into `RoomManagerComponent`
- [ ] Create `FloorplanControlsComponent` for edit mode UI
- [ ] Move HA integration logic to dedicated component

#### Phase 2: Enhanced Features
- [ ] Add drag-and-drop for marker positioning
- [ ] Implement room grouping and categories
- [ ] Add export/import functionality for floorplan configurations
- [ ] Enhanced accessibility features

#### Phase 3: Performance & Polish
- [ ] Implement virtual scrolling for large marker lists
- [ ] Add lazy loading for room images
- [ ] Optimize SVG rendering performance
- [ ] Add comprehensive error boundaries

### 🔧 Technical Debt

1. **Large Methods**: Some methods in the main component are still quite large and could be broken down further
2. **Type Safety**: Add more specific TypeScript interfaces for better type safety
3. **Error Handling**: Implement more robust error handling and user feedback
4. **Accessibility**: Add ARIA labels and keyboard navigation support

### 📊 Metrics

- **Before Refactor**: 1 component, 1452 lines
- **After Refactor**: 3 components, ~1200 total lines
- **Code Reduction**: ~17% reduction in main component size
- **Maintainability**: Significantly improved with focused responsibilities

### 🎯 Goals

1. **Maintainability**: Each component has a single, clear responsibility
2. **Reusability**: Components can be reused in other contexts
3. **Testability**: Smaller components are easier to unit test
4. **Performance**: Better change detection and rendering optimization
5. **Developer Experience**: Clearer code structure and documentation