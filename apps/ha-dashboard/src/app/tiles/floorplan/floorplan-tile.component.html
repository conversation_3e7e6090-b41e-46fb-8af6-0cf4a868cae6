<div class="floorplan-container" [class.dashboard-locked]="!isEditingEnabled">
  <div class="floorplan-main-content">
    <!-- Floorplan Viewer Component -->
    <div class="floorplan-viewer-wrapper">
      <app-floorplan-viewer
        [imagePath]="floorplanImage"
        [isEditingEnabled]="isEditingEnabled"
        [interactive]="tile.interactive || false"
        [svgContent]="svgContent"
        (svgContentChange)="onSvgContentChange($event)"
        (svgContainerClick)="onSvgContainerClick($event)"
        (roomClick)="onRoomClick($event.roomId, $event.event)"
        (svgLoaded)="onSvgLoaded($event)">
      </app-floorplan-viewer>

      <!-- Marker Overlay Component -->
      <app-marker-overlay
        *ngIf="tile.interactive"
        [markers]="allMarkers"
        [haEntities]="haEntities"
        [showLabels]="true"
        [isEditingEnabled]="isEditingEnabled"
        [loadingEntityIds]="loadingEntityIds"
        [errorEntityIds]="errorEntityIds"
        (markerClick)="onMarkerClick($event)"
        (deviceMarkerClick)="onDeviceMarkerClick($event)">
      </app-marker-overlay>

      <!-- Floating Action Buttons for Room Controls (only when editing) -->
      <div class="floating-controls" *ngIf="isEditingEnabled">
        <button class="fab room-creation-btn"
                [class.active]="isCreatingRoom"
                (click)="toggleRoomCreationMode()"
                [title]="isCreatingRoom ? 'Cancel room creation' : 'Create a custom room area'">
          <i class="fa" [class.fa-draw-polygon]="!isCreatingRoom" [class.fa-times]="isCreatingRoom"></i>
        </button>

        <button class="fab multi-select-btn" (click)="toggleMultiSelectMode()" [class.active]="isMultiSelectMode" title="Toggle multi-select mode">
          <i class="fa fa-object-group"></i>
        </button>

        <!-- Multi-select actions - only shown when in multi-select mode -->
        <div class="multi-select-actions" *ngIf="isMultiSelectMode">
          <span class="selection-count" *ngIf="selectedRooms.size > 0">{{ selectedRooms.size }} selected</span>
          <button class="fab edit-selected-btn" (click)="editSelectedRooms()" [disabled]="selectedRooms.size === 0" title="Edit selected rooms">
            <i class="fa fa-edit"></i>
          </button>
          <button class="fab save-selected-btn" (click)="saveSelectedRooms()" [disabled]="selectedRooms.size === 0" title="Save selected rooms">
            <i class="fa fa-save"></i>
          </button>
          <button class="fab clear-selection-btn" (click)="clearRoomSelection()" [disabled]="selectedRooms.size === 0" title="Clear selection">
            <i class="fa fa-times"></i>
          </button>
        </div>
      </div>

      <!-- Room Creation Mode Indicator -->
      <div *ngIf="isDrawingMode" class="room-creation-indicator">
        <i class="fa fa-draw-polygon"></i>
        <span *ngIf="currentRoomPoints.length === 0">Click to start drawing a room</span>
        <span *ngIf="currentRoomPoints.length > 0 && currentRoomPoints.length < 3">
          {{ currentRoomPoints.length }} point(s) added - need {{ 3 - currentRoomPoints.length }} more
        </span>
        <span *ngIf="currentRoomPoints.length >= 3">Click the first point to complete</span>
      </div>

      <!-- Inline Room Details Form -->
      <div *ngIf="showRoomDetailsForm" class="room-details-overlay">
        <div class="room-details-form">
          <div class="form-header">
            <h3>Room Details</h3>
            <button class="close-btn" (click)="cancelRoomCreation()">
              <i class="fa fa-times"></i>
            </button>
          </div>

          <div class="form-content">
            <div class="form-field">
              <label for="roomName">Room Name *</label>
              <input
                id="roomName"
                type="text"
                [(ngModel)]="currentRoomName"
                placeholder="Enter room name"
                maxlength="50">
            </div>

            <div class="form-field">
              <label for="roomType">Room Type</label>
              <select id="roomType" [(ngModel)]="currentRoomType">
                <option value="room">Room</option>
                <option value="bathroom">Bathroom</option>
                <option value="bedroom">Bedroom</option>
                <option value="kitchen">Kitchen</option>
                <option value="living">Living Room</option>
                <option value="hallway">Hallway</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div class="form-field">
              <label for="roomDescription">Description</label>
              <textarea
                id="roomDescription"
                [(ngModel)]="currentRoomDescription"
                placeholder="Optional description"
                rows="2"
                maxlength="200"></textarea>
            </div>
          </div>

          <div class="form-actions">
            <button class="cancel-btn" (click)="cancelRoomCreation()">Cancel</button>
            <button class="save-btn" (click)="saveCurrentRoom()" [disabled]="!currentRoomName.trim()">
              Save Room
            </button>
          </div>
        </div>
      </div>

      <!-- Multi-select mode indicator -->
      <div *ngIf="isMultiSelectMode" class="multi-select-indicator">
        <i class="fa fa-object-group"></i> Multi-select mode: Click or drag to select rooms
      </div>

      <!-- Home Assistant status indicator -->
      <div *ngIf="!haConnected && tile.haDevices && tile.haDevices.length > 0"
           class="ha-status-indicator offline">
        <i class="fas fa-exclamation-triangle"></i> Home Assistant Disconnected
      </div>

      <!-- Empty markers state -->
      <div *ngIf="tile.interactive && allMarkers.length === 0" class="empty-markers-message">
        <app-empty-state
          title="No Markers"
          description="No interactive markers have been added to this floorplan yet."
          icon="fa fa-map-marker">
        </app-empty-state>
      </div>
    </div>

    <!-- Rooms Display Section -->
    <div class="rooms-display" *ngIf="hasRooms">
      <div class="rooms-header">
        <h4>
          <i class="fa fa-home"></i>
          Rooms ({{ roomCount }})
        </h4>
        <button *ngIf="isEditingEnabled" class="toggle-rooms-btn" (click)="toggleRoomsExpanded()"
                [title]="roomsExpanded ? 'Collapse rooms' : 'Expand rooms'">
          <i class="fa" [class.fa-chevron-up]="roomsExpanded" [class.fa-chevron-down]="!roomsExpanded"></i>
        </button>
      </div>

      <div class="rooms-grid" [class.expanded]="roomsExpanded" *ngIf="roomsExpanded || !isEditingEnabled">
        <div
          *ngFor="let room of roomsList; trackBy: trackByRoomId"
          class="room-card"
          [class.selected]="selectedRooms.has(room.id)"
          [class.highlighted]="highlightedAreaId === 'room-' + room.id"
          (click)="onRoomCardClick(room, $event)"
          [title]="room.description || room.name">

          <!-- Room type icon -->
          <div class="room-icon" [style.background-color]="getRoomColor(room.type)">
            <i [class]="getRoomIcon(room.type)"></i>
          </div>

          <!-- Room info -->
          <div class="room-info">
            <div class="room-name">{{ room.name }}</div>
            <div class="room-type">{{ getRoomTypeLabel(room.type) }}</div>
            <div class="room-devices" *ngIf="room.devices && room.devices.length > 0">
              <i class="fa fa-microchip"></i>
              {{ room.devices.length }} device{{ room.devices.length !== 1 ? 's' : '' }}
            </div>
          </div>

          <!-- Room actions -->
          <div class="room-actions" *ngIf="isEditingEnabled">
            <button class="edit-room-btn" (click)="editRoomDetails(room.id); $event.stopPropagation()"
                    title="Edit room">
              <i class="fa fa-edit"></i>
            </button>
          </div>
        </div>

        <!-- Empty state for rooms -->
        <div class="empty-rooms" *ngIf="roomCount === 0">
          <i class="fa fa-home"></i>
          <span>No rooms created yet</span>
          <button *ngIf="isEditingEnabled" class="create-first-room-btn" (click)="toggleRoomCreationMode()">
            <i class="fa fa-plus"></i> Create your first room
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Room Detail Modal -->
  <div *ngIf="showRoomDetail && selectedRoom" class="room-detail-modal-wrapper">
    <app-room-detail
      [room]="selectedRoom"
      [isVisible]="showRoomDetail"
      (close)="closeRoomDetail()">
    </app-room-detail>
  </div>
</div>