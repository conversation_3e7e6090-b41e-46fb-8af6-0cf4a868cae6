import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { SvgRoomElement } from '../../services/svg-helper.service';

interface RoomDetectionDialogData {
  allRooms: SvgRoomElement[];
  newRooms: SvgRoomElement[];
  existingRooms: SvgRoomElement[];
}

@Component({
  selector: 'app-room-detection-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatCheckboxModule
  ],
  template: `
    <h2 mat-dialog-title>Room Detection</h2>
    <div mat-dialog-content>
      <p *ngIf="data.newRooms.length === 0">
        All rooms in the floorplan are already configured.
      </p>
      
      <p *ngIf="data.newRooms.length > 0">
        Found {{ data.newRooms.length }} new rooms in the floorplan.
      </p>
      
      <div class="rooms-container" *ngIf="data.newRooms.length > 0">
        <div class="select-actions">
          <button mat-button (click)="selectAll()">Select All</button>
          <button mat-button (click)="selectNone()">Select None</button>
        </div>
        
        <div class="room-list">
          <div *ngFor="let room of data.newRooms" class="room-item">
            <mat-checkbox [(ngModel)]="selectedRooms[room.actualId]">
              <div class="room-details">
                <span class="room-name">{{ room.name }}</span>
                <span class="room-type">({{ room.type }})</span>
                <span *ngIf="room.points && room.points.length > 0" class="room-shape">Polygon with {{ room.points.length }} points</span>
              </div>
            </mat-checkbox>
          </div>
        </div>
      </div>
      
      <div class="existing-rooms" *ngIf="data.existingRooms.length > 0">
        <h3>Existing Rooms ({{ data.existingRooms.length }})</h3>
        <ul>
          <li *ngFor="let room of data.existingRooms">
            {{ room.name }} ({{ room.type }})
          </li>
        </ul>
      </div>
    </div>
    
    <div mat-dialog-actions>
      <button mat-button (click)="dialogRef.close()">Cancel</button>
      <button mat-button color="primary" [disabled]="data.newRooms.length === 0 || getSelectedRoomsCount() === 0" (click)="addSelectedRooms()">
        Add {{ getSelectedRoomsCount() }} Rooms
      </button>
    </div>
  `,
  styles: [`
    .rooms-container {
      margin-top: 16px;
      margin-bottom: 16px;
    }
    
    .select-actions {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 8px;
    }
    
    .room-list {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 8px;
    }
    
    .room-item {
      margin-bottom: 8px;
      padding: 4px;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .room-details {
      display: inline-flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 8px;
    }
    
    .room-name {
      font-weight: 500;
    }
    
    .room-type {
      color: #666;
    }
    
    .room-shape {
      font-style: italic;
      color: #777;
      font-size: 0.9em;
    }
    
    .existing-rooms {
      margin-top: 16px;
      padding: 8px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
    
    .existing-rooms h3 {
      margin-top: 0;
      font-size: 1rem;
      color: #444;
    }
  `]
})
export class RoomDetectionDialogComponent implements OnInit {
  selectedRooms: { [id: string]: boolean } = {};
  
  constructor(
    public dialogRef: MatDialogRef<RoomDetectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: RoomDetectionDialogData
  ) {}
  
  ngOnInit(): void {
    // Pre-select all new rooms by default
    this.data.newRooms.forEach(room => {
      this.selectedRooms[room.actualId] = true;
    });
  }
  
  selectAll(): void {
    this.data.newRooms.forEach(room => {
      this.selectedRooms[room.actualId] = true;
    });
  }
  
  selectNone(): void {
    this.data.newRooms.forEach(room => {
      this.selectedRooms[room.actualId] = false;
    });
  }
  
  getSelectedRoomsCount(): number {
    return Object.values(this.selectedRooms).filter(selected => selected).length;
  }
  
  addSelectedRooms(): void {
    const selectedRoomElements = this.data.newRooms.filter(room => 
      this.selectedRooms[room.actualId]
    );
    
    this.dialogRef.close({
      selectedRooms: selectedRoomElements
    });
  }
} 