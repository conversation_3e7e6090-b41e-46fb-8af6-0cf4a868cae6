import { Compo<PERSON>, OnIni<PERSON>, OnD<PERSON>roy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule, Sort } from '@angular/material/sort';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { HomeAssistantService, HAEntity } from '../../services/home-assistant.service';
import { Subscription } from 'rxjs';

// Domain icons mapping
const DOMAIN_ICONS: Record<string, string> = {
  light: 'lightbulb',
  switch: 'power_settings_new',
  sensor: 'sensors',
  binary_sensor: 'fiber_manual_record',
  climate: 'thermostat',
  media_player: 'music_note',
  camera: 'videocam',
  cover: 'vertical_shades',
  fan: 'mode_fan',
  automation: 'smart_toy',
  scene: 'movie',
  script: 'code',
  weather: 'cloud',
  sun: 'wb_sunny',
  device_tracker: 'gps_fixed',
  zone: 'location_on',
  person: 'person',
  group: 'group',
  input_boolean: 'toggle_on',
  input_number: 'pin',
  input_select: 'list',
  input_text: 'text_fields',
  timer: 'timer',
  vacuum: 'robot',
  lock: 'lock',
  calendar: 'calendar_today'
};

@Component({
  selector: 'app-entity-browser',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatDividerModule,
    MatExpansionModule,
    MatChipsModule,
    MatBadgeModule,
    MatProgressSpinnerModule
  ],
  template: `
    <mat-card class="entity-browser-container">
      <mat-card-header>
        <mat-card-title>{{ title || 'Home Assistant Entities' }}</mat-card-title>
        <div class="header-actions">
          <span class="entity-count" matTooltip="Total entities">
            {{ entities.length }} entities
          </span>
          <button mat-icon-button (click)="refreshEntities()" matTooltip="Refresh entities">
            <mat-icon [class.rotating]="isLoading">refresh</mat-icon>
          </button>
        </div>
      </mat-card-header>
      
      <mat-card-content>
        <!-- Loading indicator -->
        <div *ngIf="isLoading" class="loading-overlay">
          <mat-spinner diameter="50"></mat-spinner>
        </div>
        
        <!-- Filters -->
        <div class="filter-container">
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Search entities</mat-label>
            <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilters()">
            <button *ngIf="searchTerm" matSuffix mat-icon-button (click)="clearSearch()">
              <mat-icon>close</mat-icon>
            </button>
            <mat-icon matPrefix>search</mat-icon>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="domain-filter">
            <mat-label>Filter by domain</mat-label>
            <mat-select [(ngModel)]="selectedDomain" (selectionChange)="applyFilters()">
              <mat-option value="">All domains</mat-option>
              <mat-option *ngFor="let domain of availableDomains" [value]="domain">
                {{ domain }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="state-filter">
            <mat-label>Filter by state</mat-label>
            <mat-select [(ngModel)]="selectedState" (selectionChange)="applyFilters()">
              <mat-option value="">All states</mat-option>
              <mat-option *ngFor="let state of availableStates" [value]="state">
                {{ state }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        
        <!-- Entity table with pagination -->
        <div class="table-container">
          <table mat-table [dataSource]="filteredEntities" matSort (matSortChange)="sortData($event)" class="entity-table">
            <!-- Domain Column -->
            <ng-container matColumnDef="domain">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Domain</th>
              <td mat-cell *matCellDef="let entity" class="domain-cell">
                <div class="domain-icon">
                  <mat-icon [matTooltip]="getDomain(entity)">{{ getDomainIcon(entity) }}</mat-icon>
                </div>
                <span class="domain-name">{{ getDomain(entity) }}</span>
              </td>
            </ng-container>
            
            <!-- Name Column -->
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
              <td mat-cell *matCellDef="let entity" class="entity-name-cell">
                <div class="entity-name">{{ getEntityFriendlyName(entity) }}</div>
                <div class="entity-id">{{ entity.entity_id }}</div>
              </td>
            </ng-container>
            
            <!-- State Column -->
            <ng-container matColumnDef="state">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>State</th>
              <td mat-cell *matCellDef="let entity" class="state-cell">
                <span class="state-value" [ngClass]="getStateClass(entity)">{{ entity.state }}</span>
                <span *ngIf="entity.attributes.unit_of_measurement" class="unit">
                  {{ entity.attributes.unit_of_measurement }}
                </span>
              </td>
            </ng-container>
            
            <!-- Last Updated Column -->
            <ng-container matColumnDef="last_updated">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Updated</th>
              <td mat-cell *matCellDef="let entity" class="last-updated-cell">
                {{ formatDate(entity.last_updated) }}
              </td>
            </ng-container>
            
            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let entity" class="actions-cell">
                <button mat-icon-button matTooltip="View details" (click)="viewEntityDetails(entity)">
                  <mat-icon>info</mat-icon>
                </button>
                <button 
                  mat-icon-button 
                  *ngIf="canToggleEntity(entity)"
                  [matTooltip]="entity.state === 'on' ? 'Turn off' : 'Turn on'"
                  (click)="toggleEntity(entity)">
                  <mat-icon>{{ entity.state === 'on' ? 'toggle_on' : 'toggle_off' }}</mat-icon>
                </button>
              </td>
            </ng-container>
            
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;" (click)="viewEntityDetails(row)" class="entity-row"></tr>
          </table>
          
          <!-- No results message -->
          <div *ngIf="filteredEntities.length === 0 && !isLoading" class="no-results">
            <mat-icon>search_off</mat-icon>
            <p>No entities found matching your filters.</p>
            <button mat-button color="primary" (click)="resetFilters()">Reset filters</button>
          </div>
        </div>
        
        <!-- Pagination -->
        <mat-paginator 
          [length]="filteredEntities.length"
          [pageSize]="pageSize"
          [pageSizeOptions]="[10, 25, 50, 100]"
          (page)="onPageChange($event)"
          showFirstLastButtons>
        </mat-paginator>
        
        <!-- Entity details panel -->
        <mat-expansion-panel *ngIf="selectedEntity" class="entity-details-panel">
          <mat-expansion-panel-header>
            <mat-panel-title>
              <mat-icon>{{ getDomainIcon(selectedEntity) }}</mat-icon>
              {{ getEntityFriendlyName(selectedEntity) }}
            </mat-panel-title>
            <mat-panel-description>
              {{ selectedEntity.entity_id }}
            </mat-panel-description>
          </mat-expansion-panel-header>
          
          <div class="entity-details">
            <div class="detail-section">
              <h3>Basic Information</h3>
              <div class="detail-row">
                <span class="detail-label">Domain:</span>
                <span class="detail-value">{{ getDomain(selectedEntity) }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Entity ID:</span>
                <span class="detail-value">{{ selectedEntity.entity_id }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">State:</span>
                <span class="detail-value" [ngClass]="getStateClass(selectedEntity)">{{ selectedEntity.state }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Last Changed:</span>
                <span class="detail-value">{{ formatDate(selectedEntity.last_changed, true) }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Last Updated:</span>
                <span class="detail-value">{{ formatDate(selectedEntity.last_updated, true) }}</span>
              </div>
            </div>
            
            <mat-divider></mat-divider>
            
            <div class="detail-section">
              <h3>Attributes</h3>
              <div *ngIf="getAttributesArray(selectedEntity).length === 0" class="no-attributes">
                No attributes available
              </div>
              <div *ngFor="let attr of getAttributesArray(selectedEntity)" class="detail-row attribute-row">
                <span class="detail-label">{{ attr.key }}:</span>
                <span class="detail-value">{{ formatAttributeValue(attr.value) }}</span>
              </div>
            </div>
            
            <mat-divider></mat-divider>
            
            <div class="detail-section">
              <h3>Actions</h3>
              <div class="action-buttons">
                <button 
                  mat-raised-button 
                  color="primary" 
                  *ngIf="canToggleEntity(selectedEntity)"
                  (click)="toggleEntity(selectedEntity)">
                  {{ selectedEntity.state === 'on' ? 'Turn Off' : 'Turn On' }}
                </button>
                
                <button mat-raised-button (click)="refreshEntity(selectedEntity)">
                  <mat-icon>refresh</mat-icon> Refresh
                </button>
                
                <button mat-raised-button (click)="closeDetails()">
                  <mat-icon>close</mat-icon> Close
                </button>
              </div>
            </div>
          </div>
        </mat-expansion-panel>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .entity-browser-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      background-color: var(--background-secondary, #ffffff);
      color: var(--text-color, #212529);
      border-radius: 8px;
    }
    
    mat-card-header {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--border-color, #ddd);
    }
    
    mat-card-content {
      flex: 1;
      overflow: hidden;
      position: relative;
      display: flex;
      flex-direction: column;
      padding: 0;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .entity-count {
      font-size: 14px;
      color: var(--text-secondary, #6c757d);
    }
    
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(var(--background-color-rgb, 245, 245, 245), 0.7);
      z-index: 10;
    }
    
    .rotating {
      animation: spin 1.5s linear infinite;
    }
    
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      padding: 16px;
      background-color: var(--background-secondary, #ffffff);
      border-bottom: 1px solid var(--border-color, #ddd);
    }
    
    .search-field {
      flex: 2 1 300px;
    }
    
    .domain-filter, .state-filter {
      flex: 1 1 200px;
    }
    
    .table-container {
      flex: 1;
      overflow: auto;
      position: relative;
      background-color: var(--background-secondary, #ffffff);
    }
    
    .entity-table {
      width: 100%;
      background-color: var(--background-secondary, #ffffff);
    }
    
    .mat-header-cell {
      background-color: var(--background-color, #f5f5f5);
      color: var(--text-secondary, #6c757d);
      font-weight: 500;
    }
    
    .mat-cell {
      color: var(--text-color, #212529);
    }
    
    .domain-cell {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .domain-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: rgba(var(--primary-color-rgb, 0, 123, 255), 0.1);
    }
    
    .domain-name {
      text-transform: capitalize;
    }
    
    .entity-name-cell {
      min-width: 200px;
    }
    
    .entity-name {
      font-weight: 500;
      color: var(--text-color, #212529);
    }
    
    .entity-id {
      font-size: 12px;
      color: var(--text-secondary, #6c757d);
    }
    
    .state-cell {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    .state-value {
      font-weight: 500;
      text-transform: lowercase;
    }
    
    .state-value.on {
      color: var(--success-color, #4CAF50);
    }
    
    .state-value.off {
      color: var(--text-secondary, #6c757d);
    }
    
    .state-value.unavailable, .state-value.unknown {
      color: var(--warning-color, #ff9800);
    }
    
    .state-value.error {
      color: var(--error-color, #F44336);
    }
    
    .unit {
      font-size: 12px;
      color: var(--text-secondary, #6c757d);
    }
    
    .last-updated-cell {
      color: var(--text-secondary, #6c757d);
      min-width: 120px;
    }
    
    .actions-cell {
      white-space: nowrap;
    }
    
    .entity-row {
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .entity-row:hover {
      background-color: rgba(var(--primary-color-rgb, 0, 123, 255), 0.05);
    }
    
    .no-results {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      color: var(--text-secondary, #6c757d);
    }
    
    .no-results mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
    }
    
    .entity-details-panel {
      margin: 16px;
      background-color: var(--background-secondary, #ffffff);
      color: var(--text-color, #212529);
    }
    
    .entity-details {
      padding: 16px 0;
    }
    
    .detail-section {
      margin-bottom: 24px;
    }
    
    .detail-section h3 {
      font-size: 16px;
      margin-bottom: 12px;
      color: var(--primary-color, #007bff);
    }
    
    .detail-row {
      display: flex;
      margin-bottom: 8px;
      flex-wrap: wrap;
    }
    
    .detail-label {
      min-width: 140px;
      font-weight: 500;
      color: var(--text-secondary, #6c757d);
    }
    
    .detail-value {
      flex: 1;
      color: var(--text-color, #212529);
    }
    
    .attribute-row {
      border-bottom: 1px dashed var(--border-color-light, #eee);
      padding-bottom: 8px;
    }
    
    .no-attributes {
      font-style: italic;
      color: var(--text-secondary, #6c757d);
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
    
    mat-paginator {
      background-color: var(--background-secondary, #ffffff);
      color: var(--text-color, #212529);
      border-top: 1px solid var(--border-color, #ddd);
    }
    
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
    
    @media (max-width: 768px) {
      .filter-container {
        flex-direction: column;
        gap: 8px;
      }
      
      .search-field, .domain-filter, .state-filter {
        flex: 1 1 100%;
      }
      
      .entity-details-panel {
        margin: 8px;
      }
    }
  `]
})
export class EntityBrowserComponent implements OnInit, OnDestroy {
  @Input() title?: string;
  @Input() domainFilter?: string;
  @Input() stateFilter?: string;
  entities: HAEntity[] = [];
  filteredEntities: HAEntity[] = [];
  isLoading = true;
  
  // Pagination
  pageSize = 25;
  currentPage = 0;
  
  // Filters
  searchTerm = '';
  selectedDomain = '';
  selectedState = '';
  
  // Selected entity for details view
  selectedEntity: HAEntity | null = null;
  
  // Table columns
  displayedColumns: string[] = ['domain', 'name', 'state', 'last_updated', 'actions'];
  
  // Available filter options
  availableDomains: string[] = [];
  availableStates: string[] = [];
  
  private subscription = new Subscription();
  
  constructor(private haService: HomeAssistantService) { }
  
  ngOnInit(): void {
    this.loadEntities();
  }
  
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  
  /**
   * Load entities from Home Assistant
   */
  loadEntities(): void {
    this.isLoading = true;
    this.subscription.add(
      this.haService.fetchAllEntities().subscribe({
        next: (entities) => {
          this.entities = entities;
          
          // Apply initial domain filter if provided
          if (this.domainFilter) {
            this.selectedDomain = this.domainFilter;
          }
          
          // Apply initial state filter if provided
          if (this.stateFilter) {
            this.selectedState = this.stateFilter;
          }
          
          // Update filters and apply them
          this.updateAvailableFilters();
          this.applyFilters();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching entities:', error);
          this.isLoading = false;
        }
      })
    );
  }
  
  /**
   * Refresh the entities list
   */
  refreshEntities(): void {
    this.isLoading = true;
    
    // First unsubscribe from the current subscription
    this.subscription.unsubscribe();
    this.subscription = new Subscription();
    
    // Trigger a refresh in the service (if needed)
    this.haService.fetchAllEntities().subscribe({
      next: () => {
        // Re-subscribe to get the updated entities
        this.loadEntities();
      },
      error: (error) => {
        console.error('Error refreshing entities:', error);
        this.isLoading = false;
      }
    });
  }
  
  /**
   * Refresh a specific entity
   */
  refreshEntity(entity: HAEntity): void {
    this.haService.getEntity(entity.entity_id).subscribe({
      next: (updatedEntity) => {
        if (updatedEntity) {
          // Update in the local arrays
          const index = this.entities.findIndex(e => e.entity_id === entity.entity_id);
          if (index >= 0) {
            this.entities[index] = updatedEntity;
            
            // Also update in filtered array if present
            const filteredIndex = this.filteredEntities.findIndex(e => e.entity_id === entity.entity_id);
            if (filteredIndex >= 0) {
              this.filteredEntities[filteredIndex] = updatedEntity;
            }
            
            // Update selected entity if it's the same one
            if (this.selectedEntity && this.selectedEntity.entity_id === entity.entity_id) {
              this.selectedEntity = updatedEntity;
            }
          }
        }
      },
      error: (error) => {
        console.error(`Error refreshing entity ${entity.entity_id}:`, error);
      }
    });
  }
  
  /**
   * Update available filter options based on the current entities
   */
  updateAvailableFilters(): void {
    // Get unique domains
    const domains = Array.from(new Set(this.entities.map(entity => this.getDomain(entity))));
    this.availableDomains = domains.sort();
    
    // Get unique states
    const states = Array.from(new Set(this.entities.map(entity => entity.state)));
    this.availableStates = states.sort();
  }
  
  /**
   * Apply filters to the entities list
   */
  applyFilters(): void {
    let filtered = [...this.entities];
    
    // Apply domain filter
    if (this.selectedDomain) {
      filtered = filtered.filter(entity => 
        this.getDomain(entity) === this.selectedDomain
      );
    }
    
    // Apply state filter
    if (this.selectedState) {
      filtered = filtered.filter(entity => 
        entity.state === this.selectedState
      );
    }
    
    // Apply search term
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(entity => 
        entity.entity_id.toLowerCase().includes(term) || 
        this.getEntityFriendlyName(entity).toLowerCase().includes(term) ||
        entity.state.toLowerCase().includes(term)
      );
    }
    
    this.filteredEntities = filtered;
    this.currentPage = 0; // Reset to first page when filtering
  }
  
  /**
   * Reset all filters
   */
  resetFilters(): void {
    this.searchTerm = '';
    this.selectedDomain = '';
    this.selectedState = '';
    this.applyFilters();
  }
  
  /**
   * Clear the search term
   */
  clearSearch(): void {
    this.searchTerm = '';
    this.applyFilters();
  }
  
  /**
   * Handle pagination events
   */
  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
  }
  
  /**
   * Sort the data when a column header is clicked
   */
  sortData(sort: Sort): void {
    if (!sort.active || sort.direction === '') {
      return;
    }
    
    this.filteredEntities = this.filteredEntities.slice().sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'domain': return this.compare(this.getDomain(a), this.getDomain(b), isAsc);
        case 'name': return this.compare(this.getEntityFriendlyName(a), this.getEntityFriendlyName(b), isAsc);
        case 'state': return this.compare(a.state, b.state, isAsc);
        case 'last_updated': return this.compare(a.last_updated, b.last_updated, isAsc);
        default: return 0;
      }
    });
  }
  
  /**
   * Compare function for sorting
   */
  private compare(a: string | number | Date, b: string | number | Date, isAsc: boolean): number {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
  }
  
  /**
   * Get the domain of an entity (e.g., 'light', 'switch')
   */
  getDomain(entity: HAEntity): string {
    return entity.entity_id.split('.')[0];
  }
  
  /**
   * Get an icon for a domain
   */
  getDomainIcon(entity: HAEntity): string {
    const domain = this.getDomain(entity);
    return DOMAIN_ICONS[domain] || 'extension';
  }
  
  /**
   * Get a friendly name for an entity
   */
  getEntityFriendlyName(entity: HAEntity): string {
    return entity.attributes?.['friendly_name'] || entity.entity_id.split('.')[1].replace(/_/g, ' ');
  }
  
  /**
   * Format a date for display
   */
  formatDate(dateString: string, includeTime = false): string {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }
    
    // Check if it's today
    const today = new Date();
    const isToday = date.getDate() === today.getDate() &&
                    date.getMonth() === today.getMonth() &&
                    date.getFullYear() === today.getFullYear();
    
    if (isToday) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      
      return `Today, ${hours}:${minutes}${includeTime ? `:${seconds}` : ''}`;
    }
    
    // Check if it's yesterday
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.getDate() === yesterday.getDate() &&
                        date.getMonth() === yesterday.getMonth() &&
                        date.getFullYear() === yesterday.getFullYear();
    
    if (isYesterday) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      
      return `Yesterday, ${hours}:${minutes}${includeTime ? `:${seconds}` : ''}`;
    }
    
    // For older dates
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    
    if (includeTime) {
      return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    } else {
      return `${day}/${month}/${year}`;
    }
  }
  
  /**
   * Get a CSS class for a state value
   */
  getStateClass(entity: HAEntity): string {
    const state = entity.state.toLowerCase();
    
    if (state === 'on' || state === 'open' || state === 'unlocked' || state === 'active') {
      return 'on';
    } else if (state === 'off' || state === 'closed' || state === 'locked' || state === 'inactive') {
      return 'off';
    } else if (state === 'unavailable' || state === 'unknown') {
      return 'unavailable';
    } else {
      return '';
    }
  }
  
  /**
   * Check if an entity can be toggled
   */
  canToggleEntity(entity: HAEntity): boolean {
    const domain = this.getDomain(entity);
    const toggleableDomains = ['light', 'switch', 'input_boolean', 'automation', 'fan'];
    
    return toggleableDomains.includes(domain);
  }
  
  /**
   * Toggle an entity's state
   */
  toggleEntity(entity: HAEntity): void {
    this.haService.toggleEntity(entity.entity_id).subscribe({
      next: (result) => {
        if (result.success) {
          // Refresh the entity after a short delay
          setTimeout(() => {
            this.refreshEntity(entity);
          }, 1000);
        } else {
          console.error(`Error toggling entity ${entity.entity_id}:`, result.error);
        }
      },
      error: (error) => {
        console.error(`Error toggling entity ${entity.entity_id}:`, error);
      }
    });
  }
  
  /**
   * View details of an entity
   */
  viewEntityDetails(entity: HAEntity): void {
    this.selectedEntity = entity;
  }
  
  /**
   * Close the details panel
   */
  closeDetails(): void {
    this.selectedEntity = null;
  }
  
  /**
   * Get an array of attribute key-value pairs for the entity
   */
  getAttributesArray(entity: HAEntity): {key: string, value: any}[] {
    if (!entity.attributes) {
      return [];
    }
    
    return Object.entries(entity.attributes).map(([key, value]) => ({key, value}));
  }
  
  /**
   * Format an attribute value for display
   */
  formatAttributeValue(value: any): string {
    if (value === null || value === undefined) {
      return 'null';
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  }
} 