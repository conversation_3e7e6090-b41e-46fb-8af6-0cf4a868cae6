:host {
  display: block;
  min-height: 100vh;
  background-color: var(--background-color);
}

// Authentication loading state
.auth-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--background-color);
  z-index: 1000;
  color: var(--text-color);
  gap: 24px;

  p {
    font-size: 18px;
    margin-top: 16px;
  }
}

// Authentication error state
.auth-error, .connection-error {
  width: 400px;
  max-width: 90%;
  margin: 100px auto;
  padding: 0;
  overflow: hidden;

  mat-card-header {
    background-color: var(--primary-color);
    color: white;
    padding: 16px;
    
    .error-icon {
      font-size: 24px;
      height: 24px;
      width: 24px;
      margin-right: 8px;
    }
    
    mat-card-title {
      margin: 0;
      font-size: 20px;
      font-weight: 400;
    }
  }
  
  mat-card-content {
    padding: 24px;
    font-size: 16px;
    
    p {
      margin-top: 0;
    }
    
    .status {
      display: flex;
      align-items: center;
      margin-top: 16px;
      gap: 16px;
      
      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
  
  mat-card-actions {
    display: flex;
    padding: 0 24px 24px;
    gap: 16px;
  }
  
  mat-card-footer {
    padding: 12px 24px;
    background-color: var(--background-secondary);
    border-top: 1px solid var(--border-color);
    
    .hint {
      margin: 0;
      font-size: 13px;
      color: var(--text-secondary);
    }
  }
}

// Connection error specific styles
.connection-error {
  mat-card-header {
    background-color: var(--error-color);
  }
}

// Responsive styles
@media (max-width: 500px) {
  .auth-error, .connection-error {
    margin: 60px auto;
    
    mat-card-actions {
      flex-direction: column;
      
      button {
        width: 100%;
      }
    }
  }
}

// Navigation bar styles
.app-nav {
  display: flex;
  align-items: center;
  padding: 0 20px;
  height: 64px;
  background-color: var(--primary-color);
  color: white;
  z-index: 100;
  position: sticky;
  top: 0;

  .app-title {
    font-size: 20px;
    font-weight: 500;
    text-decoration: none;
    color: white;
    margin-right: 40px;
    white-space: nowrap;
  }

  .nav-links {
    display: flex;
    gap: 24px;
    flex-grow: 1;
    flex-wrap: wrap;

    a {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      font-size: 15px;
      padding: 8px 12px;
      border-radius: 4px;
      transition: all 0.2s;
      position: relative;

      &:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
      }

      &.active {
        color: white;
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 0;
          right: 0;
          height: 3px;
          background-color: white;
          border-radius: 3px 3px 0 0;
        }
      }
    }
  }
}

// Responsive navigation
@media (max-width: 600px) {
  .app-nav {
    flex-direction: column;
    height: auto;
    padding: 12px 20px;

    .app-title {
      margin-right: 0;
      margin-bottom: 12px;
    }

    .nav-links {
      width: 100%;
      justify-content: center;
      
      a {
        padding: 6px 8px;
        
        &.active::after {
          bottom: -6px;
        }
      }
    }
  }
}
