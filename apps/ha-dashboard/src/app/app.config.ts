import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { appRoutes } from './app.routes';
import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { 
  getFirestore, 
  provideFirestore, 
  enableMultiTabIndexedDbPersistence,
  connectFirestoreEmulator,
  enableIndexedDbPersistence,
  waitForPendingWrites,
  FirestoreSettings
} from '@angular/fire/firestore';
import { getAuth, provideAuth, connectAuthEmulator } from '@angular/fire/auth';
import { environment } from '../environments/environment';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { HomeAssistantService } from './services/home-assistant.service';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(appRoutes),
    provideHttpClient(withInterceptorsFromDi()),
    provideAnimations(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    
    // Use Firebase for backend services
    provideFirebaseApp(() => {
      try {
        // Initialize the Firebase app
        const app = initializeApp(environment.firebase);
        return app;
      } catch (error) {
        console.error('Failed to initialize Firebase app:', error);
        throw error;
      }
    }),
    
    HomeAssistantService,
    provideFirestore(() => {
      try {
        // Initialize Firestore with the ha-dashboard database
        const firestore = getFirestore();
        
        // Use the 'ha-dashboard' database instead of the default
        // Note: This must be handled through database selection in the Firestore console
        // or through API calls, not through the client SDK initialization
        console.log('Using ha-dashboard Firestore database');
        
        // Try to enable enhanced offline capabilities
        try {
          // Enable multi-tab persistence (for browsers that support it)
          enableMultiTabIndexedDbPersistence(firestore)
            .then(() => console.log('Multi-tab persistence enabled'))
            .catch(error => {
              console.warn('Multi-tab persistence failed:', error);
              
              // Fall back to single-tab persistence
              enableIndexedDbPersistence(firestore)
                .then(() => console.log('Single-tab persistence enabled'))
                .catch(err => console.error('Failed to enable persistence:', err));
            });
          
          // Wait for pending writes when online
          waitForPendingWrites(firestore)
            .then(() => console.log('Pending writes synchronized'))
            .catch(err => console.warn('Error synchronizing pending writes:', err));
        } catch (persistenceError) {
          console.warn('Failed to setup persistence:', persistenceError);
        }
        
        // Use emulator in development if needed
        // if (location.hostname === 'localhost') {
        //   connectFirestoreEmulator(firestore, 'localhost', 8080);
        //   console.log('Connected to Firestore emulator');
        // }
        
        return firestore;
      } catch (error) {
        console.error('Failed to initialize Firestore:', error);
        throw error; 
      }
    }),
    provideAuth(() => {
      try {
        const auth = getAuth();
        
        // Use emulator in development if needed
        // if (location.hostname === 'localhost') {
        //   connectAuthEmulator(auth, 'http://localhost:9099');
        //   console.log('Connected to Auth emulator');
        // }
        
        return auth;
      } catch (error) {
        console.error('Failed to initialize Auth:', error);
        throw error;
      }
    }),
  ],
};
