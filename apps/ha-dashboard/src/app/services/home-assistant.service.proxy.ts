import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, of, timer } from 'rxjs';
import { catchError, map, switchMap, tap, retry } from 'rxjs/operators';

/**
 * Home Assistant entity interface representing a device or entity
 */
export interface HAEntity {
  entity_id: string;
  state: string;
  attributes: Record<string, any>;
  last_changed: string;
  last_updated: string;
  context: { id: string; parent_id?: string; user_id?: string };
}

/**
 * Home Assistant connection configuration
 */
export interface HAConfig {
  apiUrl: string;
  token: string;
}

/**
 * Service call parameters
 */
export interface HAServiceCall {
  domain: string;
  service: string;
  target?: {
    entity_id?: string | string[];
    device_id?: string | string[];
    area_id?: string | string[];
  };
  service_data?: Record<string, any>;
}

/**
 * Service call result
 */
export interface HAServiceCallResult {
  success: boolean;
  error?: string;
  response?: any;
}

/**
 * Service for interacting with the Home Assistant API through a Firebase Cloud Function proxy
 */
@Injectable({
  providedIn: 'root'
})
export class HomeAssistantService {
  private apiUrl: string | null = null;
  private token: string | null = null;
  private headers: HttpHeaders | undefined;
  
  // Observable to track connection status
  private connectionStatus = new BehaviorSubject<boolean>(false);
  public connectionStatus$ = this.connectionStatus.asObservable();
  
  // Observable to track entity states
  private entityStates = new BehaviorSubject<HAEntity[]>([]);
  public entityStates$ = this.entityStates.asObservable();
  
  // Polling interval (in milliseconds)
  private pollingInterval = 10000;
  private pollingSubscription: any = null;

  // Firebase Cloud Function URL
  private cloudFunctionUrl = 'https://your-region-your-project-id.cloudfunctions.net/haProxy';

  constructor(private http: HttpClient) {
    // Try to load configuration from local storage
    this.loadConfig();
  }
  
  /**
   * Check if Home Assistant API is configured
   */
  public isConfigured(): boolean {
    return !!(this.apiUrl && this.token);
  }
  
  /**
   * Configure the Home Assistant connection
   */
  public configure(config: HAConfig): void {
    this.apiUrl = config.apiUrl;
    this.token = config.token;
    
    // Store configuration in local storage
    localStorage.setItem('ha_config', JSON.stringify(config));
    
    // Set up headers with auth token
    this.headers = new HttpHeaders({
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json'
    });
    
    // Check connection and start polling if successful
    this.checkConnection();
  }
  
  /**
   * Load configuration from local storage
   */
  private loadConfig(): void {
    const savedConfig = localStorage.getItem('ha_config');
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig) as HAConfig;
        this.apiUrl = config.apiUrl;
        this.token = config.token;
        
        if (this.apiUrl && this.token) {
          this.headers = new HttpHeaders({
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
          });
        }
      } catch (e) {
        console.error('Error loading Home Assistant config from local storage:', e);
      }
    }
  }
  
  /**
   * Check connection to Home Assistant using the Cloud Function proxy
   */
  public checkConnection(): Observable<boolean> {
    if (!this.isConfigured()) {
      this.connectionStatus.next(false);
      return of(false);
    }
    
    // Use the check-connection endpoint in our Cloud Function
    return this.http.post<any>(`${this.cloudFunctionUrl}/check-connection`, {
      url: this.apiUrl,
      token: this.token
    }).pipe(
      map(response => {
        const connected = response.connected === true;
        this.connectionStatus.next(connected);
        
        // Start polling for entity states if connected
        if (connected && !this.pollingSubscription) {
          this.startPolling();
        }
        
        return connected;
      }),
      catchError(error => {
        console.error('Home Assistant connection error:', error);
        this.connectionStatus.next(false);
        return of(false);
      })
    );
  }
  
  /**
   * Start polling for entity states
   */
  private startPolling(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
    }
    
    this.pollingSubscription = timer(0, this.pollingInterval).pipe(
      switchMap(() => this.getStates())
    ).subscribe({
      next: states => this.entityStates.next(states),
      error: err => console.error('Error polling Home Assistant states:', err)
    });
  }
  
  /**
   * Stop polling
   */
  public stopPolling(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = null;
    }
  }
  
  /**
   * Get all states from Home Assistant
   */
  public getStates(): Observable<HAEntity[]> {
    if (!this.isConfigured()) {
      return of([]);
    }
    
    // Use the Cloud Function proxy for the API call
    return this.http.get<HAEntity[]>(
      `${this.cloudFunctionUrl}/api/states?url=${encodeURIComponent(this.apiUrl!)}`,
      { headers: this.headers }
    ).pipe(
      retry(3),
      catchError(error => {
        console.error('Error fetching Home Assistant states:', error);
        return of([]);
      })
    );
  }
  
  /**
   * Get a specific entity from Home Assistant
   */
  public getEntity(entityId: string): Observable<HAEntity | null> {
    if (!this.isConfigured()) {
      return of(null);
    }
    
    // Use the Cloud Function proxy for the API call
    return this.http.get<HAEntity>(
      `${this.cloudFunctionUrl}/api/states/${entityId}?url=${encodeURIComponent(this.apiUrl!)}`,
      { headers: this.headers }
    ).pipe(
      catchError(error => {
        console.error(`Error fetching Home Assistant entity ${entityId}:`, error);
        return of(null);
      })
    );
  }
  
  /**
   * Call a service in Home Assistant to control devices
   */
  public callService(params: HAServiceCall): Observable<HAServiceCallResult> {
    if (!this.isConfigured()) {
      return of({ success: false, error: 'Home Assistant API not configured' });
    }
    
    // Use the Cloud Function proxy for the API call
    const url = `${this.cloudFunctionUrl}/api/services/${params.domain}/${params.service}?url=${encodeURIComponent(this.apiUrl!)}`;
    const payload = {
      ...params.target ? { target: params.target } : {},
      ...params.service_data
    };
    
    return this.http.post<any>(url, payload, { headers: this.headers }).pipe(
      map(response => ({ success: true, response })),
      catchError(error => {
        console.error('Error calling Home Assistant service:', error);
        return of({ 
          success: false, 
          error: error.message || 'Failed to call service',
          response: error
        });
      })
    );
  }
} 