import { Injectable } from '@angular/core';
import { HttpC<PERSON>, HttpHead<PERSON>, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, BehaviorSubject, of, timer } from 'rxjs';
import { catchError, map, switchMap, retry, delay } from 'rxjs/operators';
import { environment } from '../environments/environment';

/**
 * Home Assistant entity interface representing a device or entity
 */
export interface HAEntity {
  entity_id: string;
  state: string;
  attributes: Record<string, any>;
  last_changed: string;
  last_updated: string;
  context: { id: string; parent_id?: string; user_id?: string };
}

/**
 * Home Assistant connection configuration
 */
export interface HAConfig {
  apiUrl: string;
  token: string;
}

/**
 * Home Assistant device interface
 */
export interface HADevice {
  id: string;
  name?: string;
  name_by_user?: string;
  model?: string;
  manufacturer?: string;
  identifiers?: string[];
  sw_version?: string;
  hw_version?: string;
  via_device_id?: string;
  area_id?: string;
  configuration_url?: string;
  disabled_by?: string;
  entry_type?: string;
  connections?: [string, string][];
}

/**
 * Home Assistant area interface
 */
export interface HAArea {
  area_id: string;
  name: string;
}

/**
 * Home Assistant API configuration interface
 */
export interface HASystemConfig {
  components: string[];
  config_dir: string;
  elevation: number;
  latitude: number;
  longitude: number;
  location_name: string;
  time_zone: string;
  unit_system: {
    length: string;
    mass: string;
    temperature: string;
    volume: string;
  };
  version: string;
}

/**
 * Home Assistant Service definition
 */
export interface HAServiceDefinition {
  name: string;
  description: string;
  fields: Record<string, {
    name: string;
    description: string;
    required?: boolean;
    example?: any;
    selector?: any;
  }>;
}

/**
 * Home Assistant Services by domain
 */
export interface HAServices {
  [domain: string]: {
    services: Record<string, HAServiceDefinition>;
  };
}

/**
 * Service call target
 */
export interface HAServiceTarget {
  entity_id?: string | string[];
  device_id?: string | string[];
  area_id?: string | string[];
}

/**
 * Service call parameters for controlling Home Assistant entities
 */
export interface HAServiceCall {
  domain: string;
  service: string;
  target?: HAServiceTarget;
  service_data?: Record<string, any>;
}

/**
 * Result of a service call to Home Assistant
 */
export interface HAServiceCallResult {
  success: boolean;
  error?: string;
  response?: any;
}

/**
 * Home Assistant history parameters
 */
export interface HAHistoryParams {
  entity_ids?: string | string[];
  start_time?: string;
  end_time?: string;
  minimal_response?: boolean;
  significant_changes_only?: boolean;
}

/**
 * Home Assistant API Error
 */
export interface HAApiError {
  error: string;
  details?: string | Record<string, any>;
}

/**
 * Connection status response
 */
export interface HAConnectionResponse {
  connected: boolean;
  message?: string;
  details?: any;
  error?: string;
}

/**
 * HTTP Method type
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

/**
 * Home Assistant history item
 */
export interface HAHistoryItem extends HAEntity {
  last_changed: string;
  last_updated: string;
}

/**
 * Home Assistant history response (array of arrays of history items)
 */
export type HAHistoryResponse = HAHistoryItem[][];

/**
 * Service for interacting with the Home Assistant API
 * Using Firebase Functions CORS proxy to prevent CORS issues
 */
@Injectable({
  providedIn: 'root'
})
export class HomeAssistantService {
  private apiUrl: string | null = null;
  private token: string | null = null;
  private headers: HttpHeaders | undefined;
  
  // Observable to track connection status
  private connectionStatus = new BehaviorSubject<boolean>(false);
  public connectionStatus$ = this.connectionStatus.asObservable();
  
  // Observable to track entity states
  private entityStates = new BehaviorSubject<HAEntity[]>([]);
  public entityStates$ = this.entityStates.asObservable();
  
  // Polling interval (in milliseconds)
  private pollingInterval = 10000;
  private pollingSubscription: any = null;

  // Firebase function URL for the cors proxy
  private proxyUrl = this.getProxyUrl();

  // Mock data for development testing
  private mockEntities: HAEntity[] = [
    {
      entity_id: 'light.living_room',
      state: 'on',
      attributes: {
        friendly_name: 'Living Room Light',
        brightness: 255,
        rgb_color: [255, 255, 255],
        supported_features: 63
      },
      last_changed: new Date().toISOString(),
      last_updated: new Date().toISOString(),
      context: { id: 'mock-context-id-1' }
    },
    {
      entity_id: 'switch.kitchen',
      state: 'off',
      attributes: {
        friendly_name: 'Kitchen Switch',
        supported_features: 0
      },
      last_changed: new Date().toISOString(),
      last_updated: new Date().toISOString(),
      context: { id: 'mock-context-id-2' }
    },
    {
      entity_id: 'sensor.temperature',
      state: '22.5',
      attributes: {
        friendly_name: 'Temperature Sensor',
        unit_of_measurement: '°C',
        device_class: 'temperature'
      },
      last_changed: new Date().toISOString(),
      last_updated: new Date().toISOString(),
      context: { id: 'mock-context-id-3' }
    },
    {
      entity_id: 'binary_sensor.motion',
      state: 'off',
      attributes: {
        friendly_name: 'Motion Sensor',
        device_class: 'motion'
      },
      last_changed: new Date().toISOString(),
      last_updated: new Date().toISOString(),
      context: { id: 'mock-context-id-4' }
    },
    {
      entity_id: 'climate.living_room',
      state: 'heat',
      attributes: {
        friendly_name: 'Living Room Thermostat',
        temperature: 21,
        target_temp_high: 24,
        target_temp_low: 18,
        hvac_modes: ['auto', 'heat', 'cool', 'off']
      },
      last_changed: new Date().toISOString(),
      last_updated: new Date().toISOString(),
      context: { id: 'mock-context-id-5' }
    }
  ];

  private mockDevices: HADevice[] = [
    {
      id: 'mock-device-1',
      name: 'Living Room Light',
      manufacturer: 'Philips',
      model: 'Hue Bulb',
      area_id: 'living_room'
    },
    {
      id: 'mock-device-2',
      name: 'Kitchen Switch',
      manufacturer: 'GE',
      model: 'Smart Switch',
      area_id: 'kitchen'
    },
    {
      id: 'mock-device-3',
      name: 'Temperature Sensor',
      manufacturer: 'Aqara',
      model: 'Temperature & Humidity Sensor',
      area_id: 'bedroom'
    },
    {
      id: 'mock-device-4',
      name: 'Motion Sensor',
      manufacturer: 'Aqara',
      model: 'Motion Sensor',
      area_id: 'hallway'
    },
    {
      id: 'mock-device-5',
      name: 'Living Room Thermostat',
      manufacturer: 'Nest',
      model: 'Learning Thermostat',
      area_id: 'living_room'
    }
  ];

  private mockAreas: HAArea[] = [
    { area_id: 'living_room', name: 'Living Room' },
    { area_id: 'kitchen', name: 'Kitchen' },
    { area_id: 'bedroom', name: 'Bedroom' },
    { area_id: 'hallway', name: 'Hallway' },
    { area_id: 'bathroom', name: 'Bathroom' }
  ];

  private mockConfig: HASystemConfig = {
    components: ['homeassistant', 'sensor', 'switch', 'light', 'climate', 'binary_sensor'],
    config_dir: '/config',
    elevation: 0,
    latitude: 37.7749,
    longitude: -122.4194,
    location_name: 'Home',
    time_zone: 'America/Los_Angeles',
    unit_system: {
      length: 'km',
      mass: 'kg',
      temperature: '°C',
      volume: 'L'
    },
    version: '2023.8.0'
  };

  private mockServices: HAServices = {
    light: {
      services: {
        turn_on: {
          name: 'Turn On',
          description: 'Turn on a light',
          fields: {}
        },
        turn_off: {
          name: 'Turn Off',
          description: 'Turn off a light',
          fields: {}
        },
        toggle: {
          name: 'Toggle',
          description: 'Toggle a light',
          fields: {}
        }
      }
    },
    switch: {
      services: {
        turn_on: {
          name: 'Turn On',
          description: 'Turn on a switch',
          fields: {}
        },
        turn_off: {
          name: 'Turn Off',
          description: 'Turn off a switch',
          fields: {}
        },
        toggle: {
          name: 'Toggle',
          description: 'Toggle a switch',
          fields: {}
        }
      }
    }
  };

  constructor(private http: HttpClient) {
    // Try to load configuration from local storage
    this.loadConfig();
  }
  
  /**
   * Get the Firebase Functions proxy URL
   */
  private getProxyUrl(): string {
    // Use our new corsProxy function for all API calls
    const projectId = environment.firebase.projectId;
    return `https://us-central1-${projectId}.cloudfunctions.net/corsProxy`;
  }
  
  /**
   * Check if Home Assistant API is configured
   */
  public isConfigured(): boolean {
    // Always return true when using mock data
    if (environment.useLocalMockData) {
      return true;
    }
    return !!(this.apiUrl && this.token);
  }
  
  /**
   * Get the base API URL, safe to use in other services
   */
  public getBaseApiUrl(): string | null {
    return this.apiUrl;
  }
  
  /**
   * Get the API URL for direct API access
   */
  public getApiUrl(): string {
    if (!this.apiUrl) {
      throw new Error('Home Assistant API URL is not configured');
    }
    return this.apiUrl;
  }
  
  /**
   * Get the access token for authenticated API calls
   */
  public getAccessToken(): string {
    if (!this.token) {
      throw new Error('Home Assistant access token is not configured');
    }
    return this.token;
  }
  
  /**
   * Make a request to Home Assistant API through the CORS proxy
   * or return mock data in development mode
   */
  private makeProxyRequest<T>(method: HttpMethod, endpoint: string, body?: any, params?: HttpParams): Observable<T> {
    if (!this.isConfigured()) {
      throw new Error('Home Assistant API not configured');
    }
    
    // If we're in development mode and using mock data, return mocks
    if (environment.useLocalMockData) {
      console.log(`Using mock data for ${endpoint}`);
      return this.getMockResponse<T>(endpoint, method, body).pipe(
        // Add a small delay to simulate network latency
        delay(300)
      );
    }
    
    // Ensure endpoint starts with a slash
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    
    // Ensure the endpoint has /api prefix if needed
    let apiPath = normalizedEndpoint;
    if (!apiPath.startsWith('/api/') && !apiPath.startsWith('/api')) {
      apiPath = `/api${normalizedEndpoint}`;
    }
    
    // Construct the full Home Assistant target URL
    const targetUrl = `${this.apiUrl}${apiPath}`;
    
    // Add query parameters if provided
    let queryString = '';
    if (params) {
      const paramsString = params.toString();
      if (paramsString) {
        queryString = `&${paramsString}`;
      }
    }
    
    // Create the proxy URL
    // The correct format is: proxyUrl?url=encodedFullHaUrl
    const proxyEndpoint = `${this.proxyUrl}?url=${encodeURIComponent(targetUrl)}${queryString}`;
    
    console.log('Making proxy request to:', proxyEndpoint);
    
    // Make the request to the proxy
    if (method === 'GET') {
      return this.http.get<T>(proxyEndpoint, { headers: this.headers }).pipe(
        catchError((error: HttpErrorResponse) => {
          console.error(`Error in Home Assistant proxy GET request to ${endpoint}:`, error);
          const apiError: HAApiError = {
            error: 'Failed to fetch data from Home Assistant',
            details: error.error || error.message
          };
          throw apiError;
        })
      );
    } else if (method === 'POST') {
      return this.http.post<T>(proxyEndpoint, body, { headers: this.headers }).pipe(
        catchError((error: HttpErrorResponse) => {
          console.error(`Error in Home Assistant proxy POST request to ${endpoint}:`, error);
          const apiError: HAApiError = {
            error: 'Failed to post data to Home Assistant',
            details: error.error || error.message
          };
          throw apiError;
        })
      );
    } else {
      // For other methods like PUT, DELETE, etc.
      return this.http.request<T>(method, proxyEndpoint, {
        body,
        headers: this.headers
      }).pipe(
        catchError((error: HttpErrorResponse) => {
          console.error(`Error in Home Assistant proxy ${method} request to ${endpoint}:`, error);
          const apiError: HAApiError = {
            error: `Failed to execute ${method} request to Home Assistant`,
            details: error.error || error.message
          };
          throw apiError;
        })
      );
    }
  }
  
  /**
   * Configure the Home Assistant connection
   */
  public configure(config: HAConfig): void {
    this.apiUrl = config.apiUrl;
    this.token = config.token;
    
    // Store configuration in local storage
    localStorage.setItem('ha_config', JSON.stringify(config));
    
    // Set up headers with auth token
    this.headers = new HttpHeaders({
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json'
    });
    
    // When using mock data in development, always set connection status to true
    if (environment.useLocalMockData) {
      this.connectionStatus.next(true);
      // Initialize entity states with mock data
      this.entityStates.next(this.mockEntities);
      return;
    }
    
    // Check connection and start polling if successful
    this.checkConnection();
  }
  
  /**
   * Load stored configuration from local storage
   */
  private loadConfig(): void {
    const storedConfig = localStorage.getItem('ha_config');
    if (storedConfig) {
      try {
        const config = JSON.parse(storedConfig) as HAConfig;
        this.configure(config);
      } catch (error) {
        console.error('Failed to parse stored Home Assistant configuration:', error);
      }
    }
  }
  
  /**
   * Check connection to Home Assistant
   */
  public checkConnection(): Observable<boolean> {
    if (!this.isConfigured()) {
      this.connectionStatus.next(false);
      return of(false);
    }
    
    return this.makeProxyRequest<HAConnectionResponse>('GET', '/').pipe(
      map(response => {
        const connected = response.connected !== false;
        this.connectionStatus.next(connected);
        
        // Start polling for entity states if connected
        if (connected && !this.pollingSubscription) {
          this.startPolling();
        }
        
        return connected;
      }),
      catchError((error: HAApiError) => {
        console.error('Home Assistant connection error:', error);
        this.connectionStatus.next(false);
        return of(false);
      })
    );
  }
  
  /**
   * Start polling for entity states
   */
  private startPolling(): void {
    // Clear any existing polling
    this.stopPolling();
    
    // Create a timer that emits at the specified interval
    this.pollingSubscription = timer(0, this.pollingInterval).pipe(
      switchMap(() => this.fetchAllEntities()),
      retry(3)
    ).subscribe({
      next: (entities) => {
        this.entityStates.next(entities);
      },
      error: (error) => {
        console.error('Error polling Home Assistant entities:', error);
      }
    });
  }
  
  /**
   * Stop polling for entity states
   */
  public stopPolling(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = null;
    }
  }
  
  /**
   * Fetch all entities from Home Assistant
   */
  public fetchAllEntities(): Observable<HAEntity[]> {
    if (!this.isConfigured()) {
      return of([]);
    }
    
    return this.makeProxyRequest<HAEntity[]>('GET', '/states').pipe(
      catchError((error: HAApiError) => {
        console.error('Error fetching Home Assistant entities:', error);
        return of([]);
      })
    );
  }
  
  /**
   * Get a specific entity by ID
   */
  public getEntity(entityId: string): Observable<HAEntity | null> {
    if (!this.isConfigured()) {
      return of(null);
    }
    
    return this.makeProxyRequest<HAEntity>('GET', `/states/${entityId}`).pipe(
      catchError((error: HAApiError) => {
        console.error(`Error fetching entity ${entityId}:`, error);
        return of(null);
      })
    );
  }
  
  /**
   * Call a service in Home Assistant to control devices
   */
  public callService(params: HAServiceCall): Observable<HAServiceCallResult> {
    if (!this.isConfigured()) {
      return of({ success: false, error: 'Home Assistant API not configured' });
    }
    
    const endpoint = `/services/${params.domain}/${params.service}`;
    const payload = {
      ...params.target ? { target: params.target } : {},
      ...params.service_data
    };
    
    return this.makeProxyRequest<unknown>('POST', endpoint, payload).pipe(
      map(response => ({ success: true, response })),
      catchError((error: HAApiError) => {
        console.error('Error calling Home Assistant service:', error);
        return of({ 
          success: false, 
          error: error.error || 'Failed to call service',
          response: error
        });
      })
    );
  }
  
  /**
   * Toggle a switch, light, or similar entity
   * @param entityId The entity ID to toggle
   * @returns Observable with service call result
   */
  public toggleEntity(entityId: string): Observable<HAServiceCallResult> {
    if (!entityId || !entityId.includes('.')) {
      return of({
        success: false,
        error: 'Invalid entity ID format. Expected domain.entity_id'
      });
    }
    
    const domain = entityId.split('.')[0];
    
    return this.callService({
      domain: domain,
      service: 'toggle',
      target: {
        entity_id: entityId
      }
    });
  }
  
  /**
   * Turn on an entity
   * @param entityId The entity ID to turn on
   * @param data Optional service data parameters
   * @returns Observable with service call result
   */
  public turnOn(entityId: string, data?: Record<string, any>): Observable<HAServiceCallResult> {
    if (!entityId || !entityId.includes('.')) {
      return of({
        success: false,
        error: 'Invalid entity ID format. Expected domain.entity_id'
      });
    }
    
    const domain = entityId.split('.')[0];
    
    return this.callService({
      domain: domain,
      service: 'turn_on',
      target: {
        entity_id: entityId
      },
      service_data: data
    });
  }
  
  /**
   * Turn off an entity
   * @param entityId The entity ID to turn off
   * @param data Optional service data parameters
   * @returns Observable with service call result
   */
  public turnOff(entityId: string, data?: Record<string, any>): Observable<HAServiceCallResult> {
    if (!entityId || !entityId.includes('.')) {
      return of({
        success: false,
        error: 'Invalid entity ID format. Expected domain.entity_id'
      });
    }
    
    const domain = entityId.split('.')[0];
    
    return this.callService({
      domain: domain,
      service: 'turn_off',
      target: {
        entity_id: entityId
      },
      service_data: data
    });
  }
  
  /**
   * Set the polling interval for entity updates
   */
  public setPollingInterval(interval: number): void {
    this.pollingInterval = interval;
    
    // Restart polling with new interval if already polling
    if (this.pollingSubscription) {
      this.startPolling();
    }
  }
  
  /**
   * Get the state of an entity by its ID
   */
  public getEntityState(entityId: string): Observable<string | null> {
    return this.entityStates$.pipe(
      map(entities => {
        const entity = entities.find(e => e.entity_id === entityId);
        return entity ? entity.state : null;
      })
    );
  }

  /**
   * Fetch all devices from Home Assistant
   */
  public fetchDevices(): Observable<HADevice[]> {
    if (!this.isConfigured()) {
      return of([]);
    }
    
    return this.makeProxyRequest<unknown>('GET', '/devices').pipe(
      map(response => {
        // Ensure response is an array
        if (Array.isArray(response)) {
          return response as HADevice[];
        } else if (response && typeof response === 'object') {
          // If it's an object but not an array, check if it's an error response
          if ('error' in response) {
            console.error('Error response from Home Assistant devices API:', response);
            return [];
          }
          
          // Try to convert object response to array if possible
          try {
            return Object.values(response) as HADevice[];
          } catch (err) {
            console.error('Failed to convert devices object to array:', err);
            return [];
          }
        }
        
        // Default fallback
        console.warn('Unexpected devices response format:', response);
        return [];
      }),
      catchError((error: HAApiError) => {
        console.error('Error fetching Home Assistant devices:', error);
        return of([]);
      })
    );
  }

  /**
   * Fetch all areas from Home Assistant
   */
  public fetchAreas(): Observable<HAArea[]> {
    if (!this.isConfigured()) {
      return of([]);
    }
    
    return this.makeProxyRequest<unknown>('GET', '/areas').pipe(
      map(response => {
        // Handle both array and object responses
        if (Array.isArray(response)) {
          return response as HAArea[];
        } else if (response && typeof response === 'object') {
          // If it's an object but not an array, check if it's an error response
          if ('error' in response) {
            console.error('Error response from Home Assistant areas API:', response);
            return [];
          }
          
          // Try to convert object response to array if possible
          try {
            return Object.values(response) as HAArea[];
          } catch (err) {
            console.error('Failed to convert areas object to array:', err);
            return [];
          }
        }
        // Default fallback
        console.warn('Unexpected areas response format:', response);
        return [];
      }),
      catchError((error: HAApiError) => {
        console.error('Error fetching Home Assistant areas:', error);
        return of([]);
      })
    );
  }

  /**
   * Fetch Home Assistant system configuration
   */
  public fetchConfig(): Observable<HASystemConfig | null> {
    if (!this.isConfigured()) {
      return of(null);
    }
    
    return this.makeProxyRequest<HASystemConfig>('GET', '/config').pipe(
      catchError((error: HAApiError) => {
        console.error('Error fetching Home Assistant configuration:', error);
        return of(null);
      })
    );
  }

  /**
   * Fetch all available services from Home Assistant
   */
  public fetchServices(): Observable<HAServices> {
    if (!this.isConfigured()) {
      return of({});
    }
    
    return this.makeProxyRequest<HAServices>('GET', '/services').pipe(
      catchError((error: HAApiError) => {
        console.error('Error fetching Home Assistant services:', error);
        return of({});
      })
    );
  }
  
  /**
   * Fetch entity history from Home Assistant
   */
  public fetchHistory(params: HAHistoryParams = {}): Observable<HAHistoryResponse> {
    if (!this.isConfigured()) {
      return of([]);
    }
    
    let httpParams = new HttpParams();
    
    if (params.entity_ids) {
      const entityIds = Array.isArray(params.entity_ids) 
        ? params.entity_ids.join(',') 
        : params.entity_ids;
      httpParams = httpParams.set('filter_entity_id', entityIds);
    }
    
    if (params.start_time) {
      httpParams = httpParams.set('start_time', params.start_time);
    }
    
    if (params.end_time) {
      httpParams = httpParams.set('end_time', params.end_time);
    }
    
    if (params.minimal_response !== undefined) {
      httpParams = httpParams.set('minimal_response', params.minimal_response.toString());
    }
    
    if (params.significant_changes_only !== undefined) {
      httpParams = httpParams.set('significant_changes_only', params.significant_changes_only.toString());
    }
    
    return this.makeProxyRequest<HAHistoryResponse>('GET', '/history/period', undefined, httpParams).pipe(
      catchError((error: HAApiError) => {
        console.error('Error fetching Home Assistant history:', error);
        return of([]);
      })
    );
  }
  
  /**
   * Fetch entity statistics from Home Assistant
   */
  public fetchStatistics(statistic_ids?: string | string[]): Observable<any> {
    if (!this.isConfigured()) {
      return of({});
    }
    
    let httpParams = new HttpParams();
    
    if (statistic_ids) {
      const ids = Array.isArray(statistic_ids) 
        ? statistic_ids.join(',') 
        : statistic_ids;
      httpParams = httpParams.set('statistic_ids', ids);
    }
    
    return this.makeProxyRequest<any>('GET', '/statistics', undefined, httpParams).pipe(
      catchError(error => {
        console.error('Error fetching Home Assistant statistics:', error);
        return of({});
      })
    );
  }
  
  /**
   * Get entities for a specific area
   */
  public getEntitiesForArea(areaId: string): Observable<HAEntity[]> {
    return this.entityStates$.pipe(
      map(entities => 
        entities.filter(entity => 
          entity.attributes && entity.attributes['area_id'] === areaId
        )
      )
    );
  }
  
  /**
   * Get devices for a specific area
   */
  public getDevicesForArea(areaId: string): Observable<HADevice[]> {
    return this.fetchDevices().pipe(
      map(devices => devices.filter(device => device.area_id === areaId))
    );
  }

  /**
   * Get mock response data for development and testing
   */
  private getMockResponse<T>(endpoint: string, method: HttpMethod, body?: any): Observable<T> {
    // Simplified endpoint for matching
    let simplifiedEndpoint = endpoint;
    if (simplifiedEndpoint.startsWith('/api/')) {
      simplifiedEndpoint = simplifiedEndpoint.substring(4);
    } else if (simplifiedEndpoint.startsWith('/')) {
      simplifiedEndpoint = simplifiedEndpoint.substring(1);
    }
    
    // Basic routing for mock data
    switch (simplifiedEndpoint) {
      case '':
      case '/':
        return of({ connected: true, message: 'API Running' } as unknown as T);
      
      case 'states':
        return of(this.mockEntities as unknown as T);
      
      case 'devices':
        return of(this.mockDevices as unknown as T);
      
      case 'areas':
        return of(this.mockAreas as unknown as T);
      
      case 'config':
        return of(this.mockConfig as unknown as T);
      
      case 'services':
        return of(this.mockServices as unknown as T);
      
      default:
        // Handle entity-specific requests
        if (simplifiedEndpoint.startsWith('states/')) {
          const entityId = simplifiedEndpoint.substring('states/'.length);
          const entity = this.mockEntities.find(e => e.entity_id === entityId);
          if (entity) {
            return of(entity as unknown as T);
          }
        }
        
        // Handle service calls
        if (simplifiedEndpoint.startsWith('services/') && method === 'POST') {
          // Return success for all service calls
          return of({ state: 'success' } as unknown as T);
        }
        
        // History endpoint
        if (simplifiedEndpoint.startsWith('history/period')) {
          return of([[...this.mockEntities]] as unknown as T);
        }
        
        console.warn('No mock data for endpoint:', endpoint);
        return of({} as T);
    }
  }
} 