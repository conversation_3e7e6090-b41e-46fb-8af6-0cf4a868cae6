import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class FloorplanLoaderService {
  constructor(private http: HttpClient) {}

  /**
   * Load SVG content from URL
   */
  loadSvgFromUrl(url: string): Observable<string> {
    return this.http.get(url, { responseType: 'text' })
      .pipe(
        catchError(error => {
          console.error('Error loading SVG:', error);
          return of('');
        })
      );
  }

  /**
   * Try loading floorplan from multiple possible paths
   */
  tryLoadingFromPaths(paths: string[]): Observable<{path: string, success: boolean}> {
    if (!paths.length) {
      return throwError(() => new Error('No paths available'));
    }

    const currentPath = paths[0];
    const cacheBuster = `?t=${new Date().getTime()}`;
    
    return this.http.get(currentPath + cacheBuster, { responseType: 'blob' })
      .pipe(
        map(() => ({ path: currentPath, success: true })),
        catchError(() => {
          if (paths.length > 1) {
            return this.tryLoadingFromPaths(paths.slice(1));
          }
          return of({ path: currentPath, success: false });
        })
      );
  }

  /**
   * Get possible floorplan paths in order of priority
   */
  getPossibleFloorplanPaths(currentPath: string): string[] {
    const possiblePaths: string[] = [];
    
    // Current path
    possiblePaths.push(currentPath);
    
    // Try root path (public folder) if not already using it
    if (currentPath !== '/floorplan.svg') {
      possiblePaths.push('/floorplan.svg');
    }
    
    // Legacy paths for backward compatibility
    if (!currentPath.includes('/assets/tiles/floorplan/')) {
      possiblePaths.push('/assets/tiles/floorplan/floorplan.svg');
    }
    
    if (!currentPath.startsWith('/assets/images/')) {
      possiblePaths.push('/assets/images/floorplan.svg');
    }
    
    if (!currentPath.startsWith('/images/')) {
      possiblePaths.push('/images/floorplan.svg');
    }
    
    return possiblePaths;
  }
}