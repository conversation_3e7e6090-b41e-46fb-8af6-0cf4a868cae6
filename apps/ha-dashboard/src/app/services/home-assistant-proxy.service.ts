import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, of, timer } from 'rxjs';
import { catchError, map, switchMap, tap, retry } from 'rxjs/operators';
import { environment } from '../environments/environment';

// Define environment interface to properly type the environment object
interface Environment {
  production: boolean;
  firebase: {
    apiKey: string;
    authDomain: string;
    databaseURL?: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
  };
  haProxyUrl: string;
  homeAssistantUrl: string;
}

// Cast environment to the interface type to ensure TypeScript recognizes the properties
const typedEnvironment = environment as Environment;

/**
 * Home Assistant entity interface representing a device or entity
 */
export interface HAEntity {
  entity_id: string;
  state: string;
  attributes: Record<string, any>;
  last_changed: string;
  last_updated: string;
  context: { id: string; parent_id?: string; user_id?: string };
}

/**
 * Home Assistant connection configuration
 */
export interface HAConfig {
  apiUrl: string;
  token: string;
}

/**
 * Service call parameters
 */
export interface HAServiceCall {
  domain: string;
  service: string;
  target?: {
    entity_id?: string | string[];
    device_id?: string | string[];
    area_id?: string | string[];
  };
  service_data?: Record<string, any>;
}

/**
 * Service call result
 */
export interface HAServiceCallResult {
  success: boolean;
  error?: string;
  response?: any;
}

/**
 * Service for interacting with the Home Assistant API through a Firebase Cloud Function proxy
 */
@Injectable({
  providedIn: 'root'
})
export class HomeAssistantProxyService {
  private apiUrl: string | null = null;
  private token: string | null = null;
  private headers: HttpHeaders | undefined;
  
  // Observable to track connection status
  private connectionStatus = new BehaviorSubject<boolean>(false);
  public connectionStatus$ = this.connectionStatus.asObservable();
  
  // Observable to track entity states
  private entityStates = new BehaviorSubject<HAEntity[]>([]);
  public entityStates$ = this.entityStates.asObservable();
  
  // Polling interval (in milliseconds)
  private pollingInterval = 10000;
  private pollingSubscription: any = null;

  constructor(private http: HttpClient) {
    // Try to load configuration from local storage
    this.loadConfig();
  }
  
  /**
   * Check if Home Assistant API is configured
   */
  public isConfigured(): boolean {
    return !!(this.apiUrl && this.token);
  }
  
  /**
   * Configure the Home Assistant connection
   */
  public configure(config: HAConfig): void {
    // If a configuration is provided, save it
    if (config) {
      // Use provided URL or fall back to environment default
      this.apiUrl = config.apiUrl || typedEnvironment.homeAssistantUrl || '';
      this.token = config.token;
      
      // Create headers with authentication token
      const headers = new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      });
      
      // Save configuration to local storage
      if (this.apiUrl) {
        localStorage.setItem('ha-api-url', this.apiUrl);
      }
      localStorage.setItem('ha-token', this.token);
      
      // Try to establish connection
      this.checkConnection().subscribe();
    }
  }
  
  /**
   * Load configuration from local storage
   */
  private loadConfig(): void {
    try {
      // Try to load URL and token from storage
      const apiUrl = localStorage.getItem('ha-api-url') || typedEnvironment.homeAssistantUrl || '';
      const token = localStorage.getItem('ha-token');
      
      if (apiUrl && token) {
        this.apiUrl = apiUrl;
        this.token = token;
        
        // Create headers with authentication token
        const headers = new HttpHeaders({
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        });
        
        // Check connection
        this.checkConnection().subscribe();
      } else {
        console.log('No saved Home Assistant configuration found');
      }
    } catch (error) {
      console.error('Error loading Home Assistant configuration:', error);
    }
  }
  
  /**
   * Check connection to Home Assistant
   */
  public checkConnection(): Observable<boolean> {
    // First check if we have API URL and token
    if (!this.isConfigured()) {
      console.warn('Cannot check connection: Home Assistant is not configured');
      return of(false);
    }
    
    console.log('Checking HA connection with token: ' + (this.token ? 'present' : 'not present'));
    
    // Create HTTP options for the request
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });
    
    // Make sure apiUrl is not null before using it in params
    const params = new HttpParams()
      .set('url', this.apiUrl || '');
    
    // Send test request to the proxy function
    return this.http.post<any>(
      `${typedEnvironment.haProxyUrl}/check-connection`,
      { apiUrl: this.apiUrl, token: this.token },
      { headers }
    ).pipe(
      map(response => {
        const success = response && response.connected === true;
        this.connectionStatus.next(success);
        
        // If connection is successful, start polling for entity states
        if (success) {
          this.startPolling();
        }
        
        return success;
      }),
      catchError(error => {
        console.error('Failed to connect to Home Assistant:', error);
        this.connectionStatus.next(false);
        return of(false);
      }),
      // Retry a few times for transient errors
      retry(2)
    );
  }
  
  /**
   * Start polling for entity states
   */
  private startPolling(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
    }
    
    this.pollingSubscription = timer(0, this.pollingInterval).pipe(
      switchMap(() => this.getStates())
    ).subscribe({
      next: states => this.entityStates.next(states),
      error: err => console.error('Error polling Home Assistant states:', err)
    });
  }
  
  /**
   * Stop polling
   */
  public stopPolling(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = null;
    }
  }
  
  /**
   * Get all entity states from Home Assistant
   */
  public getStates(): Observable<HAEntity[]> {
    if (!this.isConfigured()) {
      console.warn('Cannot get states: Home Assistant not configured');
      return of([]);
    }
    
    // Create headers with authentication token
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    });
    
    // Add URL as parameter
    const params = new HttpParams().set('url', this.apiUrl || '');
    
    return this.http.get<HAEntity[]>(`${typedEnvironment.haProxyUrl}/api/states`, { 
      headers: headers,
      params: params
    }).pipe(
      tap(entities => {
        // Update entities in our store
        this.entityStates.next(entities);
      }),
      catchError(error => {
        console.error('Error fetching Home Assistant states:', error);
        
        // If unauthorized, update connection status
        if (error.status === 401) {
          this.connectionStatus.next(false);
        }
        
        return of([]);
      })
    );
  }
  
  /**
   * Get a single entity by ID
   * @param entityId The entity ID to retrieve
   */
  public getEntity(entityId: string): Observable<HAEntity> {
    if (!this.isConfigured()) {
      console.warn('Cannot get entity: Home Assistant is not configured');
      return of({} as HAEntity);
    }
    
    // Create headers with authentication token
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    });
    
    // Use the Cloud Function proxy for the API call
    return this.http.get<HAEntity>(
      `${typedEnvironment.haProxyUrl}/api/states/${entityId}`,
      { 
        headers: headers,
        params: new HttpParams().set('url', this.apiUrl || '')
      }
    ).pipe(
      catchError(error => {
        console.error(`Error fetching entity ${entityId}:`, error);
        return of({} as HAEntity);
      })
    );
  }
  
  /**
   * Call a Home Assistant service
   * @param domain The domain of the service
   * @param service The service to call
   * @param targetEntity Optional entity to target
   * @param serviceData Optional data for the service call
   */
  public callService(
    domain: string, 
    service: string, 
    targetEntity?: string, 
    serviceData?: any
  ): Observable<any> {
    if (!this.isConfigured()) {
      console.warn('Cannot call service: Home Assistant is not configured');
      return of({ success: false, error: 'Not configured' });
    }
    
    // Create headers with authentication token
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    });
    
    // Add URL as parameter
    const params = new HttpParams().set('url', this.apiUrl || '');
    
    // Format the API endpoint
    const endpoint = `${typedEnvironment.haProxyUrl}/api/services/${domain}/${service}`;
    
    // Combine target and service_data for the request body
    const body: any = {
      ...serviceData || {}
    };
    
    // If a specific entity is targeted, add it to the body
    if (targetEntity) {
      body.entity_id = targetEntity;
    }
    
    // Make the service call
    return this.http.post(endpoint, body, { headers: headers, params: params }).pipe(
      map(response => {
        console.log(`Service ${domain}.${service} called successfully`);
        return { success: true, response };
      }),
      catchError(error => {
        console.error(`Error calling service ${domain}.${service}:`, error);
        return of({ success: false, error: error.message || 'Unknown error' });
      }),
      retry(1)
    );
  }
  
  /**
   * Toggle a switch, light, or similar entity
   */
  public toggleEntity(entityId: string): Observable<HAServiceCallResult> {
    const domain = entityId.split('.')[0];
    
    return this.callService(domain, 'toggle', entityId);
  }
  
  /**
   * Turn on an entity
   */
  public turnOn(entityId: string, data?: Record<string, any>): Observable<HAServiceCallResult> {
    const domain = entityId.split('.')[0];
    
    return this.callService(domain, 'turn_on', entityId, data);
  }
  
  /**
   * Turn off an entity
   */
  public turnOff(entityId: string, data?: Record<string, any>): Observable<HAServiceCallResult> {
    const domain = entityId.split('.')[0];
    
    return this.callService(domain, 'turn_off', entityId, data);
  }
} 