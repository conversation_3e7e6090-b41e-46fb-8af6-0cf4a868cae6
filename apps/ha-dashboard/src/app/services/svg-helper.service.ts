import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, map, of } from 'rxjs';
import { Point, Room } from '../models/dashboard.models';

/**
 * SVG element with geometry information
 */
export interface SvgRoomElement {
  id: string;
  actualId: string; // Without the "room-" prefix
  name: string;
  element: SVGElement;
  bbox: DOMRect;
  center: Point;
  points?: Point[]; // For polygon shapes
  type: Room['type'];
  // Visual properties
  fillColor?: string;
  strokeColor?: string;
  strokeWidth?: number;
}

/**
 * Detected wall information
 */
export interface WallElement {
  element: SVGElement;
  points: Point[];
  strokeWidth: number;
  strokeColor: string;
}

/**
 * Auto-detected room from visual analysis
 */
export interface DetectedRoom {
  id: string;
  name: string;
  type: Room['type'];
  points: Point[];
  boundingBox: DOMRect;
  center: Point;
  fillColor: string;
  confidence: number; // 0-100 indicating how confident we are this is a room
  uniqueId?: string; // Added to support unique identifiers in the UI
  isSelected?: boolean; // For UI selection
  color?: string; // For UI display
}

@Injectable({
  providedIn: 'root'
})
export class SvgHelperService {
  constructor(private http: HttpClient) {}

  /**
   * Load an SVG file from a URL and return it as a string
   * @param url The URL of the SVG file
   * @returns Observable with the SVG content as a string, or an empty string if there was an error
   */
  loadSvgFromUrl(url: string): Observable<string> {
    return this.http.get(url, { responseType: 'text' }).pipe(
      map(svgContent => this.optimizeSvgForScaling(svgContent)),
      catchError(error => {
        console.error(`Failed to load SVG from path: ${url}`, error);
        return of(''); // Return empty string on error
      })
    );
  }

  /**
   * Optimize SVG content for proper scaling within containers
   * @param svgString The SVG content as a string
   * @returns Optimized SVG content
   */
  optimizeSvgForScaling(svgString: string): string {
    if (!svgString) return svgString;

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const svg = doc.querySelector('svg');

    if (!svg) return svgString;

    // Ensure the SVG has proper scaling attributes
    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');

    // If no viewBox is set, create one from width/height
    if (!svg.getAttribute('viewBox')) {
      const width = svg.width?.baseVal?.value || 800;
      const height = svg.height?.baseVal?.value || 600;
      svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
    }

    // Remove fixed width/height to allow responsive scaling
    svg.removeAttribute('width');
    svg.removeAttribute('height');

    const serializer = new XMLSerializer();
    return serializer.serializeToString(doc);
  }

  /**
   * Modify an SVG element by adding a class to it
   * @param svgString The SVG content as a string
   * @param elementId The ID of the element to modify
   * @param className The class to add
   * @returns The modified SVG content
   */
  addClassToElement(svgString: string, elementId: string, className: string): string {
    if (!svgString || !elementId || !className) {
      return svgString;
    }

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const element = doc.getElementById(elementId);
    
    if (element) {
      element.classList.add(className);
      const serializer = new XMLSerializer();
      return serializer.serializeToString(doc);
    }
    
    return svgString;
  }

  /**
   * Highlight a specific element in an SVG by adding a highlight class
   * @param svgString The SVG content as a string
   * @param elementId The ID of the element to highlight
   * @returns The modified SVG content with the highlighted element
   */
  highlightElement(svgString: string, elementId: string): string {
    return this.addClassToElement(svgString, elementId, 'highlighted');
  }

  /**
   * Add click event listeners to specific elements in an SVG
   * This needs to be called after the SVG is added to the DOM
   * @param containerId The ID of the container holding the SVG
   * @param elementIds Array of element IDs to add click events to
   * @param callback Function to call when an element is clicked
   */
  addClickEvents(containerId: string, elementIds: string[], callback: (id: string) => void): void {
    const container = document.getElementById(containerId);
    if (!container) return;

    const svgElement = container.querySelector('svg');
    if (!svgElement) return;

    elementIds.forEach(id => {
      const element = svgElement.getElementById(id);
      if (element) {
        // Cast to SVGElement which can have style applied to it
        const svgEl = element as SVGElement;
        svgEl.style.cursor = 'pointer';
        element.addEventListener('click', () => callback(id));
      }
    });
  }

  /**
   * Extract unique room IDs from an SVG string
   * Assumes rooms have IDs starting with 'room-'
   * @param svgString The SVG content as a string
   * @returns Array of room IDs
   */
  extractRoomIds(svgString: string): string[] {
    if (!svgString) return [];

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const elements = doc.querySelectorAll('[id^="room-"]');
    
    return Array.from(elements).map(el => el.id);
  }

  /**
   * Modify the fill color of an SVG element
   * @param svgString The SVG content as a string
   * @param elementId The ID of the element to modify
   * @param color The new color (hex, rgb, etc.)
   * @returns The modified SVG content
   */
  setElementFill(svgString: string, elementId: string, color: string): string {
    if (!svgString || !elementId || !color) {
      return svgString;
    }

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const element = doc.getElementById(elementId);
    
    if (element) {
      element.setAttribute('fill', color);
      const serializer = new XMLSerializer();
      return serializer.serializeToString(doc);
    }
    
    return svgString;
  }

  /**
   * Extract detailed room information from SVG content
   * @param svgString The SVG content as a string
   * @returns Array of room elements with geometry information
   */
  extractRoomDetails(svgString: string): SvgRoomElement[] {
    if (!svgString) return [];

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const elements = doc.querySelectorAll('[id^="room-"]');
    const roomElements: SvgRoomElement[] = [];
    
    elements.forEach(el => {
      const element = el as SVGElement;
      const id = element.id;
      const actualId = id.replace('room-', '');
      const name = this.generateRoomName(actualId);
      
      // Extract points for polygon elements
      let points: Point[] | undefined;
      if (element.tagName.toLowerCase() === 'polygon' || element.tagName.toLowerCase() === 'polyline') {
        points = this.extractPolygonPoints(element as SVGPolygonElement);
      }
      
      // Get bounding box (with proper type casting for TypeScript)
      const svgGraphicsElement = element as unknown as SVGGraphicsElement;
      const bbox = svgGraphicsElement.getBBox();
      
      // Calculate center point
      const center: Point = {
        x: bbox.x + (bbox.width / 2),
        y: bbox.y + (bbox.height / 2)
      };
      
      // Determine room type based on name/id
      const type = this.determineRoomType(actualId, name);
      
      roomElements.push({
        id,
        actualId,
        name,
        element,
        bbox,
        center: this.normalizePoint(center, doc),
        points: points ? points.map(p => this.normalizePoint(p, doc)) : undefined,
        type
      });
    });
    
    return roomElements;
  }
  
  /**
   * Generate a human-readable room name from an ID
   */
  private generateRoomName(roomId: string): string {
    return roomId
      .replace(/[-_]/g, ' ')
      .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase());
  }
  
  /**
   * Determine room type based on ID and name
   */
  private determineRoomType(id: string, name: string): Room['type'] {
    const lowerName = name.toLowerCase();
    const lowerId = id.toLowerCase();
    
    if (lowerName.includes('kitchen') || lowerId.includes('kitchen')) {
      return 'kitchen';
    } else if (lowerName.includes('bathroom') || lowerName.includes('bath') || 
               lowerId.includes('bathroom') || lowerId.includes('bath')) {
      return 'bathroom';
    } else if (lowerName.includes('bedroom') || lowerName.includes('bed') || 
               lowerId.includes('bedroom') || lowerId.includes('bed')) {
      return 'bedroom';
    } else if (lowerName.includes('living') || lowerId.includes('living')) {
      return 'living';
    } else if (lowerName.includes('hall') || lowerId.includes('hall')) {
      return 'hallway';
    } else {
      return 'room';
    }
  }
  
  /**
   * Extract polygon points from a polygon element
   */
  private extractPolygonPoints(element: SVGPolygonElement): Point[] {
    const points: Point[] = [];
    const pointList = element.points;
    
    for (let i = 0; i < pointList.numberOfItems; i++) {
      const point = pointList.getItem(i);
      points.push({ x: point.x, y: point.y });
    }
    
    return points;
  }
  
  /**
   * Normalize a point's coordinates to percentages of SVG dimensions
   */
  private normalizePoint(point: Point, doc: Document): Point {
    const svg = doc.querySelector('svg');
    if (!svg) return point;
    
    // Get SVG dimensions
    const viewBox = svg.viewBox.baseVal;
    const width = viewBox.width || svg.width.baseVal.value;
    const height = viewBox.height || svg.height.baseVal.value;
    
    if (width === 0 || height === 0) return point;
    
    return {
      x: (point.x / width) * 100,
      y: (point.y / height) * 100
    };
  }

  /**
   * Advanced room detection that looks for walls (black lines) and areas
   * @param svgString The SVG content as a string
   * @returns Array of detected rooms
   */
  detectRoomsFromVisuals(svgString: string): DetectedRoom[] {
    if (!svgString) return [];

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const svg = doc.querySelector('svg');
    if (!svg) return [];

    // Get SVG dimensions
    const viewBox = svg.viewBox.baseVal;
    const width = viewBox.width || svg.width.baseVal.value;
    const height = viewBox.height || svg.height.baseVal.value;

    // Step 1: Detect walls (black or dark lines)
    const walls = this.detectWalls(doc);
    
    // Step 2: Detect existing filled areas that might be rooms
    const roomCandidates = this.detectRoomCandidates(doc);
    
    // Step 3: Generate room configurations from walls and areas
    return this.generateDetectedRooms(walls, roomCandidates, width, height);
  }

  /**
   * Detect wall elements (dark lines) in the SVG
   */
  private detectWalls(doc: Document): WallElement[] {
    const walls: WallElement[] = [];
    
    // Find paths and lines that could be walls (black/dark lines with specific properties)
    const pathElements = doc.querySelectorAll('path, line, polyline');
    
    pathElements.forEach(el => {
      const element = el as SVGElement;
      
      // Get the stroke properties
      const strokeColor = element.getAttribute('stroke') || 
                        window.getComputedStyle(element).stroke || 
                        '#000000';
      
      const strokeWidth = parseFloat(element.getAttribute('stroke-width') || 
                                   window.getComputedStyle(element).strokeWidth || 
                                   '1');
      
      // Check if this looks like a wall (dark color, visible width)
      if (this.isDarkColor(strokeColor) && strokeWidth > 0) {
        // Extract points from different element types
        let points: Point[] = [];
        
        if (element.tagName.toLowerCase() === 'line') {
          // For line elements
          const line = element as SVGLineElement;
          points = [
            { x: line.x1.baseVal.value, y: line.y1.baseVal.value },
            { x: line.x2.baseVal.value, y: line.y2.baseVal.value }
          ];
        } 
        else if (element.tagName.toLowerCase() === 'polyline') {
          // For polyline elements
          points = this.extractPolygonPoints(element as SVGPolylineElement);
        }
        else if (element.tagName.toLowerCase() === 'path') {
          // For path elements - approximate with points
          points = this.approximatePathPoints(element as SVGPathElement);
        }
        
        // Only add if we have valid points
        if (points.length > 1) {
          walls.push({
            element,
            points,
            strokeWidth,
            strokeColor
          });
        }
      }
    });
    
    return walls;
  }

  /**
   * Detect areas that could be rooms based on fills
   */
  private detectRoomCandidates(doc: Document): SvgRoomElement[] {
    const candidates: SvgRoomElement[] = [];
    
    // Find elements with fills that could be rooms
    const elements = doc.querySelectorAll('rect, circle, ellipse, polygon, path');
    
    elements.forEach((el, index) => {
      const element = el as SVGElement;
      
      // Get fill properties
      const fillColor = element.getAttribute('fill') || 
                      window.getComputedStyle(element).fill || 
                      'none';
      
      // Skip elements with no fill or transparent fill
      if (fillColor === 'none' || fillColor === 'transparent') {
        return;
      }
      
      // Skip very small elements
      const svgGraphicsElement = element as unknown as SVGGraphicsElement;
      const bbox = svgGraphicsElement.getBBox();
      
      if (bbox.width < 10 || bbox.height < 10) {
        return;
      }
      
      // Get element points for polygon/path types
      let points: Point[] | undefined;
      if (element.tagName.toLowerCase() === 'polygon') {
        points = this.extractPolygonPoints(element as SVGPolygonElement);
      }
      else if (element.tagName.toLowerCase() === 'path') {
        points = this.approximatePathPoints(element as SVGPathElement);
      }
      else if (element.tagName.toLowerCase() === 'rect') {
        const rect = element as SVGRectElement;
        points = [
          { x: rect.x.baseVal.value, y: rect.y.baseVal.value },
          { x: rect.x.baseVal.value + rect.width.baseVal.value, y: rect.y.baseVal.value },
          { x: rect.x.baseVal.value + rect.width.baseVal.value, y: rect.y.baseVal.value + rect.height.baseVal.value },
          { x: rect.x.baseVal.value, y: rect.y.baseVal.value + rect.height.baseVal.value }
        ];
      }
      
      // Get stroke properties
      const strokeColor = element.getAttribute('stroke') || 
                        window.getComputedStyle(element).stroke || 
                        'none';
      
      const strokeWidth = parseFloat(element.getAttribute('stroke-width') || 
                                   window.getComputedStyle(element).strokeWidth || 
                                   '0');
      
      // Calculate center point
      const center: Point = {
        x: bbox.x + (bbox.width / 2),
        y: bbox.y + (bbox.height / 2)
      };
      
      // Create a unique ID for this element
      const id = element.id || `detected-room-${index}`;
      const actualId = id.startsWith('room-') ? id.replace('room-', '') : id;
      
      // Determine room type based on fill color
      const roomType = this.determineRoomTypeFromColor(fillColor);
      
      candidates.push({
        id,
        actualId,
        name: this.generateRoomName(actualId),
        element,
        bbox,
        center: this.normalizePoint(center, doc),
        points: points ? points.map(p => this.normalizePoint(p, doc)) : undefined,
        type: roomType,
        fillColor,
        strokeColor,
        strokeWidth
      });
    });
    
    return candidates;
  }

  /**
   * Generate detected rooms from walls and room candidates
   */
  private generateDetectedRooms(
    walls: WallElement[], 
    candidates: SvgRoomElement[],
    svgWidth: number,
    svgHeight: number
  ): DetectedRoom[] {
    const detectedRooms: DetectedRoom[] = [];
    
    // First, add rooms from filled area candidates
    candidates.forEach((candidate, index) => {
      // Skip elements that are likely to be decorations, not rooms
      if (this.isLikelyDecoration(candidate)) {
        return;
      }
      
      // We need polygon points to create a room
      if (!candidate.points || candidate.points.length < 3) {
        return;
      }
      
      detectedRooms.push({
        id: `detected-${index}`,
        name: candidate.name || `Detected Room ${index + 1}`,
        type: candidate.type,
        points: candidate.points,
        boundingBox: candidate.bbox,
        center: candidate.center,
        fillColor: candidate.fillColor || '#ffffff',
        confidence: this.calculateRoomConfidence(candidate, walls)
      });
    });
    
    // TODO: In the future, we could try to detect rooms by identifying closed paths formed by walls
    // This would be a more advanced algorithm that traces wall segments to find enclosed areas
    
    return detectedRooms.filter(room => room.confidence > 40); // Only return rooms with decent confidence
  }

  /**
   * Check if a color is dark (likely to be a wall)
   */
  private isDarkColor(color: string): boolean {
    // Simple check for common dark colors
    if (!color || color === 'none' || color === 'transparent') {
      return false;
    }
    
    const darkColors = ['black', '#000', '#000000', 'rgb(0,0,0)', 'rgba(0,0,0'];
    if (darkColors.some(dc => color.includes(dc))) {
      return true;
    }
    
    // For hex colors, check if they're dark (implement proper color parsing in a real app)
    if (color.startsWith('#') && color.length >= 4) {
      // Simple brightness estimation for hex colors
      let r, g, b;
      
      if (color.length === 4) {
        // #RGB format
        r = parseInt(color[1] + color[1], 16);
        g = parseInt(color[2] + color[2], 16);
        b = parseInt(color[3] + color[3], 16);
      } else {
        // #RRGGBB format
        r = parseInt(color.substring(1, 3), 16);
        g = parseInt(color.substring(3, 5), 16);
        b = parseInt(color.substring(5, 7), 16);
      }
      
      // Calculate perceived brightness
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      return brightness < 80; // Dark enough to be a wall
    }
    
    // Default to false for unknown formats
    return false;
  }

  /**
   * Approximate points along a path
   */
  private approximatePathPoints(pathElement: SVGPathElement): Point[] {
    const points: Point[] = [];
    
    try {
      // Get the total length of the path
      const pathLength = pathElement.getTotalLength();
      
      // Sample points along the path
      const numPoints = Math.max(10, Math.floor(pathLength / 10)); // More samples for longer paths
      
      for (let i = 0; i < numPoints; i++) {
        const distance = (i / (numPoints - 1)) * pathLength;
        const point = pathElement.getPointAtLength(distance);
        points.push({ x: point.x, y: point.y });
      }
    } catch (e) {
      console.warn('Could not approximate path points', e);
    }
    
    return points;
  }

  /**
   * Determine room type based on fill color
   */
  private determineRoomTypeFromColor(fillColor: string): Room['type'] {
    // This is a very simple heuristic that could be improved
    if (!fillColor || fillColor === 'none' || fillColor === 'transparent') {
      return 'room';
    }
    
    // Convert to lowercase for easier matching
    const color = fillColor.toLowerCase();
    
    // Very basic color-based room type matching
    if (color.includes('blue') || color.includes('cyan') || color === '#0000ff' || color === '#00ffff') {
      return 'bathroom'; // Blue/cyan often represents bathrooms
    }
    
    if (color.includes('green') || color === '#00ff00') {
      return 'bedroom'; // Green might be bedrooms
    }
    
    if (color.includes('yellow') || color === '#ffff00') {
      return 'kitchen'; // Yellow could be kitchen
    }
    
    if (color.includes('brown') || color.includes('orange') || color === '#ffa500') {
      return 'living'; // Brown/orange could be living rooms
    }
    
    if (color.includes('gray') || color.includes('grey')) {
      return 'hallway'; // Grey often represents hallways
    }
    
    // Default for unknown colors
    return 'room';
  }

  /**
   * Check if an element is likely decorative (not a room)
   */
  private isLikelyDecoration(element: SvgRoomElement): boolean {
    // Very small elements are likely decorations
    if (element.bbox.width < 20 || element.bbox.height < 20) {
      return true;
    }
    
    // Perfectly round elements might be light fixtures or other decorations
    if (element.element.tagName.toLowerCase() === 'circle') {
      return true;
    }
    
    // Long thin rectangles are likely counters, tables, etc
    if (element.element.tagName.toLowerCase() === 'rect') {
      const aspectRatio = element.bbox.width / element.bbox.height;
      if (aspectRatio > 5 || aspectRatio < 0.2) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Calculate confidence that an element represents a room
   */
  private calculateRoomConfidence(element: SvgRoomElement, walls: WallElement[]): number {
    let confidence = 50; // Start with neutral confidence
    
    // Room-like elements get higher confidence
    if (element.element.tagName.toLowerCase() === 'rect' || 
        element.element.tagName.toLowerCase() === 'polygon') {
      confidence += 20;
    }
    
    // Larger areas are more likely to be rooms
    const area = element.bbox.width * element.bbox.height;
    if (area > 2000) confidence += 10;
    if (area > 5000) confidence += 10;
    
    // Elements with appropriate naming are likely rooms
    if (element.id.toLowerCase().includes('room') || 
        element.id.toLowerCase().includes('kitchen') ||
        element.id.toLowerCase().includes('bath') ||
        element.id.toLowerCase().includes('bed')) {
      confidence += 30;
    }
    
    // Elements bordered by walls are likely rooms
    // (simplified check - just see if there are walls nearby)
    if (this.hasWallsNearby(element, walls)) {
      confidence += 20;
    }
    
    // Cap confidence at 100
    return Math.min(confidence, 100);
  }

  /**
   * Check if an element has walls nearby (simplified)
   */
  private hasWallsNearby(element: SvgRoomElement, walls: WallElement[]): boolean {
    // Expand bounding box slightly to look for nearby walls
    const expandedBBox = {
      x: element.bbox.x - 5,
      y: element.bbox.y - 5,
      width: element.bbox.width + 10,
      height: element.bbox.height + 10
    };
    
    // Check if any wall points are within or near the bounding box
    return walls.some(wall => {
      return wall.points.some(point => {
        return point.x >= expandedBBox.x && 
               point.x <= expandedBBox.x + expandedBBox.width &&
               point.y >= expandedBBox.y && 
               point.y <= expandedBBox.y + expandedBBox.height;
      });
    });
  }
} 