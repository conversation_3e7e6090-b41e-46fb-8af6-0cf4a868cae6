import { Injectable, NgZone } from '@angular/core';
import { HomeAssistantService, HAEntity } from '../services/home-assistant.service';
import { FloorplanMarker, HADeviceMapping } from '../models/dashboard.models';
import { BehaviorSubject, Observable, Subscription, take } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class HAIntegrationService {
  private markersSubject = new BehaviorSubject<FloorplanMarker[]>([]);
  private connectedSubject = new BehaviorSubject<boolean>(false);
  private entitiesSubject = new BehaviorSubject<HAEntity[]>([]);
  
  private haSubscriptions = new Subscription();
  private refreshTimeout: ReturnType<typeof setTimeout> | null = null;

  constructor(
    private homeAssistantService: HomeAssistantService,
    private ngZone: NgZone
  ) {}

  get markers$(): Observable<FloorplanMarker[]> {
    return this.markersSubject.asObservable();
  }

  get connected$(): Observable<boolean> {
    return this.connectedSubject.asObservable();
  }

  get entities$(): Observable<HAEntity[]> {
    return this.entitiesSubject.asObservable();
  }

  initialize(haDevices?: HADeviceMapping[], refreshInterval?: number): void {
    // Clean up any existing subscriptions
    this.cleanup();
    
    // Check if Home Assistant service is configured
    if (!this.homeAssistantService.isConfigured() || !haDevices) {
      return;
    }
    
    // Monitor connection status
    const connectionSub = this.homeAssistantService.connectionStatus$.subscribe(connected => {
      this.connectedSubject.next(connected);
      
      if (connected) {
        this.fetchHomeAssistantDevices();
      }
    });
    
    // Get all entity states
    const entitiesSub = this.homeAssistantService.entityStates$.subscribe(entities => {
      if (entities && entities.length > 0) {
        this.entitiesSubject.next(entities);
        this.updateDeviceMarkers(haDevices);
      }
    });
    
    // Add subscriptions to be cleaned up on destroy
    this.haSubscriptions.add(connectionSub);
    this.haSubscriptions.add(entitiesSub);
    
    // Schedule regular refresh if specified
    if (refreshInterval && refreshInterval > 0) {
      this.scheduleDeviceRefresh(refreshInterval);
    }
  }

  cleanup(): void {
    this.haSubscriptions.unsubscribe();
    this.haSubscriptions = new Subscription();
    
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
      this.refreshTimeout = null;
    }
  }

  private fetchHomeAssistantDevices(): void {
    this.homeAssistantService.entityStates$.pipe(take(1)).subscribe((entities: HAEntity[]) => {
      if (entities && entities.length > 0) {
        this.entitiesSubject.next(entities);
      }
    });
  }

  private updateDeviceMarkers(haDevices: HADeviceMapping[]): void {
    const entities = this.entitiesSubject.value;
    if (!haDevices || !entities || entities.length === 0) {
      return;
    }
    
    // Generate markers from device mappings
    const generatedMarkers = haDevices
      .map(deviceMapping => {
        const entity = entities.find(e => e.entity_id === deviceMapping.entityId);
        return entity ? this.createMarkerFromEntity(entity, deviceMapping) : null;
      })
      .filter((marker): marker is FloorplanMarker => marker !== null);
    
    this.markersSubject.next(generatedMarkers);
  }

  private createMarkerFromEntity(entity: HAEntity, mapping: HADeviceMapping): FloorplanMarker | null {
    if (!entity || !mapping) return null;
    
    // Determine marker type from entity domain or mapping
    const domain = entity.entity_id.split('.')[0];
    const markerType = mapping.markerType || this.getMarkerTypeFromDomain(domain);
    
    // Determine position (required for marker)
    if (!mapping.position) {
      console.warn(`No position specified for entity ${entity.entity_id}`);
      return null;
    }
    
    // Create marker
    return {
      id: `ha-${entity.entity_id}`,
      x: mapping.position.x,
      y: mapping.position.y,
      type: markerType as any,
      label: mapping.showLabel ? entity.attributes['friendly_name'] || entity.entity_id : undefined,
      entityId: entity.entity_id,
      showState: mapping.showState || false,
      stateAttribute: mapping.stateAttribute,
      icon: mapping.icon || this.getIconForEntity(entity),
      color: mapping.color || this.getColorForEntityState(entity)
    };
  }

  private getMarkerTypeFromDomain(domain: string): string {
    switch (domain) {
      case 'light': return 'light';
      case 'switch': return 'switch';
      case 'sensor': return 'sensor';
      case 'binary_sensor': return 'sensor';
      case 'cover': return 'cover';
      case 'climate': return 'climate';
      case 'lock': return 'lock';
      default: return 'generic';
    }
  }

  private getIconForEntity(entity: HAEntity): string {
    const domain = entity.entity_id.split('.')[0];
    
    switch (domain) {
      case 'light': return 'fas fa-lightbulb';
      case 'switch': return 'fas fa-power-off';
      case 'sensor': return 'fas fa-thermometer-half';
      case 'binary_sensor': return 'fas fa-dot-circle';
      case 'cover': return 'fas fa-window-maximize';
      case 'climate': return 'fas fa-temperature-high';
      case 'lock': return entity.state === 'locked' ? 'fas fa-lock' : 'fas fa-lock-open';
      default: return 'fas fa-cube';
    }
  }

  private getColorForEntityState(entity: HAEntity): string {
    const domain = entity.entity_id.split('.')[0];
    const state = entity.state;
    
    switch (domain) {
      case 'light':
      case 'switch':
        return state === 'on' ? '#ffcc00' : '#888888';
      case 'lock':
        return state === 'locked' ? '#44cc44' : '#cc4444';
      case 'binary_sensor':
        return state === 'on' ? '#cc4444' : '#44cc44';
      default:
        return '#4477cc';
    }
  }

  private scheduleDeviceRefresh(interval: number): void {
    this.ngZone.runOutsideAngular(() => {
      this.refreshTimeout = setTimeout(() => {
        this.ngZone.run(() => {
          this.fetchHomeAssistantDevices();
          this.scheduleDeviceRefresh(interval);
        });
      }, interval);
    });
  }

  toggleEntity(entityId: string): void {
    const entity = this.entitiesSubject.value.find(e => e.entity_id === entityId);
    if (!entity) return;
    
    const domain = entityId.split('.')[0];
    const service = entity.state === 'on' ? 'turn_off' : 'turn_on';
    
    this.homeAssistantService.callService({
      domain: domain,
      service: service,
      target: {
        entity_id: entityId
      }
    })
    .pipe(take(1))
    .subscribe({
      next: () => {
        // Refresh entity status
        setTimeout(() => this.fetchHomeAssistantDevices(), 500);
      },
      error: (err) => {
        console.error(`Error toggling ${entityId}`, err);
      }
    });
  }

  controlCover(entityId: string, currentState: string): void {
    let service = '';
    
    if (currentState === 'open') {
      service = 'close_cover';
    } else {
      service = 'open_cover';
    }
    
    this.homeAssistantService.callService({
      domain: 'cover',
      service: service,
      target: {
        entity_id: entityId
      }
    })
    .pipe(take(1))
    .subscribe({
      next: () => {
        setTimeout(() => this.fetchHomeAssistantDevices(), 500);
      },
      error: (err) => {
        console.error('Error controlling cover', err);
      }
    });
  }

  controlLock(entityId: string, currentState: string): void {
    let service = '';
    
    if (currentState === 'locked') {
      service = 'unlock';
    } else {
      service = 'lock';
    }
    
    this.homeAssistantService.callService({
      domain: 'lock',
      service: service,
      target: {
        entity_id: entityId
      }
    })
    .pipe(take(1))
    .subscribe({
      next: () => {
        setTimeout(() => this.fetchHomeAssistantDevices(), 500);
      },
      error: (err) => {
        console.error('Error controlling lock', err);
      }
    });
  }
}