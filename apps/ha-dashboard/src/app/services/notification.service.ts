import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Notification {
  id: string;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  timestamp: Date;
  autoClose?: boolean;
  duration?: number;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notifications = new BehaviorSubject<Notification[]>([]);
  private timers = new Map<string, ReturnType<typeof setTimeout>>();
  
  constructor() {}
  
  getNotifications(): Observable<Notification[]> {
    return this.notifications.asObservable();
  }
  
  success(message: string, duration = 3000): string {
    return this.addNotification({
      message,
      type: 'success',
      autoClose: true,
      duration
    });
  }
  
  error(message: string, duration = 5000): string {
    return this.addNotification({
      message,
      type: 'error',
      autoClose: true,
      duration
    });
  }
  
  info(message: string, duration = 3000): string {
    return this.addNotification({
      message,
      type: 'info',
      autoClose: true,
      duration
    });
  }
  
  warning(message: string, duration = 4000): string {
    return this.addNotification({
      message,
      type: 'warning',
      autoClose: true,
      duration
    });
  }
  
  removeNotification(id: string): void {
    const currentNotifications = this.notifications.getValue();
    const updatedNotifications = currentNotifications.filter(n => n.id !== id);
    this.notifications.next(updatedNotifications);
    
    // Clear any associated timer
    if (this.timers.has(id)) {
      clearTimeout(this.timers.get(id)!);
      this.timers.delete(id);
    }
  }
  
  clearAll(): void {
    // Clear all timers
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
    
    // Clear all notifications
    this.notifications.next([]);
  }
  
  private addNotification(notification: Omit<Notification, 'id' | 'timestamp'>): string {
    const id = this.generateId();
    const timestamp = new Date();
    
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp
    };
    
    const currentNotifications = this.notifications.getValue();
    this.notifications.next([...currentNotifications, newNotification]);
    
    // Set up auto-close timer if needed
    if (newNotification.autoClose && newNotification.duration) {
      const timer = setTimeout(() => {
        this.removeNotification(id);
      }, newNotification.duration);
      
      this.timers.set(id, timer);
    }
    
    return id;
  }
  
  private generateId(): string {
    return `notification-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  }
} 