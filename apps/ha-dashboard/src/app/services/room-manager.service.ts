import { Injectable } from '@angular/core';
import { Room, Point } from '../models/dashboard.models';
import { SvgHelperService } from './svg-helper.service';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class RoomManagerService {
  private roomsSubject = new BehaviorSubject<{[id: string]: Room}>({});
  private selectedRoomSubject = new BehaviorSubject<Room | undefined>(undefined);
  private multiSelectModeSubject = new BehaviorSubject<boolean>(false);
  private selectedRoomsSubject = new BehaviorSubject<Map<string, Room>>(new Map());
  
  // Room colors based on type
  roomColors: Record<string, string> = {
    'room': 'rgba(var(--primary-color-rgb), 0.4)',
    'bathroom': 'rgba(77, 195, 236, 0.4)',
    'bedroom': 'rgba(139, 195, 74, 0.4)',
    'kitchen': 'rgba(255, 193, 7, 0.4)',
    'living': 'rgba(255, 152, 0, 0.4)',
    'hallway': 'rgba(189, 189, 189, 0.4)',
    'other': 'rgba(156, 39, 176, 0.4)'
  };

  constructor(private svgHelper: SvgHelperService) {}

  // Observable streams
  get rooms$(): Observable<{[id: string]: Room}> {
    return this.roomsSubject.asObservable();
  }

  get selectedRoom$(): Observable<Room | undefined> {
    return this.selectedRoomSubject.asObservable();
  }

  get multiSelectMode$(): Observable<boolean> {
    return this.multiSelectModeSubject.asObservable();
  }

  get selectedRooms$(): Observable<Map<string, Room>> {
    return this.selectedRoomsSubject.asObservable();
  }

  // Methods
  setRooms(rooms: {[id: string]: Room}): void {
    this.roomsSubject.next(rooms || {});
  }

  selectRoom(room: Room | undefined): void {
    this.selectedRoomSubject.next(room);
  }

  toggleMultiSelectMode(): void {
    const current = this.multiSelectModeSubject.value;
    this.multiSelectModeSubject.next(!current);
    
    // Clear selection when toggling off
    if (current) {
      this.clearRoomSelection();
    }
  }

  toggleRoomSelection(roomId: string, room: Room): void {
    const selectedRooms = new Map(this.selectedRoomsSubject.value);
    
    if (selectedRooms.has(roomId)) {
      selectedRooms.delete(roomId);
    } else {
      selectedRooms.set(roomId, room);
    }
    
    this.selectedRoomsSubject.next(selectedRooms);
  }

  clearRoomSelection(): void {
    this.selectedRoomsSubject.next(new Map());
  }

  getRoomColor(roomType: string): string {
    return this.roomColors[roomType] || this.roomColors['room'];
  }

  createRoomFromPolygon(roomId: string, name: string, type: Room['type'], description: string, points: Point[]): Room {
    return {
      id: roomId,
      name: name || this.getRoomName(roomId),
      imagePath: `/assets/images/rooms/${roomId}.png`,
      type: type || 'room',
      description: description || '',
      customPolygon: points
    };
  }

  getRoomName(roomId: string): string {
    // Convert room-living-room to Living Room
    const baseName = roomId.replace(/^room-/, '');
    return baseName
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  sanitizeForFirestore(obj: any): any {
    // Deep clone to avoid modifying the original
    const clone = JSON.parse(JSON.stringify(obj));
    
    // Convert Maps to objects
    if (clone instanceof Map) {
      return Object.fromEntries(clone);
    }
    
    // Process object properties
    if (clone && typeof clone === 'object') {
      Object.keys(clone).forEach(key => {
        // Remove undefined values
        if (clone[key] === undefined) {
          delete clone[key];
        } 
        // Recursively sanitize nested objects
        else if (typeof clone[key] === 'object' && clone[key] !== null) {
          clone[key] = this.sanitizeForFirestore(clone[key]);
        }
      });
    }
    
    return clone;
  }
}