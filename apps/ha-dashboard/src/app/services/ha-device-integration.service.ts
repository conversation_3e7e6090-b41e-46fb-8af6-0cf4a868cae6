import { Injectable } from '@angular/core';
import { Observable, map, of, take } from 'rxjs';
import { HomeAssistantService, HAEntity, HAServiceCallResult } from './home-assistant.service';

/**
 * Light entity control options
 */
export interface LightOptions {
  brightness?: number; // 0-255
  color_temp?: number; // mireds
  rgb_color?: [number, number, number]; // RGB values
  effect?: string; // Light effect
  transition?: number; // transition time in seconds
}

/**
 * Climate entity control options
 */
export interface ClimateOptions {
  temperature?: number;
  target_temp_high?: number;
  target_temp_low?: number;
  hvac_mode?: 'auto' | 'off' | 'heat' | 'cool' | 'heat_cool' | 'dry' | 'fan_only';
  preset_mode?: string;
}

/**
 * Sensor type mapping
 */
export interface SensorInfo {
  state: string;
  displayValue: string;
  unit?: string;
  batteryLevel?: number;
  lastUpdated: Date;
}

/**
 * Service for integrating with specific Home Assistant device types
 */
@Injectable({
  providedIn: 'root'
})
export class HaDeviceIntegrationService {

  constructor(private haService: HomeAssistantService) { }

  //#region LIGHTS

  /**
   * Get all light entities
   */
  getAllLights(): Observable<HAEntity[]> {
    return this.haService.entityStates$.pipe(
      map(entities => entities.filter(entity => entity.entity_id.startsWith('light.')))
    );
  }

  /**
   * Turn on a light
   */
  turnOnLight(entityId: string, options?: LightOptions): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'light',
      service: 'turn_on',
      target: {
        entity_id: entityId
      },
      service_data: options
    });
  }

  /**
   * Turn off a light
   */
  turnOffLight(entityId: string, transition?: number): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'light',
      service: 'turn_off',
      target: {
        entity_id: entityId
      },
      service_data: transition ? { transition } : undefined
    });
  }

  /**
   * Set brightness for a light (0-255)
   */
  setBrightness(entityId: string, brightness: number): Observable<HAServiceCallResult> {
    return this.turnOnLight(entityId, { brightness });
  }

  /**
   * Set color temperature for a light
   */
  setColorTemperature(entityId: string, colorTemp: number): Observable<HAServiceCallResult> {
    return this.turnOnLight(entityId, { color_temp: colorTemp });
  }

  /**
   * Set RGB color for a light
   */
  setRGBColor(entityId: string, r: number, g: number, b: number): Observable<HAServiceCallResult> {
    return this.turnOnLight(entityId, { rgb_color: [r, g, b] });
  }

  //#endregion

  //#region SENSORS

  /**
   * Get all sensor entities
   */
  getAllSensors(): Observable<HAEntity[]> {
    return this.haService.entityStates$.pipe(
      map(entities => entities.filter(entity => 
        entity.entity_id.startsWith('sensor.') || 
        entity.entity_id.startsWith('binary_sensor.')
      ))
    );
  }

  /**
   * Get sensors by specific domain (temperature, humidity, etc.)
   */
  getSensorsByDeviceClass(deviceClass: string): Observable<HAEntity[]> {
    return this.haService.entityStates$.pipe(
      map(entities => entities.filter(entity => 
        (entity.entity_id.startsWith('sensor.') || entity.entity_id.startsWith('binary_sensor.')) && 
        entity.attributes['device_class'] === deviceClass
      ))
    );
  }

  /**
   * Get formatted sensor information for display
   */
  getSensorInfo(entityId: string): Observable<SensorInfo | null> {
    return this.haService.getEntity(entityId).pipe(
      map(entity => {
        if (!entity) return null;

        return {
          state: entity.state,
          displayValue: this.formatSensorValue(entity),
          unit: entity.attributes['unit_of_measurement'],
          batteryLevel: entity.attributes['battery_level'],
          lastUpdated: new Date(entity.last_updated)
        };
      })
    );
  }

  /**
   * Format sensor value for display
   */
  private formatSensorValue(entity: HAEntity): string {
    if (!entity) return 'Unknown';

    const state = entity.state;
    const unit = entity.attributes['unit_of_measurement'];

    // For binary sensors
    if (entity.entity_id.startsWith('binary_sensor.')) {
      return state === 'on' ? 'Active' : 'Inactive';
    }

    // For numeric values, format based on device class
    if (!isNaN(Number(state))) {
      const numValue = Number(state);
      const deviceClass = entity.attributes['device_class'];

      switch (deviceClass) {
        case 'temperature':
          return `${numValue.toFixed(1)}${unit || '°'}`;
        case 'humidity':
          return `${numValue.toFixed(0)}${unit || '%'}`;
        case 'battery':
          return `${numValue.toFixed(0)}${unit || '%'}`;
        case 'pressure':
          return `${numValue.toFixed(0)}${unit || ' hPa'}`;
        default:
          return `${numValue}${unit ? ' ' + unit : ''}`;
      }
    }

    // Default formatting
    return `${state}${unit ? ' ' + unit : ''}`;
  }

  //#endregion

  //#region SWITCHES & OUTLETS

  /**
   * Get all switch entities
   */
  getAllSwitches(): Observable<HAEntity[]> {
    return this.haService.entityStates$.pipe(
      map(entities => entities.filter(entity => entity.entity_id.startsWith('switch.')))
    );
  }

  /**
   * Turn on a switch
   */
  turnOnSwitch(entityId: string): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'switch',
      service: 'turn_on',
      target: {
        entity_id: entityId
      }
    });
  }

  /**
   * Turn off a switch
   */
  turnOffSwitch(entityId: string): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'switch',
      service: 'turn_off',
      target: {
        entity_id: entityId
      }
    });
  }

  //#endregion

  //#region CLIMATE DEVICES

  /**
   * Get all climate entities (thermostats, etc.)
   */
  getAllClimateDevices(): Observable<HAEntity[]> {
    return this.haService.entityStates$.pipe(
      map(entities => entities.filter(entity => entity.entity_id.startsWith('climate.')))
    );
  }

  /**
   * Set climate device settings
   */
  setClimateSettings(entityId: string, options: ClimateOptions): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'climate',
      service: 'set_temperature',
      target: {
        entity_id: entityId
      },
      service_data: options
    });
  }

  /**
   * Set climate operation mode
   */
  setClimateMode(entityId: string, mode: ClimateOptions['hvac_mode']): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'climate',
      service: 'set_hvac_mode',
      target: {
        entity_id: entityId
      },
      service_data: {
        hvac_mode: mode
      }
    });
  }

  //#endregion

  //#region COVERS (Blinds, Garage Doors, etc.)

  /**
   * Get all cover entities
   */
  getAllCovers(): Observable<HAEntity[]> {
    return this.haService.entityStates$.pipe(
      map(entities => entities.filter(entity => entity.entity_id.startsWith('cover.')))
    );
  }

  /**
   * Open a cover
   */
  openCover(entityId: string): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'cover',
      service: 'open_cover',
      target: {
        entity_id: entityId
      }
    });
  }

  /**
   * Close a cover
   */
  closeCover(entityId: string): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'cover',
      service: 'close_cover',
      target: {
        entity_id: entityId
      }
    });
  }

  /**
   * Stop a cover's current action
   */
  stopCover(entityId: string): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'cover',
      service: 'stop_cover',
      target: {
        entity_id: entityId
      }
    });
  }

  /**
   * Set cover position (0-100)
   */
  setCoverPosition(entityId: string, position: number): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'cover',
      service: 'set_cover_position',
      target: {
        entity_id: entityId
      },
      service_data: {
        position
      }
    });
  }

  //#endregion

  //#region MEDIA PLAYERS

  /**
   * Get all media player entities
   */
  getAllMediaPlayers(): Observable<HAEntity[]> {
    return this.haService.entityStates$.pipe(
      map(entities => entities.filter(entity => entity.entity_id.startsWith('media_player.')))
    );
  }

  /**
   * Play media
   */
  playMedia(entityId: string): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'media_player',
      service: 'media_play',
      target: {
        entity_id: entityId
      }
    });
  }

  /**
   * Pause media
   */
  pauseMedia(entityId: string): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'media_player',
      service: 'media_pause',
      target: {
        entity_id: entityId
      }
    });
  }

  /**
   * Stop media
   */
  stopMedia(entityId: string): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'media_player',
      service: 'media_stop',
      target: {
        entity_id: entityId
      }
    });
  }

  /**
   * Set volume level (0-1)
   */
  setVolume(entityId: string, volume: number): Observable<HAServiceCallResult> {
    return this.haService.callService({
      domain: 'media_player',
      service: 'volume_set',
      target: {
        entity_id: entityId
      },
      service_data: {
        volume_level: volume
      }
    });
  }

  //#endregion

  //#region CAMERAS

  /**
   * Get all camera entities
   */
  getAllCameras(): Observable<HAEntity[]> {
    return this.haService.entityStates$.pipe(
      map(entities => entities.filter(entity => entity.entity_id.startsWith('camera.')))
    );
  }

  /**
   * Get camera snapshot URL (processed through Home Assistant)
   */
  getCameraSnapshotUrl(entityId: string): string | null {
    if (!this.haService.isConfigured() || !entityId) {
      return null;
    }
    
    // Get the base URL from the Home Assistant service
    const baseUrl = this.getHomeAssistantBaseUrl();
    if (!baseUrl) {
      return null;
    }
    
    // Construct the camera proxy URL
    return `${baseUrl}/camera_proxy/${entityId}`;
  }
  
  /**
   * Helper method to get the Home Assistant base URL
   * This avoids directly accessing private properties
   */
  private getHomeAssistantBaseUrl(): string | null {
    // Use the proper method to get the base URL
    return this.haService.getBaseApiUrl();
  }

  //#endregion

  //#region UTILITY METHODS

  /**
   * Group entities by room or area
   */
  groupEntitiesByArea(): Observable<Record<string, HAEntity[]>> {
    return this.haService.entityStates$.pipe(
      map(entities => {
        const groupedEntities: Record<string, HAEntity[]> = {};
        
        entities.forEach(entity => {
          const area = entity.attributes['friendly_name']?.split(' ')[0] || 'Ungrouped';
          if (!groupedEntities[area]) {
            groupedEntities[area] = [];
          }
          groupedEntities[area].push(entity);
        });
        
        return groupedEntities;
      })
    );
  }

  /**
   * Get entities by type for a specific area
   */
  getEntitiesByTypeForArea(area: string, type: string): Observable<HAEntity[]> {
    return this.haService.entityStates$.pipe(
      map(entities => 
        entities.filter(entity => 
          entity.entity_id.startsWith(`${type}.`) && 
          (entity.attributes['friendly_name']?.includes(area) || false)
        )
      )
    );
  }

  /**
   * Gets active devices that are currently "on" or "open"
   */
  getActiveDevices(): Observable<HAEntity[]> {
    return this.haService.entityStates$.pipe(
      map(entities => 
        entities.filter(entity => 
          (entity.entity_id.startsWith('light.') || 
           entity.entity_id.startsWith('switch.') ||
           entity.entity_id.startsWith('climate.') ||
           entity.entity_id.startsWith('fan.')) && 
          ['on', 'open', 'heating', 'cooling'].includes(entity.state)
        )
      )
    );
  }

  //#endregion
} 