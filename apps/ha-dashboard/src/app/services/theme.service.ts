import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export type Theme = 'light' | 'dark';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'ha-dashboard-theme';
  private darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  // BehaviorSubject to track current theme
  private themeSubject = new BehaviorSubject<Theme>(this.getInitialTheme());
  public theme$ = this.themeSubject.asObservable();
  
  constructor() {
    // Listen for system theme changes
    this.darkModeMediaQuery.addEventListener('change', (e) => {
      if (this.isUsingSystemTheme()) {
        this.setTheme(e.matches ? 'dark' : 'light', true);
      }
    });
    
    // Apply initial theme
    this.applyTheme(this.themeSubject.value);
  }
  
  /**
   * Get the initial theme based on:
   * 1. Saved user preference
   * 2. System preference if no user preference exists
   */
  private getInitialTheme(): Theme {
    const savedTheme = localStorage.getItem(this.THEME_KEY);
    
    if (savedTheme === 'light' || savedTheme === 'dark') {
      return savedTheme;
    }
    
    // If no saved preference, use system preference
    return this.darkModeMediaQuery.matches ? 'dark' : 'light';
  }
  
  /**
   * Check if the current theme is based on system settings
   */
  public isUsingSystemTheme(): boolean {
    return localStorage.getItem(this.THEME_KEY) === null;
  }
  
  /**
   * Toggle between light and dark themes
   */
  public toggleTheme(): void {
    const currentTheme = this.themeSubject.value;
    const newTheme: Theme = currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }
  
  /**
   * Set theme to light, dark, or system
   */
  public setTheme(theme: Theme, isSystemChange = false): void {
    // If it's a manual change (not system), save it to localStorage
    if (!isSystemChange) {
      localStorage.setItem(this.THEME_KEY, theme);
    }
    
    this.themeSubject.next(theme);
    this.applyTheme(theme);
  }
  
  /**
   * Reset to system theme
   */
  public useSystemTheme(): void {
    localStorage.removeItem(this.THEME_KEY);
    const systemTheme: Theme = this.darkModeMediaQuery.matches ? 'dark' : 'light';
    this.themeSubject.next(systemTheme);
    this.applyTheme(systemTheme);
  }
  
  /**
   * Get current theme
   */
  public getCurrentTheme(): Theme {
    return this.themeSubject.value;
  }
  
  /**
   * Apply theme to document
   */
  private applyTheme(theme: Theme): void {
    // Set data-theme attribute on html element (for CSS variables)
    document.documentElement.setAttribute('data-theme', theme);
    
    // Add or remove theme class from body
    if (theme === 'dark') {
      document.body.classList.add('dark-theme');
      document.body.classList.add('mat-app-background');
      document.body.classList.add('mat-typography');
      
      // Add material dark theme class for Angular Material components
      document.body.classList.add('mat-dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
      document.body.classList.remove('mat-dark-theme');
      document.body.classList.add('mat-app-background');
      document.body.classList.add('mat-typography');
    }
    
    // Update meta theme-color for browser UI
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', 
        theme === 'dark' ? '#121212' : '#f5f5f5'
      );
    }
  }
} 