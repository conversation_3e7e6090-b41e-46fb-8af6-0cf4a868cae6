/**
 * Development environment configuration
 */
export const environment = {
  production: false,
  
  // Firebase configuration
  firebase: {
    apiKey: "test-api-key",
    authDomain: "test-project-id.firebaseapp.com", 
    projectId: "test-project-id",
    storageBucket: "test-project-id.appspot.com",
    messagingSenderId: "123456789",
    appId: "1:123456789:web:abcdef123456789"
  },
  
  // Using a mock proxy for testing
  // For local development, we'll just bypass the proxy
  useLocalMockData: true,
  
  // For production 
  haProxyUrl: "https://us-central1-test-project-id.cloudfunctions.net/corsProxy",
  
  // Mock Home Assistant URL for testing
  homeAssistantUrl: "http://localhost:8123"
}; 