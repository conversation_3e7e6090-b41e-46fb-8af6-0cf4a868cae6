import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { LoadingSpinnerComponent } from './components/loading-spinner/loading-spinner.component';
import { EmptyStateComponent } from './components/empty-state/empty-state.component';
import { HamburgerMenuComponent } from './components/hamburger-menu/hamburger-menu.component';
import { FloorplanUploaderComponent } from './components/floorplan-uploader/floorplan-uploader.component';
import { RoomDetailComponent } from './components/room-detail/room-detail.component';
import { HAConfigComponent } from './components/ha-config/ha-config.component';
import { SafeHtmlPipe } from './pipes/safe-html.pipe';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatProgressSpinnerModule,
    // Import standalone components
    LoadingSpinnerComponent,
    EmptyStateComponent,
    HamburgerMenuComponent,
    FloorplanUploaderComponent,
    RoomDetailComponent,
    HAConfigComponent,
    SafeHtmlPipe
  ],
  exports: [
    // Export all components and pipes
    LoadingSpinnerComponent,
    EmptyStateComponent,
    HamburgerMenuComponent,
    FloorplanUploaderComponent,
    RoomDetailComponent,
    HAConfigComponent,
    SafeHtmlPipe,
    // Re-export common modules
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatProgressSpinnerModule
  ]
})
export class SharedModule { } 