import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MatDialogModule,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { MatTabsModule } from '@angular/material/tabs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { SafeHtmlPipe } from '../../pipes/safe-html.pipe';
import { DetectedRoom } from '../../../services/svg-helper.service';

interface RoomSelectionDialogData {
  detectedRooms: DetectedRoom[];
  existingRoomCount: number;
  mode: 'visual' | 'id';
  svgContent: string;
}

@Component({
  selector: 'app-room-selection-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatCheckboxModule,
    MatTabsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    SafeHtmlPipe,
  ],
  template: `
    <h2 mat-dialog-title>Room Detection Results</h2>
    <div mat-dialog-content>
      <div class="detection-summary">
        <div class="summary-text">
          <p *ngIf="data.existingRoomCount > 0">
            You already have {{ data.existingRoomCount }} existing room(s)
            configured.
          </p>
          <p>
            {{
              data.mode === 'visual' ? 'Visual analysis' : 'ID-based detection'
            }}
            found {{ data.detectedRooms.length }} new room(s).
          </p>
        </div>

        <div class="selection-actions">
          <button mat-button (click)="selectAll()">Select All</button>
          <button mat-button (click)="selectNone()">Select None</button>
        </div>
      </div>

      <mat-tab-group>
        <mat-tab label="Room Preview">
          <div class="preview-container">
            <div class="svg-preview" [innerHTML]="previewSvg | safeHtml"></div>
            <div class="preview-legend">
              <h4>Room Types</h4>
              <div class="legend-item" *ngFor="let type of uniqueTypes">
                <div
                  class="color-sample"
                  [style.background-color]="getColorForType(type)"
                ></div>
                <span>{{ type | titlecase }}</span>
              </div>
            </div>
          </div>
        </mat-tab>

        <mat-tab label="Room List">
          <div class="rooms-list">
            <div *ngFor="let room of data.detectedRooms" class="room-item">
              <div class="room-checkbox">
                <mat-checkbox [(ngModel)]="room.isSelected">
                  <span class="room-name">{{ room.name }}</span>
                </mat-checkbox>
              </div>

              <div class="room-details" *ngIf="room.isSelected">
                <mat-form-field appearance="outline">
                  <mat-label>Name</mat-label>
                  <input matInput [(ngModel)]="room.name" />
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Room Type</mat-label>
                  <mat-select [(ngModel)]="room.type">
                    <mat-option value="room">Generic Room</mat-option>
                    <mat-option value="living">Living Room</mat-option>
                    <mat-option value="kitchen">Kitchen</mat-option>
                    <mat-option value="bedroom">Bedroom</mat-option>
                    <mat-option value="bathroom">Bathroom</mat-option>
                    <mat-option value="hallway">Hallway</mat-option>
                    <mat-option value="other">Other</mat-option>
                  </mat-select>
                </mat-form-field>

                <div class="room-stats">
                  <span
                    class="confidence"
                    [class.high]="room.confidence > 70"
                    [class.medium]="
                      room.confidence > 40 && room.confidence <= 70
                    "
                    [class.low]="room.confidence <= 40"
                  >
                    Confidence: {{ room.confidence }}%
                  </span>

                  <span class="points-count" *ngIf="room.points">
                    {{ room.points.length }} points
                  </span>
                </div>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>

    <div mat-dialog-actions>
      <button mat-button (click)="dialogRef.close()">Cancel</button>
      <button
        mat-button
        color="primary"
        [disabled]="selectedRoomCount === 0"
        (click)="addSelectedRooms()"
      >
        Add {{ selectedRoomCount }} Room{{ selectedRoomCount !== 1 ? 's' : '' }}
      </button>
    </div>
  `,
  styles: [
    `
      .detection-summary {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      .selection-actions {
        display: flex;
        gap: 8px;
      }

      .preview-container {
        display: flex;
        flex-direction: row;
        gap: 16px;
        margin-top: 16px;
        height: 400px;
      }

      .svg-preview {
        flex: 1;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        overflow: hidden;
        background-color: #f5f5f5;
      }

      .svg-preview ::ng-deep svg {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .preview-legend {
        width: 150px;
        padding: 8px;
        background-color: #f9f9f9;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
      }

      .preview-legend h4 {
        margin-top: 0;
        margin-bottom: 12px;
        font-size: 14px;
        color: #333;
      }

      .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }

      .color-sample {
        width: 16px;
        height: 16px;
        border-radius: 4px;
        margin-right: 8px;
        border: 1px solid rgba(0, 0, 0, 0.2);
      }

      .rooms-list {
        margin-top: 16px;
        max-height: 400px;
        overflow-y: auto;
      }

      .room-item {
        border-bottom: 1px solid #f0f0f0;
        padding: 8px 0;
      }

      .room-checkbox {
        margin-bottom: 8px;
      }

      .room-details {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-left: 24px;
        padding-top: 8px;
      }

      .room-details mat-form-field {
        flex: 1;
        min-width: 200px;
      }

      .room-stats {
        display: flex;
        width: 100%;
        justify-content: space-between;
        font-size: 0.85em;
        color: #666;
        margin-top: 4px;
      }

      .confidence.high {
        color: #4caf50;
      }

      .confidence.medium {
        color: #ff9800;
      }

      .confidence.low {
        color: #f44336;
      }
      
      /* Dropdown theming styles */
      ::ng-deep .mat-mdc-select-panel {
        background-color: var(--background-secondary) !important;
      }
      
      ::ng-deep .mat-mdc-option {
        color: var(--text-color) !important;
      }
      
      ::ng-deep .mat-mdc-option .mdc-list-item__primary-text {
        color: var(--text-color) !important;
      }
      
      ::ng-deep .mat-mdc-option:hover:not(.mdc-list-item--disabled) {
        background-color: rgba(var(--primary-color-rgb), 0.1) !important;
      }
      
      ::ng-deep .mat-mdc-option.mat-mdc-option-active {
        background-color: rgba(var(--primary-color-rgb), 0.2) !important;
      }
      
      ::ng-deep .mat-primary .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after {
        color: var(--primary-color) !important;
      }
    `,
  ],
})
export class RoomSelectionDialogComponent implements OnInit {
  previewSvg: string = '';
  uniqueTypes: string[] = [];

  get selectedRoomCount(): number {
    return this.data.detectedRooms.filter((room) => room.isSelected).length;
  }

  constructor(
    public dialogRef: MatDialogRef<RoomSelectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: RoomSelectionDialogData
  ) {}

  ngOnInit(): void {
    // Create a set of unique room types
    this.uniqueTypes = Array.from(
      new Set(this.data.detectedRooms.map((room) => room.type))
    );

    // Generate SVG preview with highlighted rooms
    this.generateSvgPreview();
  }

  /**
   * Generate SVG preview with detected rooms highlighted
   */
  private generateSvgPreview(): void {
    if (!this.data.svgContent) {
      return;
    }

    // Parse the SVG
    const parser = new DOMParser();
    const doc = parser.parseFromString(this.data.svgContent, 'image/svg+xml');
    const svg = doc.querySelector('svg');

    if (!svg) {
      return;
    }

    // Add detected room polygons to the SVG
    this.data.detectedRooms.forEach((room, index) => {
      if (!room.points || room.points.length < 3) {
        return;
      }

      // Create a unique ID for this preview room
      const id = `preview-room-${index}`;

      // Create polygon element
      const polygon = document.createElementNS(
        'http://www.w3.org/2000/svg',
        'polygon'
      );
      polygon.setAttribute('id', id);
      polygon.setAttribute(
        'points',
        room.points.map((p) => `${p.x},${p.y}`).join(' ')
      );
      polygon.setAttribute(
        'fill',
        room.color || this.getColorForType(room.type)
      );
      polygon.setAttribute('fill-opacity', '0.5');
      polygon.setAttribute('stroke', '#333');
      polygon.setAttribute('stroke-width', '1');
      polygon.setAttribute(
        'stroke-dasharray',
        room.isSelected ? 'none' : '4,4'
      );

      // Add the room name as text above the room
      const text = document.createElementNS(
        'http://www.w3.org/2000/svg',
        'text'
      );
      text.setAttribute('x', room.center.x.toString());
      text.setAttribute('y', room.center.y.toString());
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('font-size', '10');
      text.setAttribute('fill', '#000');
      text.setAttribute('stroke', '#fff');
      text.setAttribute('stroke-width', '0.5');
      text.setAttribute('paint-order', 'stroke');
      text.textContent = room.name;

      // Add elements to SVG
      svg.appendChild(polygon);
      svg.appendChild(text);
    });

    // Convert back to string
    const serializer = new XMLSerializer();
    this.previewSvg = serializer.serializeToString(doc);
  }

  /**
   * Get a color for a room type
   */
  getColorForType(type: string): string {
    switch (type) {
      case 'bathroom':
        return '#77c3ec'; // Light blue
      case 'bedroom':
        return '#8bc34a'; // Light green
      case 'kitchen':
        return '#ffeb3b'; // Yellow
      case 'living':
        return '#ff9800'; // Orange
      case 'hallway':
        return '#bdbdbd'; // Gray
      default:
        return '#e1e1e1'; // Light gray for generic rooms
    }
  }

  /**
   * Select all rooms
   */
  selectAll(): void {
    this.data.detectedRooms.forEach((room) => {
      room.isSelected = true;
    });

    this.generateSvgPreview();
  }

  /**
   * Deselect all rooms
   */
  selectNone(): void {
    this.data.detectedRooms.forEach((room) => {
      room.isSelected = false;
    });

    this.generateSvgPreview();
  }

  /**
   * Add the selected rooms
   */
  addSelectedRooms(): void {
    const selectedRooms = this.data.detectedRooms.filter(
      (room) => room.isSelected
    );

    this.dialogRef.close({
      selectedRooms: selectedRooms,
    });
  }
}
