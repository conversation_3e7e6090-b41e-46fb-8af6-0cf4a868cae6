import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SafeHtmlPipe } from '../../pipes/safe-html.pipe';

@Component({
  selector: 'app-floorplan-uploader',
  standalone: true,
  imports: [CommonModule, FormsModule, SafeHtmlPipe],
  template: `
    <div class="floorplan-uploader">
      <div class="uploader-header">
        <h3>{{ title }}</h3>
      </div>
      
      <div class="uploader-content">
        <div class="file-drop-area" 
          [class.drag-over]="isDragging"
          (dragover)="onDragOver($event)"
          (dragleave)="onDragLeave($event)"
          (drop)="onDrop($event)">
          
          <input type="file" 
            #fileInput
            [accept]="acceptedFileTypes" 
            (change)="onFileSelected($event)" 
            style="display: none">
            
          <div class="drop-message">
            <i class="fa fa-upload"></i>
            <p>Drag & drop your floorplan file here</p>
            <p class="file-types">Accepted formats: {{ acceptedFileTypes }}</p>
            <button class="browse-button" (click)="fileInput.click()">Browse Files</button>
          </div>
        </div>
        
        <div *ngIf="selectedFile" class="selected-file">
          <div class="file-info">
            <span class="file-name">{{ selectedFile.name }}</span>
            <span class="file-size">{{ formatFileSize(selectedFile.size) }}</span>
          </div>
          
          <div class="file-actions">
            <button class="upload-button" [disabled]="isUploading" (click)="uploadFile()">
              <i class="fa" [ngClass]="isUploading ? 'fa-spinner fa-spin' : 'fa-cloud-upload'"></i>
              {{ isUploading ? 'Uploading...' : 'Upload' }}
            </button>
            <button class="clear-button" [disabled]="isUploading" (click)="clearSelectedFile()">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        
        <div *ngIf="errorMessage" class="error-message">
          <i class="fa fa-exclamation-circle"></i>
          {{ errorMessage }}
        </div>
        
        <div *ngIf="previewUrl" class="file-preview">
          <h4>Preview</h4>
          <div class="preview-container">
            <img *ngIf="!isSvgFile" [src]="previewUrl" alt="Floorplan Preview">
            <div *ngIf="isSvgFile" [innerHTML]="svgContent | safeHtml"></div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .floorplan-uploader {
      width: 100%;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
      background-color: #fff;
    }
    
    .uploader-header {
      padding: 12px 16px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }
    
    .uploader-content {
      padding: 16px;
    }
    
    .file-drop-area {
      border: 2px dashed #ccc;
      border-radius: 4px;
      padding: 32px 16px;
      text-align: center;
      transition: all 0.3s ease;
      
      &.drag-over {
        border-color: #2196f3;
        background-color: rgba(33, 150, 243, 0.05);
      }
      
      .drop-message {
        i {
          font-size: 36px;
          color: #757575;
          margin-bottom: 8px;
        }
        
        p {
          margin: 8px 0;
          color: #616161;
        }
        
        .file-types {
          font-size: 12px;
          color: #9e9e9e;
        }
      }
      
      .browse-button {
        margin-top: 16px;
        padding: 8px 16px;
        background-color: #2196f3;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
        
        &:hover {
          background-color: #1976d2;
        }
      }
    }
    
    .selected-file {
      margin-top: 16px;
      padding: 12px;
      background-color: #f5f5f5;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .file-info {
        .file-name {
          font-weight: 500;
          margin-right: 8px;
        }
        
        .file-size {
          color: #757575;
          font-size: 12px;
        }
      }
      
      .file-actions {
        display: flex;
        gap: 8px;
        
        .upload-button {
          padding: 6px 12px;
          background-color: #4caf50;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.2s;
          display: flex;
          align-items: center;
          gap: 6px;
          
          &:hover:not(:disabled) {
            background-color: #388e3c;
          }
          
          &:disabled {
            background-color: #a5d6a7;
            cursor: not-allowed;
          }
        }
        
        .clear-button {
          padding: 6px;
          background-color: #f44336;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.2s;
          
          &:hover:not(:disabled) {
            background-color: #d32f2f;
          }
          
          &:disabled {
            background-color: #ef9a9a;
            cursor: not-allowed;
          }
        }
      }
    }
    
    .error-message {
      margin-top: 16px;
      padding: 12px;
      background-color: #ffebee;
      color: #c62828;
      border-radius: 4px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .file-preview {
      margin-top: 16px;
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 500;
      }
      
      .preview-container {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 8px;
        background-color: #f9f9f9;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
        max-height: 300px;
        overflow: hidden;
        
        img, div {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
      }
    }
  `]
})
export class FloorplanUploaderComponent {
  @Input() title = 'Upload Floorplan';
  @Input() acceptedFileTypes = '.svg, .png, .jpg, .jpeg';
  @Output() fileUploaded = new EventEmitter<{ url: string, filename: string }>();
  
  @ViewChild('fileInput') fileInput!: ElementRef;
  
  selectedFile: File | null = null;
  isUploading = false;
  isDragging = false;
  errorMessage = '';
  previewUrl: string | null = null;
  isSvgFile = false;
  svgContent = '';
  
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
  }
  
  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
  }
  
  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
    
    if (event.dataTransfer?.files.length) {
      this.processFile(event.dataTransfer.files[0]);
    }
  }
  
  onFileSelected(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement.files?.length) {
      this.processFile(inputElement.files[0]);
    }
  }
  
  clearSelectedFile(): void {
    this.selectedFile = null;
    this.previewUrl = null;
    this.errorMessage = '';
    this.isSvgFile = false;
    this.svgContent = '';
    
    // Reset the file input
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }
  
  uploadFile(): void {
    if (!this.selectedFile) {
      this.errorMessage = 'No file selected';
      return;
    }
    
    this.isUploading = true;
    this.errorMessage = '';
    
    // Here you would typically upload to Firebase storage
    // This is a placeholder for the actual implementation
    setTimeout(() => {
      // Simulate successful upload
      if (this.selectedFile) {
        const url = URL.createObjectURL(this.selectedFile);
        this.fileUploaded.emit({
          url,
          filename: this.selectedFile.name || 'floorplan'
        });
      }
      
      this.isUploading = false;
      // You might want to clear the selection after upload
      // this.clearSelectedFile();
    }, 2000);
  }
  
  private processFile(file: File): void {
    this.errorMessage = '';
    
    // Check if the file type is allowed
    const fileExtension = this.getFileExtension(file.name).toLowerCase();
    const acceptedExtensions = this.acceptedFileTypes
      .split(',')
      .map(type => type.trim().replace('.', '').toLowerCase());
    
    if (!acceptedExtensions.includes(fileExtension)) {
      this.errorMessage = `File type not supported. Please use: ${this.acceptedFileTypes}`;
      return;
    }
    
    // Check file size (max 5MB)
    const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSizeInBytes) {
      this.errorMessage = 'File is too large. Maximum size is 5MB.';
      return;
    }
    
    this.selectedFile = file;
    this.isSvgFile = fileExtension === 'svg';
    
    // Generate preview
    if (this.isSvgFile) {
      const reader = new FileReader();
      reader.onload = e => {
        this.svgContent = e.target?.result as string;
        this.previewUrl = URL.createObjectURL(file);
      };
      reader.readAsText(file);
    } else {
      this.previewUrl = URL.createObjectURL(file);
    }
  }
  
  private getFileExtension(filename: string): string {
    return filename.split('.').pop() || '';
  }
  
  formatFileSize(sizeInBytes: number): string {
    if (sizeInBytes < 1024) {
      return sizeInBytes + ' B';
    } else if (sizeInBytes < 1024 * 1024) {
      return (sizeInBytes / 1024).toFixed(1) + ' KB';
    } else {
      return (sizeInBytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
  }
} 