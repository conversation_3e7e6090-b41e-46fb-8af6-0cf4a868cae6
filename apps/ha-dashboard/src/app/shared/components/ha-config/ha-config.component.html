<div class="ha-config-dialog">
  <h2 mat-dialog-title>Configure Home Assistant</h2>
  
  <mat-dialog-content>
    <form [formGroup]="configForm">
      <div class="form-description">
        <p>Connect to your Home Assistant instance to display device states and controls on your dashboard.</p>
        <p class="info-text">
          <mat-icon>info</mat-icon>
          You'll need to generate a Long-Lived Access Token in your Home Assistant profile settings.
        </p>
      </div>
      
      <div class="form-fields">
        <mat-form-field appearance="outline">
          <mat-label>Home Assistant URL</mat-label>
          <input matInput formControlName="apiUrl" placeholder="https://homeassistant.local:8123/api">
          <mat-hint>Include the full URL with protocol and port</mat-hint>
          <mat-error *ngIf="configForm.get('apiUrl')?.hasError('required')">
            URL is required
          </mat-error>
          <mat-error *ngIf="configForm.get('apiUrl')?.hasError('pattern')">
            Must be a valid URL starting with http:// or https://
          </mat-error>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Long-Lived Access Token</mat-label>
          <input matInput formControlName="token" type="password">
          <mat-hint>Generate this in your Home Assistant profile</mat-hint>
          <mat-error *ngIf="configForm.get('token')?.hasError('required')">
            Access token is required
          </mat-error>
        </mat-form-field>
      </div>
      
      <div class="connection-test" *ngIf="connectionTestResult">
        <div class="test-result success" *ngIf="connectionTestResult === 'success'">
          <mat-icon>check_circle</mat-icon>
          <span>Successfully connected to Home Assistant!</span>
        </div>
        
        <div class="test-result error" *ngIf="connectionTestResult === 'error'">
          <mat-icon>error</mat-icon>
          <span>{{ errorMessage }}</span>
        </div>
      </div>
    </form>
  </mat-dialog-content>
  
  <mat-dialog-actions align="end">
    <button mat-button (click)="cancel()">Cancel</button>
    <button 
      mat-button 
      color="primary" 
      (click)="testConnection()" 
      [disabled]="configForm.invalid || testing">
      <mat-spinner diameter="20" *ngIf="testing"></mat-spinner>
      <span *ngIf="!testing">Test Connection</span>
    </button>
    <button 
      mat-raised-button 
      color="primary" 
      (click)="save()" 
      [disabled]="configForm.invalid">
      Save
    </button>
  </mat-dialog-actions>
</div> 