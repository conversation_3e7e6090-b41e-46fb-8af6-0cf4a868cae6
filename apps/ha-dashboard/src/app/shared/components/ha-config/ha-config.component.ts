import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { HomeAssistantProxyService, HAConfig } from '../../../services/home-assistant-proxy.service';
import { take } from 'rxjs/operators';

@Component({
  selector: 'app-ha-config',
  templateUrl: './ha-config.component.html',
  styleUrls: ['./ha-config.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatProgressSpinnerModule
  ]
})
export class HAConfigComponent implements OnInit {
  configForm: FormGroup;
  testing = false;
  connectionTestResult: 'success' | 'error' | null = null;
  errorMessage = '';
  
  constructor(
    private fb: FormBuilder,
    private homeAssistantService: HomeAssistantProxyService,
    private dialogRef: MatDialogRef<HAConfigComponent>
  ) {
    this.configForm = this.fb.group({
      apiUrl: ['', [Validators.required, Validators.pattern(/^https?:\/\/.+/)]],
      token: ['', Validators.required]
    });
  }
  
  ngOnInit(): void {
    // Load existing configuration if available
    if (this.homeAssistantService.isConfigured()) {
      // Load from localStorage directly since our service doesn't expose the config
      const apiUrl = localStorage.getItem('ha-api-url');
      const token = localStorage.getItem('ha-token');
      
      if (apiUrl && token) {
        this.configForm.patchValue({
          apiUrl,
          token
        });
      }
    }
  }
  
  testConnection(): void {
    if (this.configForm.invalid) {
      return;
    }
    
    this.testing = true;
    this.connectionTestResult = null;
    this.errorMessage = '';
    
    const config = this.configForm.value as HAConfig;
    this.homeAssistantService.configure(config);
    
    this.homeAssistantService.checkConnection()
      .pipe(take(1))
      .subscribe({
        next: (connected) => {
          this.testing = false;
          this.connectionTestResult = connected ? 'success' : 'error';
          if (!connected) {
            this.errorMessage = 'Failed to connect to Home Assistant. Please check your URL and token.';
          }
        },
        error: (err) => {
          this.testing = false;
          this.connectionTestResult = 'error';
          this.errorMessage = err.message || 'An unknown error occurred';
        }
      });
  }
  
  save(): void {
    if (this.configForm.invalid) {
      return;
    }
    
    const config = this.configForm.value as HAConfig;
    this.homeAssistantService.configure(config);
    this.dialogRef.close(true);
  }
  
  cancel(): void {
    this.dialogRef.close(false);
  }
} 