.ha-config-dialog {
  min-width: 400px;
  max-width: 500px;
  
  .form-description {
    margin-bottom: 20px;
    
    .info-text {
      display: flex;
      align-items: center;
      color: #666;
      background-color: rgba(0, 0, 0, 0.03);
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
      
      mat-icon {
        margin-right: 8px;
        color: #1976d2;
      }
    }
  }
  
  .form-fields {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
    
    mat-form-field {
      width: 100%;
    }
  }
  
  .connection-test {
    margin: 16px 0;
    
    .test-result {
      display: flex;
      align-items: center;
      padding: 10px;
      border-radius: 4px;
      
      mat-icon {
        margin-right: 8px;
      }
      
      &.success {
        background-color: rgba(76, 175, 80, 0.1);
        color: #2e7d32;
        
        mat-icon {
          color: #2e7d32;
        }
      }
      
      &.error {
        background-color: rgba(244, 67, 54, 0.1);
        color: #d32f2f;
        
        mat-icon {
          color: #d32f2f;
        }
      }
    }
  }
  
  mat-dialog-actions {
    button {
      min-width: 100px;
      
      mat-spinner {
        display: inline-block;
        margin-right: 8px;
      }
    }
  }
} 