import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { Point, Room } from '../../../models/dashboard.models';

@Component({
  selector: 'app-room-details-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule
  ],
  template: `
    <h2 mat-dialog-title>Room Details</h2>
    <div mat-dialog-content>
      <p>Enter details for your new room</p>
      
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Room ID</mat-label>
        <input matInput [(ngModel)]="roomData.id" required>
        <mat-hint>Use a unique ID without spaces (e.g., living-room)</mat-hint>
      </mat-form-field>
      
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Room Name</mat-label>
        <input matInput [(ngModel)]="roomData.name" required>
      </mat-form-field>
      
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Room Type</mat-label>
        <mat-select [(ngModel)]="roomData.type" required>
          <mat-option value="room">Generic Room</mat-option>
          <mat-option value="living">Living Room</mat-option>
          <mat-option value="bedroom">Bedroom</mat-option>
          <mat-option value="bathroom">Bathroom</mat-option>
          <mat-option value="kitchen">Kitchen</mat-option>
          <mat-option value="hallway">Hallway</mat-option>
          <mat-option value="other">Other</mat-option>
        </mat-select>
      </mat-form-field>
      
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Description</mat-label>
        <textarea matInput [(ngModel)]="roomData.description" rows="3"></textarea>
        <mat-hint>Add a detailed description of this room (optional)</mat-hint>
      </mat-form-field>
    </div>
    
    <div mat-dialog-actions align="end">
      <button mat-button (click)="dialogRef.close()">Cancel</button>
      <button mat-button color="primary" (click)="saveRoom()" [disabled]="!isValid()">
        Save Room
      </button>
    </div>
  `,
  styles: [`
    :host {
      --mdc-dialog-container-color: var(--background-secondary);
      --mdc-dialog-with-divider-divider-color: var(--border-color);
      --mdc-dialog-subhead-color: var(--text-color);
      --mdc-dialog-supporting-text-color: var(--text-color);
    }
    
    ::ng-deep .mat-mdc-dialog-surface {
      background-color: var(--background-secondary) !important;
      color: var(--text-color) !important;
    }
    
    ::ng-deep h2.mat-mdc-dialog-title {
      color: var(--text-color) !important;
    }
    
    ::ng-deep .mat-mdc-form-field-focus-overlay {
      background-color: var(--background-secondary);
    }
    
    ::ng-deep .mat-mdc-text-field-wrapper {
      background-color: var(--background-secondary);
    }
    
    ::ng-deep .mat-mdc-form-field-input-control {
      color: var(--text-color) !important;
    }
    
    ::ng-deep .mdc-text-field--outlined .mdc-notched-outline__leading,
    ::ng-deep .mdc-text-field--outlined .mdc-notched-outline__notch,
    ::ng-deep .mdc-text-field--outlined .mdc-notched-outline__trailing {
      border-color: var(--border-color) !important;
    }
    
    ::ng-deep .mat-mdc-form-field-label {
      color: var(--text-secondary) !important;
    }
    
    ::ng-deep .mdc-floating-label, 
    ::ng-deep .mdc-floating-label--float-above,
    ::ng-deep .mdc-text-field:not(.mdc-text-field--disabled) .mdc-floating-label,
    ::ng-deep .mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-form-field-label {
      color: var(--text-secondary) !important;
    }
    
    ::ng-deep .mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-floating-label,
    ::ng-deep .mat-mdc-form-field.mat-focused:not(.mat-form-field-disabled) .mat-mdc-form-field-label {
      color: var(--primary-color) !important;
    }
    
    ::ng-deep .mat-mdc-form-field-hint {
      color: var(--text-secondary) !important;
    }
    
    ::ng-deep .mat-mdc-input-element,
    ::ng-deep textarea.mat-mdc-input-element {
      color: var(--text-color) !important;
    }
    
    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }
    
    mat-dialog-content {
      min-width: 350px;
    }
    
    ::ng-deep .mat-mdc-select-panel {
      background-color: var(--background-secondary) !important;
    }
    
    ::ng-deep .mat-mdc-option {
      color: var(--text-color) !important;
    }
    
    ::ng-deep .mat-mdc-option .mdc-list-item__primary-text {
      color: var(--text-color) !important;
    }
    
    ::ng-deep .mat-mdc-option:hover:not(.mdc-list-item--disabled) {
      background-color: rgba(var(--primary-color-rgb), 0.1) !important;
    }
    
    ::ng-deep .mat-mdc-option.mat-mdc-option-active {
      background-color: rgba(var(--primary-color-rgb), 0.2) !important;
    }
    
    ::ng-deep .mat-primary .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after {
      color: var(--primary-color) !important;
    }
    
    /* Fix the button text colors */
    ::ng-deep .mat-mdc-button:not(.mat-primary) {
      color: var(--text-color) !important;
    }
  `]
})
export class RoomDetailsDialogComponent {
  roomData: Room;
  
  constructor(
    public dialogRef: MatDialogRef<RoomDetailsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { 
      points: Point[],
      existingRooms?: string[]
    }
  ) {
    // Initialize with default values
    this.roomData = {
      id: this.generateUniqueId(),
      name: '',
      type: 'room',
      description: '',
      isCustomShape: true,
      customPolygon: data.points
    };
  }
  
  /**
   * Generate a unique room ID that doesn't conflict with existing rooms
   */
  private generateUniqueId(): string {
    const baseId = 'custom-room';
    
    if (!this.data.existingRooms || this.data.existingRooms.length === 0) {
      return baseId;
    }
    
    let counter = 1;
    let newId = baseId;
    
    while (this.data.existingRooms.includes(newId)) {
      newId = `${baseId}-${counter}`;
      counter++;
    }
    
    return newId;
  }
  
  /**
   * Validate the form data
   */
  isValid(): boolean {
    return !!(
      this.roomData.id && 
      this.roomData.id.trim() !== '' && 
      this.roomData.name && 
      this.roomData.name.trim() !== ''
    );
  }
  
  /**
   * Save the room data and close the dialog
   */
  saveRoom(): void {
    if (!this.isValid()) return;
    
    // Clean up the ID to ensure it's URL-friendly
    this.roomData.id = this.roomData.id
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');
    
    // Set image path based on room type
    this.roomData.imagePath = `/assets/images/rooms/${this.roomData.type}.png`;
    
    this.dialogRef.close(this.roomData);
  }
} 