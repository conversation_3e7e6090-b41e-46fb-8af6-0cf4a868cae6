import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ThemeService, Theme } from '../../../services/theme.service';

export interface MenuItem {
  label: string;
  icon: string;
  action: () => void;
  divider?: boolean;
}

export interface MenuConfig {
  autoSaveEnabled: boolean;
  isDefault?: boolean;
  showGrid?: boolean;
  canUndo?: boolean;
  isSaving?: boolean;
  dashboardName?: string;
  isEditingEnabled?: boolean;
  haConnected?: boolean;
  additionalItems?: MenuItem[];
}

@Component({
  selector: 'app-hamburger-menu',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="hamburger-menu-container" [class.open]="isOpen">
      <button class="hamburger-button" (click)="toggleMenu()">
        <span class="hamburger-icon" [class.open]="isOpen">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </span>
      </button>

      <div class="menu-panel" *ngIf="isOpen">
        <div class="menu-header">
          <h3>{{ config.dashboardName || 'Dashboard' }} Settings</h3>
          <button class="close-button" (click)="toggleMenu()">✕</button>
        </div>
        
        <div class="menu-content">
          <div class="dashboard-name">
            <h4>Current Dashboard: {{ config.dashboardName || 'Dashboard' }}</h4>
          </div>

          <div class="actions-group">
            <h5>Actions</h5>
            <div class="setting-item">
              <button (click)="saveLayout()" [disabled]="config.isSaving" class="primary-button">
                <i class="fa" [class.fa-save]="!config.isSaving" [class.fa-spinner]="config.isSaving" [class.fa-spin]="config.isSaving"></i>
                {{ config.isSaving ? 'Saving...' : 'Save Layout' }}
              </button>
              <p class="description">Save the current dashboard layout</p>
            </div>
            
            <div class="setting-item">
              <button (click)="undoLastChange()" class="secondary-button" [disabled]="!config.canUndo">
                <i class="fa fa-undo"></i> Undo Last Change
              </button>
              <p class="description">Restore the previous dashboard state</p>
            </div>
          </div>

          <div class="settings-group">
            <h5>Settings</h5>
            <div class="setting-item">
              <label class="toggle">
                <span class="toggle-label">Autosave</span>
                <input 
                  type="checkbox" 
                  [checked]="config.autoSaveEnabled" 
                  (change)="toggleAutoSave()"
                >
                <span class="toggle-slider"></span>
              </label>
              <p class="description">Automatically save changes after editing</p>
            </div>
            
            <div class="setting-item">
              <label class="toggle">
                <span class="toggle-label">Show Grid</span>
                <input 
                  type="checkbox" 
                  [checked]="config.showGrid" 
                  (change)="toggleShowGrid()"
                >
                <span class="toggle-slider"></span>
              </label>
              <p class="description">Show grid lines while editing</p>
            </div>
            
            <div class="setting-item">
              <label class="toggle">
                <span class="toggle-label">Dark Mode</span>
                <input 
                  type="checkbox" 
                  [checked]="isDarkMode" 
                  (change)="toggleDarkMode()"
                >
                <span class="toggle-slider"></span>
              </label>
              <p class="description">
                Switch between light and dark theme
                <button *ngIf="!isUsingSystemTheme" class="reset-button" (click)="useSystemTheme()">
                  <i class="fa fa-sync-alt"></i> Use system setting
                </button>
              </p>
            </div>
            
            <div class="setting-item">
              <button (click)="configureHomeAssistant()" class="secondary-button">
                <i class="fa fa-home"></i> 
                Home Assistant 
                <span class="ha-status" [class.connected]="config.haConnected" [class.disconnected]="config.haConnected === false">
                  <i class="fa" [class.fa-circle]="config.haConnected" [class.fa-exclamation-circle]="config.haConnected === false"></i>
                </span>
              </button>
              <p class="description">
                Configure connection to your Home Assistant instance
                <span *ngIf="config.haConnected" class="status-text connected">Connected</span>
                <span *ngIf="config.haConnected === false" class="status-text disconnected">Disconnected</span>
              </p>
            </div>

            <div class="setting-item">
              <button (click)="openExplorer()" class="secondary-button" [disabled]="!config.haConnected">
                <i class="fa fa-search"></i> 
                Home Assistant Explorer
              </button>
              <p class="description">
                Browse devices, entities, and areas from Home Assistant
              </p>
            </div>
          </div>

          <div class="settings-group" *ngIf="config.additionalItems && config.additionalItems.length > 0">
            <h5>Additional Actions</h5>
            <div class="setting-item" *ngFor="let item of config.additionalItems">
              <button class="secondary-button" (click)="executeAdditionalAction(item)">
                <i class="fa fa-{{ item.icon }}"></i> {{ item.label }}
              </button>
              <hr *ngIf="item.divider" class="menu-divider">
            </div>
          </div>

          <div class="dashboard-controls">
            <h5>Dashboard Controls</h5>
            <div class="setting-item" *ngIf="!config.isDefault">
              <button class="action-button" (click)="setAsDefault()">
                Set as Default Dashboard
              </button>
              <p class="description">Makes this dashboard load first</p>
            </div>

            <div class="setting-item">
              <button class="danger-button" (click)="createNewDashboard()">
                Create New Dashboard
              </button>
              <p class="description">Create a fresh dashboard layout</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Backdrop for mobile -->
    <div 
      class="menu-backdrop" 
      *ngIf="isOpen" 
      (click)="toggleMenu()"
    ></div>
  `,
  styles: [`
    :host {
      display: block;
      position: relative;
    }

    .hamburger-menu-container {
      position: relative;
      z-index: 1000;
    }

    .hamburger-button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      transition: background-color 0.3s;
    }

    .hamburger-button:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    [data-theme="dark"] .hamburger-button:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .hamburger-icon {
      width: 24px;
      height: 18px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      transition: transform 0.3s;
    }

    .hamburger-icon.open {
      transform: rotate(45deg);
    }

    .bar {
      width: 100%;
      height: 2px;
      background-color: var(--text-color);
      border-radius: 2px;
      transition: transform 0.3s, opacity 0.3s;
    }

    .hamburger-icon.open .bar:nth-child(1) {
      transform: translateY(8px) rotate(0deg);
    }

    .hamburger-icon.open .bar:nth-child(2) {
      opacity: 0;
    }

    .hamburger-icon.open .bar:nth-child(3) {
      transform: translateY(-8px) rotate(90deg);
    }

    .menu-panel {
      position: absolute;
      top: 45px;
      right: 0;
      width: 320px;
      background: var(--menu-bg);
      color: var(--menu-text);
      border-radius: 8px;
      box-shadow: 0 4px 12px var(--box-shadow);
      z-index: 1000;
      overflow: hidden;
    }

    .menu-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid var(--menu-border);
    }

    .menu-header h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
    }

    .close-button {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 16px;
      color: var(--text-secondary);
    }

    .menu-content {
      padding: 16px;
      max-height: 80vh;
      overflow-y: auto;
    }

    .dashboard-name {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--menu-border);
    }

    .dashboard-name h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: var(--text-secondary);
    }

    .actions-group, .settings-group, .dashboard-controls {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--menu-border);
    }

    .actions-group h5, .settings-group h5, .dashboard-controls h5 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 14px;
      font-weight: 600;
      color: var(--text-color);
    }

    .setting-item {
      margin-bottom: 20px;
    }

    .setting-item:last-child {
      margin-bottom: 0;
    }

    .toggle {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      user-select: none;
    }

    .toggle-label {
      font-weight: 500;
      margin-right: 10px;
      color: var(--text-color);
    }

    .toggle input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: relative;
      display: inline-block;
      width: 46px;
      height: 24px;
      background-color: var(--toggle-bg);
      border-radius: 34px;
      transition: .4s;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      border-radius: 50%;
      transition: .4s;
    }

    input:checked + .toggle-slider {
      background-color: var(--toggle-active);
    }

    input:checked + .toggle-slider:before {
      transform: translateX(22px);
    }

    .description {
      margin-top: 6px;
      margin-bottom: 0;
      font-size: 12px;
      color: var(--text-secondary);
    }

    .primary-button, .action-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
      padding: 10px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.3s;
    }

    .primary-button:hover:not(:disabled), .action-button:hover {
      background-color: var(--primary-hover);
    }

    .primary-button:disabled {
      opacity: 0.65;
      cursor: not-allowed;
    }

    .secondary-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
      padding: 10px;
      background-color: var(--secondary-color);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.3s;
    }

    .secondary-button:hover:not(:disabled) {
      background-color: var(--secondary-hover);
    }

    .secondary-button:disabled {
      opacity: 0.65;
      cursor: not-allowed;
    }

    .danger-button {
      display: block;
      width: 100%;
      padding: 10px;
      background-color: var(--background-secondary);
      color: var(--text-color);
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.3s;
    }

    .danger-button:hover {
      background-color: var(--border-color);
    }

    .menu-backdrop {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.3);
      z-index: 999;
    }

    .reset-button {
      background: none;
      border: none;
      padding: 3px 6px;
      margin-left: 8px;
      font-size: 11px;
      color: var(--text-secondary);
      cursor: pointer;
      border-radius: 3px;
      transition: background-color 0.2s;
    }

    .reset-button:hover {
      background-color: var(--border-color);
    }

    @media (max-width: 768px) {
      .menu-panel {
        position: fixed;
        top: 0;
        right: 0;
        height: 100vh;
        width: 280px;
        border-radius: 0;
      }
    }

    .ha-status {
      display: inline-block;
      margin-left: 8px;
      font-size: 12px;
    }
    
    .ha-status.connected {
      color: var(--success-color);
    }
    
    .ha-status.disconnected {
      color: var(--error-color);
    }
    
    .status-text {
      display: inline-block;
      font-size: 12px;
      margin-left: 5px;
      padding: 2px 6px;
      border-radius: 10px;
    }
    
    .status-text.connected {
      background-color: rgba(76, 175, 80, 0.2);
      color: var(--success-color);
    }
    
    .status-text.disconnected {
      background-color: rgba(244, 67, 54, 0.2);
      color: var(--error-color);
    }
  `]
})
export class HamburgerMenuComponent implements OnInit {
  @Input() config: MenuConfig = {
    autoSaveEnabled: true,
    showGrid: false,
    isDefault: false,
    canUndo: false,
    isSaving: false,
    dashboardName: 'Dashboard'
  };

  @Output() toggleAutoSaveEvent = new EventEmitter<boolean>();
  @Output() toggleShowGridEvent = new EventEmitter<boolean>();
  @Output() setAsDefaultEvent = new EventEmitter<void>();
  @Output() createNewDashboardEvent = new EventEmitter<void>();
  @Output() saveLayoutEvent = new EventEmitter<void>();
  @Output() undoLastChangeEvent = new EventEmitter<void>();
  @Output() configureHomeAssistantEvent = new EventEmitter<void>();
  @Output() openExplorerEvent = new EventEmitter<void>();

  isOpen = false;
  isDarkMode = false;
  isUsingSystemTheme = true;

  constructor(private themeService: ThemeService) {}

  ngOnInit(): void {
    // Initialize theme state
    this.isDarkMode = this.themeService.getCurrentTheme() === 'dark';
    this.isUsingSystemTheme = this.themeService.isUsingSystemTheme();

    // Subscribe to theme changes
    this.themeService.theme$.subscribe(theme => {
      this.isDarkMode = theme === 'dark';
    });
  }

  toggleMenu(): void {
    this.isOpen = !this.isOpen;
  }

  toggleAutoSave(): void {
    this.config.autoSaveEnabled = !this.config.autoSaveEnabled;
    this.toggleAutoSaveEvent.emit(this.config.autoSaveEnabled);
  }

  toggleShowGrid(): void {
    this.config.showGrid = !this.config.showGrid;
    this.toggleShowGridEvent.emit(this.config.showGrid);
  }
  
  toggleDarkMode(): void {
    this.themeService.toggleTheme();
    this.isUsingSystemTheme = false;
  }

  useSystemTheme(): void {
    this.themeService.useSystemTheme();
    this.isUsingSystemTheme = true;
  }

  setAsDefault(): void {
    this.setAsDefaultEvent.emit();
  }

  createNewDashboard(): void {
    this.createNewDashboardEvent.emit();
  }
  
  saveLayout(): void {
    this.saveLayoutEvent.emit();
  }
  
  undoLastChange(): void {
    this.undoLastChangeEvent.emit();
  }

  configureHomeAssistant(): void {
    this.configureHomeAssistantEvent.emit();
    this.toggleMenu();
  }

  openExplorer(): void {
    this.openExplorerEvent.emit();
    this.toggleMenu();
  }

  /**
   * Execute action from additional items
   */
  executeAdditionalAction(item: MenuItem): void {
    if (item && item.action) {
      item.action();
      this.toggleMenu();
    }
  }
} 