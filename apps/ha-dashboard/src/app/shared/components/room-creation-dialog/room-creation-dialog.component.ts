import { Component, Inject, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { Point } from '../../../models/dashboard.models';
import { SafeHtmlPipe } from '../../pipes/safe-html.pipe';

@Component({
  selector: 'app-room-creation-dialog',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatButtonModule, SafeHtmlPipe],
  template: `
    <div class="room-creation-dialog">
      <div class="dialog-header">
        <h2>Create Room</h2>
        <div class="actions">
          <button mat-button (click)="clearPoints()">Clear</button>
          <button mat-button (click)="dialogRef.close()">Cancel</button>
          <button mat-button color="primary" [disabled]="points.length < 3" (click)="complete()">Done</button>
        </div>
      </div>
      
      <div class="dialog-content" #container>
        <!-- SVG Container with proper scaling -->
        <div class="svg-container" #svgContainer [innerHTML]="data.svgContent | safeHtml"></div>
        
        <!-- Overlay for vertex creation -->
        <svg class="vertex-overlay" #vertexOverlay [attr.viewBox]="svgViewBox">
          <!-- Polygon in progress -->
          <polygon 
            *ngIf="points.length > 1"
            [attr.points]="getPolygonPoints()"
            class="polygon-preview"
          />
          
          <!-- Vertices -->
          <g *ngFor="let point of points; let i = index">
            <!-- Completion indicator ring for first vertex -->
            <circle
              *ngIf="i === 0 && points.length >= 3"
              [attr.cx]="point.x"
              [attr.cy]="point.y"
              [attr.r]="SNAP_HIGHLIGHT_RADIUS + 4"
              class="completion-ring"
              [class.active]="isHoveringFirstVertex || (i === 0 && points.length >= 3 && isFirstVertexHovered)"
            />
            
            <!-- Vertex circle -->
            <circle
              [attr.cx]="point.x"
              [attr.cy]="point.y"
              [attr.r]="getVertexRadius(i)"
              [class]="getVertexClass(i)"
              [attr.data-tooltip]="getVertexTooltip(i)"
              (mousedown)="onVertexMouseDown($event, i)"
              (mouseenter)="onVertexMouseEnter(i)"
              (mouseleave)="onVertexMouseLeave(i)"
              (click)="onVertexClick($event, i)"
            />
          </g>
          
          <!-- Preview line from last point to cursor -->
          <line
            *ngIf="points.length > 0 && !isHoveringFirstVertex"
            [attr.x1]="points[points.length - 1].x"
            [attr.y1]="points[points.length - 1].y"
            [attr.x2]="cursorPosition.x"
            [attr.y2]="cursorPosition.y"
            class="preview-line"
          />
        </svg>
      </div>
    </div>
  `,
  styles: [`
    :host {
      --mdc-dialog-container-color: var(--background-secondary);
      --mdc-dialog-with-divider-divider-color: var(--border-color);
      --mdc-dialog-subhead-color: var(--text-color);
      --mdc-dialog-supporting-text-color: var(--text-color);
    }
    
    .room-creation-dialog {
      width: 100vw;
      height: 100vh;
      display: flex;
      flex-direction: column;
      background: var(--background-secondary);
      position: relative;
      color: var(--text-color);
    }
    
    .dialog-header {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--border-color);
      background: var(--background-secondary);
      z-index: 10;
      color: var(--text-color);
    }
    
    .dialog-header h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: var(--text-color);
    }
    
    .dialog-content {
      flex: 1;
      position: relative;
      overflow: hidden;
      background: var(--background-color);
    }
    
    .svg-container {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      
      ::ng-deep svg {
        width: 100%;
        height: 100%;
        object-fit: contain;
        
        // Ensure the SVG preserves aspect ratio and fills the container
        &:not([preserveAspectRatio]) {
          preserveAspectRatio: xMidYMid meet;
        }
      }
    }
    
    .vertex-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }
    
    .polygon-preview {
      fill: rgba(var(--primary-color-rgb), 0.1);
      stroke: var(--primary-color);
      stroke-width: 2;
      vector-effect: non-scaling-stroke;
    }
    
    .preview-line {
      stroke: var(--primary-color);
      stroke-width: 2;
      stroke-dasharray: 4;
      vector-effect: non-scaling-stroke;
    }
    
    .vertex {
      fill: var(--primary-color);
      stroke: var(--background-secondary);
      stroke-width: 2;
      cursor: move;
      pointer-events: all;
      vector-effect: non-scaling-stroke;
      transition: r 0.2s ease, fill 0.2s ease;
      
      &.first-vertex {
        fill: var(--secondary-color);
        stroke: var(--background-secondary);
        stroke-width: 2.5;
        cursor: pointer;
        
        &.can-complete {
          cursor: pointer;
          
          &:hover {
            fill: var(--success-color);
          }
        }
      }
      
      &.snap-highlight {
        fill: var(--success-color);
        stroke: var(--background-secondary);
        stroke-width: 3;
      }
      
      &[data-tooltip]:hover::after {
        content: attr(data-tooltip);
        position: absolute;
        background: var(--background-secondary);
        color: var(--text-color);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        pointer-events: none;
        transform: translateY(-100%);
        margin-top: -8px;
        box-shadow: 0 2px 4px var(--box-shadow);
      }
    }
    
    .completion-ring {
      fill: none;
      stroke: var(--success-color);
      stroke-width: 2;
      stroke-dasharray: 8;
      opacity: 0;
      vector-effect: non-scaling-stroke;
      transition: opacity 0.3s ease, stroke-dashoffset 2s linear;
      pointer-events: none;
      
      &.active {
        opacity: 0.8;
        animation: rotate 2s linear infinite;
      }
    }
    
    ::ng-deep .mat-mdc-button {
      color: var(--text-color) !important;
    }
    
    ::ng-deep .mat-mdc-button.mat-primary {
      color: var(--primary-color) !important;
    }
    
    @keyframes rotate {
      from {
        stroke-dashoffset: 0;
      }
      to {
        stroke-dashoffset: -32;
      }
    }
  `]
})
export class RoomCreationDialogComponent implements AfterViewInit {
  @ViewChild('container') container!: ElementRef;
  @ViewChild('vertexOverlay') vertexOverlay!: ElementRef;
  @ViewChild('svgContainer') svgContainer!: ElementRef;
  
  points: Point[] = [];
  cursorPosition: Point = { x: 0, y: 0 };
  svgViewBox = '0 0 100 100'; // Default viewBox
  
  // Dragging state
  isDragging = false;
  currentDragIndex = -1;
  
  // Snap threshold in SVG coordinates
  private readonly SNAP_THRESHOLD = 10;
  readonly SNAP_HIGHLIGHT_RADIUS = 8;
  
  // Track hover states
  isHoveringFirstVertex = false;
  isFirstVertexHovered = false;
  
  constructor(
    public dialogRef: MatDialogRef<RoomCreationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { svgContent: string }
  ) {}
  
  ngAfterViewInit(): void {
    this.setupEventListeners();
    this.setupSvgViewBox();
  }
  
  private setupSvgViewBox(): void {
    // Wait for the SVG to be loaded in the DOM
    setTimeout(() => {
      const svgElement = this.svgContainer.nativeElement.querySelector('svg');
      if (svgElement) {
        // Get the viewBox from the original SVG
        let viewBox = '0 0 100 100'; // Default fallback
        if (svgElement.viewBox?.baseVal) {
          const vb = svgElement.viewBox.baseVal;
          viewBox = `${vb.x} ${vb.y} ${vb.width} ${vb.height}`;
        } else if (svgElement.getAttribute('viewBox')) {
          viewBox = svgElement.getAttribute('viewBox')!;
        }
        
        // Update our viewBox state
        this.svgViewBox = viewBox;
        
        // Set up the overlay SVG to match exactly
        const overlay = this.vertexOverlay.nativeElement;
        overlay.setAttribute('viewBox', viewBox);
        
        // Ensure the original SVG fills its container
        svgElement.style.width = '100%';
        svgElement.style.height = '100%';
        
        // Match preserveAspectRatio if specified
        const preserveAspectRatio = svgElement.getAttribute('preserveAspectRatio') || 'xMidYMid meet';
        overlay.setAttribute('preserveAspectRatio', preserveAspectRatio);
        svgElement.setAttribute('preserveAspectRatio', preserveAspectRatio);
      }
    }, 0);
  }
  
  private setupEventListeners(): void {
    const container = this.container.nativeElement;
    const overlay = this.vertexOverlay.nativeElement;
    
    // Click to add vertex
    container.addEventListener('click', (e: MouseEvent) => {
      if (!this.isDragging) {
        this.addPoint(this.getRelativeCoordinates(e));
      }
    });
    
    // Track cursor for preview line and snapping
    container.addEventListener('mousemove', (e: MouseEvent) => {
      const coords = this.getRelativeCoordinates(e);
      this.cursorPosition = coords;
      
      if (this.isDragging && this.currentDragIndex !== -1) {
        // When dragging, check if we're near the first vertex
        if (this.points.length >= 3 && this.currentDragIndex === this.points.length - 1) {
          const firstPoint = this.points[0];
          const distance = this.getDistance(coords, firstPoint);
          
          if (distance <= this.SNAP_THRESHOLD) {
            this.isHoveringFirstVertex = true;
            // Snap to first vertex position
            this.points[this.currentDragIndex] = { ...firstPoint };
          } else {
            this.isHoveringFirstVertex = false;
            this.points[this.currentDragIndex] = coords;
          }
        } else {
          this.points[this.currentDragIndex] = coords;
        }
      }
    });
    
    // Handle drag end and check for completion
    document.addEventListener('mouseup', () => {
      if (this.isDragging && this.currentDragIndex === this.points.length - 1 && this.isHoveringFirstVertex) {
        // Complete the polygon
        this.points[this.currentDragIndex] = { ...this.points[0] };
        this.complete();
      }
      
      this.isDragging = false;
      this.currentDragIndex = -1;
      this.isHoveringFirstVertex = false;
    });
  }
  
  startDragging(event: MouseEvent, index: number): void {
    event.stopPropagation();
    this.isDragging = true;
    this.currentDragIndex = index;
  }
  
  private getRelativeCoordinates(event: MouseEvent): Point {
    const overlay = this.vertexOverlay.nativeElement;
    const svgPoint = overlay.createSVGPoint();
    
    svgPoint.x = event.clientX;
    svgPoint.y = event.clientY;
    
    // Convert screen coordinates to SVG coordinates
    const ctm = overlay.getScreenCTM();
    if (ctm) {
      const transformed = svgPoint.matrixTransform(ctm.inverse());
      return {
        x: transformed.x,
        y: transformed.y
      };
    }
    
    // Fallback to simple calculation if CTM is not available
    const rect = overlay.getBoundingClientRect();
    const viewBox = overlay.viewBox.baseVal;
    
    return {
      x: ((event.clientX - rect.left) / rect.width) * viewBox.width + viewBox.x,
      y: ((event.clientY - rect.top) / rect.height) * viewBox.height + viewBox.y
    };
  }
  
  addPoint(point: Point): void {
    this.points.push(point);
  }
  
  clearPoints(): void {
    this.points = [];
  }
  
  getPolygonPoints(): string {
    return this.points.map(p => `${p.x},${p.y}`).join(' ');
  }
  
  complete(): void {
    if (this.points.length >= 3) {
      this.dialogRef.close({ points: this.points });
    }
  }
  
  private getDistance(p1: Point, p2: Point): number {
    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }
  
  getVertexRadius(index: number): number {
    if (index === 0) {
      if (this.isHoveringFirstVertex || (this.points.length >= 3 && this.isFirstVertexHovered)) {
        return this.SNAP_HIGHLIGHT_RADIUS;
      }
      return 7;
    }
    return 6;
  }
  
  getVertexClass(index: number): string {
    const classes = ['vertex'];
    
    if (index === 0) {
      classes.push('first-vertex');
      if (this.points.length >= 3) {
        classes.push('can-complete');
      }
      if (this.isHoveringFirstVertex || (index === 0 && this.points.length >= 3 && this.isFirstVertexHovered)) {
        classes.push('snap-highlight');
      }
    }
    
    return classes.join(' ');
  }
  
  getVertexTooltip(index: number): string {
    if (index === 0 && this.points.length >= 3) {
      return 'Click to complete room';
    }
    return '';
  }
  
  onVertexMouseDown(event: MouseEvent, index: number): void {
    // Only start dragging if it's not the first vertex with enough points to complete
    if (!(index === 0 && this.points.length >= 3)) {
      this.startDragging(event, index);
    }
  }
  
  onVertexMouseEnter(index: number): void {
    if (index === 0) {
      this.isFirstVertexHovered = true;
    }
  }
  
  onVertexMouseLeave(index: number): void {
    if (index === 0) {
      this.isFirstVertexHovered = false;
    }
  }
  
  onVertexClick(event: MouseEvent, index: number): void {
    // Prevent click from creating a new point
    event.stopPropagation();
    
    // Complete the room if clicking the first vertex with enough points
    if (index === 0 && this.points.length >= 3) {
      this.complete();
    }
  }
} 