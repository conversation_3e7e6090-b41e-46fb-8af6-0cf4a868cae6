import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { HomeAssistantService, HAEntity, HADev<PERSON>, HAArea } from '../../../services/home-assistant.service';
import { Observable, forkJoin, of } from 'rxjs';
import { catchError, finalize, tap } from 'rxjs/operators';

@Component({
  selector: 'app-ha-explorer-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatCardModule,
    MatDividerModule,
    MatExpansionModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    FormsModule
  ],
  template: `
    <div class="ha-explorer-container">
      <div class="ha-explorer-header">
        <h2>Home Assistant Explorer</h2>
        <div class="ha-explorer-actions">
          <button mat-icon-button (click)="refresh()" matTooltip="Refresh" [disabled]="loading">
            <mat-icon>refresh</mat-icon>
          </button>
          <button mat-icon-button (click)="close()" matTooltip="Close">
            <mat-icon>close</mat-icon>
          </button>
        </div>
      </div>

      <div class="ha-explorer-content">
        <mat-tab-group [(selectedIndex)]="selectedTabIndex" animationDuration="200ms">
          <!-- Entities Tab -->
          <mat-tab label="Entities ({{ entities.length }})">
            <div class="tab-content">
              <div class="filter-container">
                <mat-form-field appearance="outline">
                  <mat-label>Filter by domain</mat-label>
                  <mat-select [(ngModel)]="selectedDomain" (selectionChange)="filterEntities()">
                    <mat-option value="">All domains</mat-option>
                    <mat-option *ngFor="let domain of entityDomains" [value]="domain">
                      {{ domain }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                
                <mat-form-field appearance="outline">
                  <mat-label>Search entities</mat-label>
                  <input matInput [(ngModel)]="entitySearchTerm" (keyup)="filterEntities()">
                  <mat-icon matSuffix>search</mat-icon>
                </mat-form-field>
              </div>
              
              <div *ngIf="loading" class="loading-container">
                <mat-spinner diameter="40"></mat-spinner>
                <span>Loading entities...</span>
              </div>
              
              <mat-expansion-panel *ngFor="let entity of filteredEntities">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <mat-icon>{{ getEntityIcon(entity) }}</mat-icon>
                    {{ entity.attributes['friendly_name'] || entity.entity_id }}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ entity.state }}
                    <span *ngIf="entity.attributes['unit_of_measurement']">
                      {{ entity.attributes['unit_of_measurement'] }}
                    </span>
                  </mat-panel-description>
                </mat-expansion-panel-header>
                
                <mat-card>
                  <mat-card-content>
                    <div class="entity-details">
                      <div class="entity-detail-item">
                        <span class="label">Entity ID:</span>
                        <span class="value">{{ entity.entity_id }}</span>
                      </div>
                      <div class="entity-detail-item">
                        <span class="label">State:</span>
                        <span class="value">{{ entity.state }}</span>
                      </div>
                      <div class="entity-detail-item">
                        <span class="label">Last Changed:</span>
                        <span class="value">{{ entity.last_changed | date:'medium' }}</span>
                      </div>
                      <div class="entity-detail-item">
                        <span class="label">Last Updated:</span>
                        <span class="value">{{ entity.last_updated | date:'medium' }}</span>
                      </div>
                    </div>
                    
                    <mat-expansion-panel class="attributes-panel">
                      <mat-expansion-panel-header>
                        <mat-panel-title>Attributes</mat-panel-title>
                      </mat-expansion-panel-header>
                      <div class="attributes-list">
                        <div *ngFor="let attr of getAttributeKeys(entity)" class="attribute-item">
                          <span class="label">{{ attr }}:</span>
                          <span class="value">{{ formatAttributeValue(entity.attributes[attr]) }}</span>
                        </div>
                      </div>
                    </mat-expansion-panel>
                    
                    <div class="entity-actions" *ngIf="canToggle(entity)">
                      <button mat-raised-button color="primary" (click)="toggleEntity(entity.entity_id)">
                        TOGGLE
                      </button>
                    </div>
                  </mat-card-content>
                </mat-card>
              </mat-expansion-panel>
              
              <div *ngIf="filteredEntities.length === 0 && !loading" class="empty-state">
                <mat-icon>info</mat-icon>
                <p>No entities found matching your criteria.</p>
              </div>
            </div>
          </mat-tab>
          
          <!-- Devices Tab -->
          <mat-tab label="Devices ({{ devices.length }})">
            <div class="tab-content">
              <div class="filter-container">
                <mat-form-field appearance="outline">
                  <mat-label>Filter by area</mat-label>
                  <mat-select [(ngModel)]="selectedArea" (selectionChange)="filterDevices()">
                    <mat-option value="">All areas</mat-option>
                    <mat-option *ngFor="let area of areas" [value]="area.area_id">
                      {{ area.name }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                
                <mat-form-field appearance="outline">
                  <mat-label>Search devices</mat-label>
                  <input matInput [(ngModel)]="deviceSearchTerm" (keyup)="filterDevices()">
                  <mat-icon matSuffix>search</mat-icon>
                </mat-form-field>
              </div>
              
              <div *ngIf="loading" class="loading-container">
                <mat-spinner diameter="40"></mat-spinner>
                <span>Loading devices...</span>
              </div>
              
              <mat-expansion-panel *ngFor="let device of filteredDevices">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <mat-icon>devices</mat-icon>
                    {{ device.name_by_user || device.name || 'Unknown Device' }}
                  </mat-panel-title>
                  <mat-panel-description>
                    <span *ngIf="device.manufacturer">{{ device.manufacturer }}</span>
                    <span *ngIf="device.model"> - {{ device.model }}</span>
                  </mat-panel-description>
                </mat-expansion-panel-header>
                
                <mat-card>
                  <mat-card-content>
                    <div class="device-details">
                      <div class="device-detail-item">
                        <span class="label">Device ID:</span>
                        <span class="value">{{ device.id }}</span>
                      </div>
                      <div *ngIf="device.manufacturer" class="device-detail-item">
                        <span class="label">Manufacturer:</span>
                        <span class="value">{{ device.manufacturer }}</span>
                      </div>
                      <div *ngIf="device.model" class="device-detail-item">
                        <span class="label">Model:</span>
                        <span class="value">{{ device.model }}</span>
                      </div>
                      <div *ngIf="device.sw_version" class="device-detail-item">
                        <span class="label">Software Version:</span>
                        <span class="value">{{ device.sw_version }}</span>
                      </div>
                      <div *ngIf="device.hw_version" class="device-detail-item">
                        <span class="label">Hardware Version:</span>
                        <span class="value">{{ device.hw_version }}</span>
                      </div>
                      <div *ngIf="device.area_id" class="device-detail-item">
                        <span class="label">Area:</span>
                        <span class="value">{{ getAreaName(device.area_id) }}</span>
                      </div>
                    </div>
                    
                    <mat-expansion-panel class="entities-panel">
                      <mat-expansion-panel-header>
                        <mat-panel-title>Related Entities</mat-panel-title>
                      </mat-expansion-panel-header>
                      <div class="related-entities">
                        <mat-list>
                          <mat-list-item *ngFor="let entity of getDeviceEntities(device.id)">
                            <mat-icon matListItemIcon>{{ getEntityIcon(entity) }}</mat-icon>
                            <div matListItemTitle>{{ entity.attributes['friendly_name'] || entity.entity_id }}</div>
                            <div matListItemLine>{{ entity.entity_id }}</div>
                            <div matListItemMeta>{{ entity.state }}</div>
                          </mat-list-item>
                          
                          <div *ngIf="getDeviceEntities(device.id).length === 0" class="empty-sublist">
                            <p>No entities associated with this device.</p>
                          </div>
                        </mat-list>
                      </div>
                    </mat-expansion-panel>
                  </mat-card-content>
                </mat-card>
              </mat-expansion-panel>
              
              <div *ngIf="filteredDevices.length === 0 && !loading" class="empty-state">
                <mat-icon>info</mat-icon>
                <p>No devices found matching your criteria.</p>
              </div>
            </div>
          </mat-tab>
          
          <!-- Areas Tab -->
          <mat-tab label="Areas ({{ areas.length }})">
            <div class="tab-content">
              <div class="filter-container">
                <mat-form-field appearance="outline">
                  <mat-label>Search areas</mat-label>
                  <input matInput [(ngModel)]="areaSearchTerm" (keyup)="filterAreas()">
                  <mat-icon matSuffix>search</mat-icon>
                </mat-form-field>
              </div>
              
              <div *ngIf="loading" class="loading-container">
                <mat-spinner diameter="40"></mat-spinner>
                <span>Loading areas...</span>
              </div>
              
              <mat-expansion-panel *ngFor="let area of filteredAreas">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <mat-icon>location_on</mat-icon>
                    {{ area.name }}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ getDevicesInArea(area.area_id).length }} devices
                  </mat-panel-description>
                </mat-expansion-panel-header>
                
                <mat-card>
                  <mat-card-content>
                    <div class="area-details">
                      <div class="area-detail-item">
                        <span class="label">Area ID:</span>
                        <span class="value">{{ area.area_id }}</span>
                      </div>
                    </div>
                    
                    <mat-expansion-panel class="devices-panel">
                      <mat-expansion-panel-header>
                        <mat-panel-title>Devices in this Area</mat-panel-title>
                      </mat-expansion-panel-header>
                      <div class="related-devices">
                        <mat-list>
                          <mat-list-item *ngFor="let device of getDevicesInArea(area.area_id)">
                            <mat-icon matListItemIcon>devices</mat-icon>
                            <div matListItemTitle>{{ device.name_by_user || device.name || 'Unknown Device' }}</div>
                            <div matListItemLine *ngIf="device.manufacturer || device.model">
                              <span *ngIf="device.manufacturer">{{ device.manufacturer }}</span>
                              <span *ngIf="device.model"> - {{ device.model }}</span>
                            </div>
                          </mat-list-item>
                          
                          <div *ngIf="getDevicesInArea(area.area_id).length === 0" class="empty-sublist">
                            <p>No devices in this area.</p>
                          </div>
                        </mat-list>
                      </div>
                    </mat-expansion-panel>
                    
                    <mat-expansion-panel class="entities-panel">
                      <mat-expansion-panel-header>
                        <mat-panel-title>Entities in this Area</mat-panel-title>
                      </mat-expansion-panel-header>
                      <div class="related-entities">
                        <mat-list>
                          <mat-list-item *ngFor="let entity of getEntitiesInArea(area.area_id)">
                            <mat-icon matListItemIcon>{{ getEntityIcon(entity) }}</mat-icon>
                            <div matListItemTitle>{{ entity.attributes['friendly_name'] || entity.entity_id }}</div>
                            <div matListItemLine>{{ entity.entity_id }}</div>
                            <div matListItemMeta>{{ entity.state }}</div>
                          </mat-list-item>
                          
                          <div *ngIf="getEntitiesInArea(area.area_id).length === 0" class="empty-sublist">
                            <p>No entities directly associated with this area.</p>
                          </div>
                        </mat-list>
                      </div>
                    </mat-expansion-panel>
                  </mat-card-content>
                </mat-card>
              </mat-expansion-panel>
              
              <div *ngIf="filteredAreas.length === 0 && !loading" class="empty-state">
                <mat-icon>info</mat-icon>
                <p>No areas found matching your criteria.</p>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </div>
  `,
  styles: [`
    .ha-explorer-container {
      display: flex;
      flex-direction: column;
      height: 90vh;
      width: 90vw;
      max-width: 1200px;
      overflow: hidden;
    }
    
    .ha-explorer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }
    
    .ha-explorer-header h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 400;
    }
    
    .ha-explorer-actions {
      display: flex;
      gap: 8px;
    }
    
    .ha-explorer-content {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    ::ng-deep .mat-mdc-tab-body-wrapper {
      flex: 1;
      overflow: hidden;
    }
    
    .tab-content {
      padding: 16px;
      overflow-y: auto;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .filter-container {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
      margin-bottom: 8px;
    }
    
    .filter-container mat-form-field {
      flex: 1;
      min-width: 200px;
    }
    
    .loading-container {
      display: flex;
      align-items: center;
      gap: 16px;
      margin: 32px auto;
    }
    
    .entity-details, .device-details, .area-details {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 16px;
    }
    
    .entity-detail-item, .device-detail-item, .area-detail-item {
      display: flex;
      gap: 8px;
    }
    
    .label {
      font-weight: 500;
      min-width: 140px;
      color: rgba(0, 0, 0, 0.7);
    }
    
    .value {
      flex: 1;
    }
    
    .attributes-panel, .entities-panel, .devices-panel {
      margin-top: 16px;
    }
    
    .attributes-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .attribute-item {
      display: flex;
      gap: 8px;
    }
    
    .entity-actions {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
    
    .empty-state {
      text-align: center;
      padding: 32px;
      color: rgba(0, 0, 0, 0.6);
    }
    
    .empty-state mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
    }
    
    .empty-sublist {
      padding: 16px;
      text-align: center;
      color: rgba(0, 0, 0, 0.6);
    }
    
    ::ng-deep .mat-expansion-panel-body {
      padding: 0 16px 16px !important;
    }
  `]
})
export class HaExplorerModalComponent implements OnInit {
  // Data arrays
  entities: HAEntity[] = [];
  devices: HADevice[] = [];
  areas: HAArea[] = [];
  
  // Filtered arrays
  filteredEntities: HAEntity[] = [];
  filteredDevices: HADevice[] = [];
  filteredAreas: HAArea[] = [];
  
  // Filter values
  selectedDomain = '';
  selectedArea = '';
  entitySearchTerm = '';
  deviceSearchTerm = '';
  areaSearchTerm = '';
  
  // UI state
  loading = false;
  error = '';
  entityDomains: string[] = [];
  selectedTabIndex = 0;
  
  constructor(
    private dialogRef: MatDialogRef<HaExplorerModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private haService: HomeAssistantService
  ) {}
  
  ngOnInit(): void {
    this.fetchAllData();
    
    // Set initial tab if provided in data
    if (this.data && this.data.initialTab) {
      this.setInitialTab(this.data.initialTab);
    }
  }
  
  fetchAllData(): void {
    this.loading = true;
    this.error = '';
    
    forkJoin({
      entities: this.haService.fetchAllEntities().pipe(
        catchError(error => {
          console.error('Error fetching entities:', error);
          return of([]);
        })
      ),
      devices: this.haService.fetchDevices().pipe(
        catchError(error => {
          console.error('Error fetching devices:', error);
          return of([]);
        })
      ),
      areas: this.haService.fetchAreas().pipe(
        catchError(error => {
          console.error('Error fetching areas:', error);
          return of([]);
        })
      )
    }).pipe(
      finalize(() => {
        this.loading = false;
      })
    ).subscribe(results => {
      this.entities = results.entities;
      this.devices = results.devices;
      this.areas = results.areas;
      
      this.filteredEntities = this.entities;
      this.filteredDevices = this.devices;
      this.filteredAreas = this.areas;
      
      // Extract unique entity domains for filtering
      this.entityDomains = [...new Set(
        this.entities.map(entity => entity.entity_id.split('.')[0])
      )].sort();
    });
  }
  
  refresh(): void {
    this.fetchAllData();
  }
  
  close(): void {
    this.dialogRef.close();
  }
  
  filterEntities(): void {
    this.filteredEntities = this.entities.filter(entity => {
      const matchesDomain = !this.selectedDomain || entity.entity_id.startsWith(`${this.selectedDomain}.`);
      const matchesSearch = !this.entitySearchTerm || 
        entity.entity_id.toLowerCase().includes(this.entitySearchTerm.toLowerCase()) ||
        (entity.attributes['friendly_name'] && 
         entity.attributes['friendly_name'].toString().toLowerCase().includes(this.entitySearchTerm.toLowerCase()));
      
      return matchesDomain && matchesSearch;
    });
  }
  
  filterDevices(): void {
    this.filteredDevices = this.devices.filter(device => {
      const matchesArea = !this.selectedArea || device.area_id === this.selectedArea;
      const deviceName = device.name_by_user || device.name || '';
      const matchesSearch = !this.deviceSearchTerm || 
        deviceName.toLowerCase().includes(this.deviceSearchTerm.toLowerCase()) ||
        (device.manufacturer && device.manufacturer.toLowerCase().includes(this.deviceSearchTerm.toLowerCase())) ||
        (device.model && device.model.toLowerCase().includes(this.deviceSearchTerm.toLowerCase()));
      
      return matchesArea && matchesSearch;
    });
  }
  
  filterAreas(): void {
    this.filteredAreas = this.areas.filter(area => {
      return !this.areaSearchTerm || area.name.toLowerCase().includes(this.areaSearchTerm.toLowerCase());
    });
  }
  
  getEntityIcon(entity: HAEntity): string {
    const domain = entity.entity_id.split('.')[0];
    
    switch (domain) {
      case 'light': return 'lightbulb';
      case 'switch': return 'toggle_on';
      case 'sensor': return 'sensors';
      case 'binary_sensor': return 'fiber_manual_record';
      case 'climate': return 'thermostat';
      case 'camera': return 'videocam';
      case 'media_player': return 'music_note';
      case 'fan': return 'mode_fan';
      case 'cover': return 'vertical_shades';
      case 'lock': return 'lock';
      case 'vacuum': return 'robot';
      case 'automation': return 'smart_toy';
      case 'script': return 'code';
      case 'scene': return 'movie';
      case 'weather': return 'cloud';
      case 'sun': return 'wb_sunny';
      case 'device_tracker': return 'location_on';
      case 'person': return 'person';
      case 'zone': return 'map';
      default: return 'device_unknown';
    }
  }
  
  canToggle(entity: HAEntity): boolean {
    const domain = entity.entity_id.split('.')[0];
    const toggleableDomains = ['light', 'switch', 'input_boolean', 'automation', 'fan', 'cover'];
    
    return toggleableDomains.includes(domain);
  }
  
  toggleEntity(entityId: string): void {
    this.haService.toggleEntity(entityId).subscribe(result => {
      if (!result.success) {
        console.error('Failed to toggle entity:', result.error);
      }
    });
  }
  
  getAttributeKeys(entity: HAEntity): string[] {
    return Object.keys(entity.attributes || {}).sort();
  }
  
  formatAttributeValue(value: any): string {
    if (value === null || value === undefined) {
      return 'null';
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    
    return String(value);
  }
  
  getAreaName(areaId: string): string {
    const area = this.areas.find(a => a.area_id === areaId);
    return area ? area.name : 'Unknown';
  }
  
  getDeviceEntities(deviceId: string): HAEntity[] {
    return this.entities.filter(entity => 
      entity.attributes['device_id'] === deviceId
    );
  }
  
  getDevicesInArea(areaId: string): HADevice[] {
    return this.devices.filter(device => device.area_id === areaId);
  }
  
  getEntitiesInArea(areaId: string): HAEntity[] {
    // Get entities directly associated with the area
    const directEntities = this.entities.filter(entity => 
      entity.attributes['area_id'] === areaId
    );
    
    // Get entities associated with devices in this area
    const deviceIds = this.getDevicesInArea(areaId).map(device => device.id);
    const deviceEntities = this.entities.filter(entity => 
      entity.attributes['device_id'] && deviceIds.includes(entity.attributes['device_id'] as string)
    );
    
    // Combine and remove duplicates
    const combinedEntities = [...directEntities];
    
    deviceEntities.forEach(entity => {
      if (!combinedEntities.some(e => e.entity_id === entity.entity_id)) {
        combinedEntities.push(entity);
      }
    });
    
    return combinedEntities;
  }
  
  /**
   * Sets the initial tab based on the provided tab name
   */
  private setInitialTab(tabName: string): void {
    // Wait for the next tick to ensure the tab group is initialized
    setTimeout(() => {
      const tabIndex = this.getTabIndexByName(tabName);
      if (tabIndex !== -1) {
        this.selectedTabIndex = tabIndex;
      }
    });
  }
  
  /**
   * Gets the tab index by name
   */
  private getTabIndexByName(tabName: string): number {
    switch (tabName.toLowerCase()) {
      case 'entities':
        return 0;
      case 'devices':
        return 1;
      case 'areas':
        return 2;
      default:
        return -1;
    }
  }
} 