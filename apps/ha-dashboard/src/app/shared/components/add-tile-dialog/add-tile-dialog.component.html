<div class="add-tile-dialog">
  <h2 mat-dialog-title>Add Tile</h2>
  
  <mat-dialog-content>
    <p class="dialog-description">
      Select a tile type to add to your dashboard
    </p>
    
    <mat-tab-group>
      <mat-tab label="Home Assistant">
        <div class="category-description">
          <mat-icon>home</mat-icon>
          <p>Display and control your Home Assistant devices</p>
        </div>
        
        <div class="tile-options-grid">
          <div *ngFor="let option of homeAssistantTiles" 
               class="tile-option" 
               [class.selected]="selectedTileType === option.type && 
                                selectedDeviceType === option.settings?.['deviceType']"
               (click)="selectTile(option)">
            <div class="tile-icon">
              <i class="fa {{ option.icon }}"></i>
            </div>
            <div class="tile-info">
              <h3>{{ option.name }}</h3>
              <p>{{ option.description }}</p>
            </div>
          </div>
        </div>
      </mat-tab>
      
      <mat-tab label="General">
        <div class="category-description">
          <mat-icon>dashboard</mat-icon>
          <p>General purpose dashboard widgets</p>
        </div>
        
        <div class="tile-options-grid">
          <div *ngFor="let option of generalTiles" 
               class="tile-option" 
               [class.selected]="selectedTileType === option.type"
               (click)="selectTile(option)">
            <div class="tile-icon">
              <i class="fa {{ option.icon }}"></i>
            </div>
            <div class="tile-info">
              <h3>{{ option.name }}</h3>
              <p>{{ option.description }}</p>
            </div>
          </div>
        </div>
      </mat-tab>
      
      <mat-tab label="Custom">
        <div class="category-description">
          <mat-icon>code</mat-icon>
          <p>Custom and specialty tiles</p>
        </div>
        
        <div class="tile-options-grid">
          <div *ngFor="let option of customTiles" 
               class="tile-option" 
               [class.selected]="selectedTileType === option.type"
               (click)="selectTile(option)">
            <div class="tile-icon">
              <i class="fa {{ option.icon }}"></i>
            </div>
            <div class="tile-info">
              <h3>{{ option.name }}</h3>
              <p>{{ option.description }}</p>
            </div>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
    
    <div *ngIf="selectedTileType" class="tile-configuration">
      <!-- Name input for all tile types -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Tile Name</mat-label>
        <input matInput [(ngModel)]="tileName" placeholder="Enter a name for this tile">
      </mat-form-field>
      
      <!-- Entity browser specific settings -->
      <div *ngIf="selectedTileType === 'entity-browser'" class="entity-browser-settings">
        <h3>Entity Browser Settings</h3>
        
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Filter by Domain</mat-label>
          <mat-select [(ngModel)]="domainFilter">
            <mat-option value="">All domains</mat-option>
            <mat-option *ngFor="let domain of availableDomains" [value]="domain">
              {{ domain }}
            </mat-option>
          </mat-select>
          <mat-hint>Only show entities of this domain type</mat-hint>
        </mat-form-field>
        
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Filter by State</mat-label>
          <mat-select [(ngModel)]="stateFilter">
            <mat-option value="">All states</mat-option>
            <mat-option *ngFor="let state of availableStates" [value]="state">
              {{ state }}
            </mat-option>
          </mat-select>
          <mat-hint>Only show entities with this state</mat-hint>
        </mat-form-field>
      </div>
    </div>
  </mat-dialog-content>
  
  <mat-dialog-actions align="end">
    <button mat-button (click)="cancel()">Cancel</button>
    <button 
      mat-raised-button 
      color="primary" 
      (click)="addTile()" 
      [disabled]="!selectedTileType">
      Add Tile
    </button>
  </mat-dialog-actions>
</div> 