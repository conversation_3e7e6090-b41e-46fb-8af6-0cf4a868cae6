import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { TileType } from '../../../models/dashboard.models';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { HomeAssistantService } from '../../../services/home-assistant.service';

interface TileOption {
  type: TileType;
  name: string;
  description: string;
  icon: string;
  settings?: Record<string, any>;
  category?: 'home-assistant' | 'general' | 'custom';
}

@Component({
  selector: 'app-add-tile-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    MatTabsModule,
    MatDividerModule
  ],
  templateUrl: './add-tile-dialog.component.html',
  styleUrls: ['./add-tile-dialog.component.scss']
})
export class AddTileDialogComponent {
  selectedTileType: TileType | null = null;
  tileName = '';
  selectedDeviceType: string | null = null;
  
  // Entity browser filter properties
  domainFilter = '';
  stateFilter = '';
  availableDomains: string[] = [];
  availableStates: string[] = [];
  
  // Define available tile types
  tileOptions: TileOption[] = [
    // Home Assistant device status tiles
    {
      type: 'device-status',
      name: 'Lights Control',
      description: 'Control your Home Assistant lights',
      icon: 'fa-lightbulb',
      settings: { deviceType: 'light' },
      category: 'home-assistant'
    },
    {
      type: 'device-status',
      name: 'Climate Control',
      description: 'Manage your thermostats and HVAC systems',
      icon: 'fa-temperature-high',
      settings: { deviceType: 'climate' },
      category: 'home-assistant'
    },
    {
      type: 'device-status',
      name: 'Switches',
      description: 'Control your Home Assistant switches',
      icon: 'fa-toggle-on',
      settings: { deviceType: 'switch' },
      category: 'home-assistant'
    },
    {
      type: 'device-status',
      name: 'Sensors',
      description: 'Display data from your sensors',
      icon: 'fa-chart-line',
      settings: { deviceType: 'sensor' },
      category: 'home-assistant'
    },
    {
      type: 'device-status',
      name: 'Media Players',
      description: 'Control your media devices',
      icon: 'fa-music',
      settings: { deviceType: 'media_player' },
      category: 'home-assistant'
    },
    {
      type: 'device-status',
      name: 'Cameras',
      description: 'View your security cameras',
      icon: 'fa-video',
      settings: { deviceType: 'camera' },
      category: 'home-assistant'
    },
    {
      type: 'entity-browser',
      name: 'Entity Browser',
      description: 'Browse and manage all Home Assistant entities',
      icon: 'fa-list-ul',
      settings: { domainFilter: '', stateFilter: '' },
      category: 'home-assistant'
    },
    {
      type: 'entity-browser',
      name: 'Lights Browser',
      description: 'Browse and manage light entities',
      icon: 'fa-lightbulb',
      settings: { domainFilter: 'light', stateFilter: '' },
      category: 'home-assistant'
    },
    {
      type: 'entity-browser',
      name: 'Active Devices',
      description: 'Show all devices that are currently on',
      icon: 'fa-toggle-on',
      settings: { domainFilter: '', stateFilter: 'on' },
      category: 'home-assistant'
    },
    
    // General tiles
    {
      type: 'floorplan',
      name: 'Floor Plan',
      description: 'Interactive floor plan with rooms and devices',
      icon: 'fa-home',
      category: 'general'
    },
    {
      type: 'weather',
      name: 'Weather',
      description: 'Display current weather and forecast',
      icon: 'fa-cloud-sun',
      category: 'general'
    },
    {
      type: 'chart',
      name: 'Chart',
      description: 'Display charts or graphs',
      icon: 'fa-chart-pie',
      category: 'general'
    },
    
    // Custom tiles
    {
      type: 'default',
      name: 'Default',
      description: 'Basic information tile',
      icon: 'fa-square',
      category: 'custom'
    },
    {
      type: 'custom',
      name: 'Custom',
      description: 'Create a custom tile',
      icon: 'fa-code',
      category: 'custom'
    }
  ];

  // Filter tiles by category
  get homeAssistantTiles(): TileOption[] {
    return this.tileOptions.filter(tile => tile.category === 'home-assistant');
  }

  get generalTiles(): TileOption[] {
    return this.tileOptions.filter(tile => tile.category === 'general');
  }

  get customTiles(): TileOption[] {
    return this.tileOptions.filter(tile => tile.category === 'custom');
  }

  constructor(
    private dialogRef: MatDialogRef<AddTileDialogComponent>,
    private haService: HomeAssistantService
  ) {
    // Fetch available domains for filtering
    this.haService.fetchAllEntities().subscribe(entities => {
      // Extract unique domains
      this.availableDomains = [...new Set(entities.map(e => e.entity_id.split('.')[0]))].sort();
      
      // Extract unique states
      this.availableStates = [...new Set(entities.map(e => e.state))].sort();
    });
  }

  selectTile(tileOption: TileOption): void {
    this.selectedTileType = tileOption.type;
    this.selectedDeviceType = tileOption.settings?.['deviceType'] || null;
    
    // Set domain and state filters if they exist in settings
    if (tileOption.settings) {
      this.domainFilter = tileOption.settings['domainFilter'] || '';
      this.stateFilter = tileOption.settings['stateFilter'] || '';
    }
    
    // Set a default title based on the tile name
    if (!this.tileName) {
      this.tileName = tileOption.name;
    }
  }

  addTile(): void {
    if (!this.selectedTileType) {
      return;
    }
    
    // Get the selected tile option
    const selectedOption = this.tileOptions.find(
      option => option.type === this.selectedTileType && 
      (option.settings?.['deviceType'] === this.selectedDeviceType || !option.settings?.['deviceType'])
    );
    
    if (!selectedOption) {
      return;
    }
    
    // Create settings object with any custom filter settings
    const settings = { ...selectedOption.settings };
    
    // For entity browser, add custom domain and state filters if set
    if (this.selectedTileType === 'entity-browser') {
      settings['domainFilter'] = this.domainFilter;
      settings['stateFilter'] = this.stateFilter;
    }
    
    this.dialogRef.close({
      type: this.selectedTileType,
      title: this.tileName || selectedOption.name,
      settings
    });
  }

  cancel(): void {
    this.dialogRef.close(null);
  }
} 