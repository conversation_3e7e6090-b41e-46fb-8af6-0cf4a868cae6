.add-tile-dialog {
  width: 100%;
  max-width: 800px;
  
  .dialog-description {
    margin-bottom: 20px;
    color: rgba(0, 0, 0, 0.6);
  }
  
  // Tabs styling
  ::ng-deep .mat-mdc-tab-body-content {
    padding: 20px 0;
  }
  
  .category-description {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    
    mat-icon {
      margin-right: 10px;
    }
    
    p {
      margin: 0;
    }
  }
  
  .tile-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
    padding: 10px 0 25px;
    
    .tile-option {
      display: flex;
      padding: 15px;
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.12);
      transition: all 0.2s ease;
      cursor: pointer;
      
      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
        border-color: rgba(0, 0, 0, 0.25);
      }
      
      &.selected {
        background-color: rgba(63, 81, 181, 0.08);
        border-color: rgba(63, 81, 181, 0.6);
      }
      
      .tile-icon {
        font-size: 24px;
        margin-right: 12px;
        color: #3f51b5;
        display: flex;
        align-items: center;
        justify-content: center;
        
        i {
          font-size: 24px;
        }
      }
      
      .tile-info {
        flex: 1;
        
        h3 {
          margin: 0 0 5px 0;
          font-size: 16px;
          font-weight: 500;
        }
        
        p {
          margin: 0;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.6);
        }
      }
    }
  }
  
  .tile-configuration {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.12);
    
    .full-width {
      width: 100%;
      margin-bottom: 15px;
    }
    
    .entity-browser-settings {
      margin-top: 20px;
      padding: 15px;
      background-color: rgba(0, 0, 0, 0.03);
      border-radius: 8px;
      
      h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
  
  .tile-name-input {
    margin-top: 24px;
    
    mat-form-field {
      width: 100%;
    }
  }
  
  mat-dialog-actions {
    margin-top: 16px;
    padding: 16px 0;
  }
} 