import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Notification, NotificationService } from '../../../services/notification.service';
import { Subscription } from 'rxjs';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-notification',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule
  ],
  template: `
    <div class="notification-container" *ngIf="notifications.length > 0">
      <div 
        *ngFor="let notification of notifications"
        class="notification-item" 
        [ngClass]="notification.type"
        [@notificationAnimation]
      >
        <mat-icon class="notification-icon">
          {{ getIconForType(notification.type) }}
        </mat-icon>
        <div class="notification-content">
          {{ notification.message }}
        </div>
        <button 
          mat-icon-button 
          class="notification-close"
          (click)="closeNotification(notification.id)"
        >
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .notification-container {
      position: fixed;
      bottom: 24px;
      left: 24px;
      z-index: 1000;
      display: flex;
      flex-direction: column;
      gap: 10px;
      max-width: 350px;
    }
    
    .notification-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 6px 10px 0 rgba(0,0,0,.14), 0 1px 18px 0 rgba(0,0,0,.12);
      color: white;
      min-width: 250px;
      animation: slide-in 0.3s ease-out;
    }
    
    .notification-icon {
      margin-right: 12px;
    }
    
    .notification-content {
      flex: 1;
      font-size: 14px;
    }
    
    .notification-close {
      color: white;
      opacity: 0.8;
    }
    
    .notification-close:hover {
      opacity: 1;
    }
    
    .success {
      background-color: #4CAF50;
    }
    
    .error {
      background-color: #F44336;
    }
    
    .info {
      background-color: #2196F3;
    }
    
    .warning {
      background-color: #FF9800;
    }
    
    @keyframes slide-in {
      from {
        transform: translateX(-100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  `],
  animations: [
    trigger('notificationAnimation', [
      state('void', style({
        transform: 'translateX(-100%)',
        opacity: 0
      })),
      transition('void => *', [
        animate('300ms ease-out', style({
          transform: 'translateX(0)',
          opacity: 1
        }))
      ]),
      transition('* => void', [
        animate('200ms ease-in', style({
          transform: 'translateX(-100%)',
          opacity: 0
        }))
      ])
    ])
  ]
})
export class NotificationComponent implements OnInit, OnDestroy {
  notifications: Notification[] = [];
  private subscription: Subscription = new Subscription();
  
  constructor(private notificationService: NotificationService) {}
  
  ngOnInit(): void {
    this.subscription = this.notificationService.getNotifications()
      .subscribe(notifications => {
        this.notifications = notifications;
      });
  }
  
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  
  closeNotification(id: string): void {
    this.notificationService.removeNotification(id);
  }
  
  getIconForType(type: string): string {
    switch (type) {
      case 'success':
        return 'check_circle';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
      default:
        return 'info';
    }
  }
} 