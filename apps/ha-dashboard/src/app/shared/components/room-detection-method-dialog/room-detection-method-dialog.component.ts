import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-room-detection-method-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatRadioModule
  ],
  template: `
    <h2 mat-dialog-title>Room Detection Method</h2>
    <div mat-dialog-content>
      <p>Select how you want to detect rooms in your floorplan:</p>
      
      <div class="method-options">
        <div class="method-option" [class.selected]="selectedMethod === 'id'" (click)="selectedMethod = 'id'">
          <mat-radio-button [checked]="selectedMethod === 'id'" (change)="selectedMethod = 'id'">
            Element ID Detection
          </mat-radio-button>
          <p class="description">
            Finds rooms based on SVG elements with IDs starting with "room-". 
            This works best if your SVG already has properly named room elements.
          </p>
        </div>
        
        <div class="method-option" [class.selected]="selectedMethod === 'visual'" (click)="selectedMethod = 'visual'">
          <mat-radio-button [checked]="selectedMethod === 'visual'" (change)="selectedMethod = 'visual'">
            Visual Room Detection
          </mat-radio-button>
          <p class="description">
            Analyzes your SVG to find areas that look like rooms based on walls (black lines)
            and filled areas. Works with simple architectural floor plans.
          </p>
        </div>
      </div>
    </div>
    
    <div mat-dialog-actions>
      <button mat-button (click)="dialogRef.close()">Cancel</button>
      <button mat-button color="primary" (click)="proceed()" [disabled]="!selectedMethod">
        Detect Rooms
      </button>
    </div>
  `,
  styles: [`
    .method-options {
      margin-top: 16px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .method-option {
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .method-option:hover {
      background-color: #f5f5f5;
    }
    
    .method-option.selected {
      border-color: #3f51b5;
      background-color: rgba(63, 81, 181, 0.05);
    }
    
    .description {
      margin: 8px 0 0 24px;
      color: #666;
      font-size: 0.9em;
    }
  `]
})
export class RoomDetectionMethodDialogComponent {
  selectedMethod: 'id' | 'visual' = 'visual'; // Default to visual detection
  
  constructor(
    public dialogRef: MatDialogRef<RoomDetectionMethodDialogComponent>
  ) {}
  
  proceed(): void {
    this.dialogRef.close({
      method: this.selectedMethod
    });
  }
} 