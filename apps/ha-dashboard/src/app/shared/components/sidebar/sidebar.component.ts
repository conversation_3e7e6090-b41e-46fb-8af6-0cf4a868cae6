import { Component, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatRippleModule } from '@angular/material/core';
import { RouterModule } from '@angular/router';

export enum DashboardType {
  GRIDSTER = 'gridster',
  SIMPLE = 'simple',
  DEVICES = 'devices',
  DEVICE_TEST = 'device-test'
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    MatListModule,
    MatIconModule,
    MatDividerModule,
    MatRippleModule,
    RouterModule
  ],
  template: `
    <div class="sidebar-container">
      <div class="sidebar-header">
        <div class="logo-container">
          <h2 class="title">HA Dashboard</h2>
        </div>
      </div>
      
      <mat-divider></mat-divider>
      
      <!-- Primary navigation items -->
      <div class="sidebar-section">
        <h3 class="section-title">Dashboards</h3>
        <mat-nav-list>
          <a mat-list-item 
             [class.active]="activeDashboard === DashboardType.GRIDSTER"
             (click)="selectDashboard(DashboardType.GRIDSTER)">
            <mat-icon matListItemIcon>dashboard</mat-icon>
            <span matListItemTitle>Gridster Dashboard</span>
          </a>
          <a mat-list-item 
             [class.active]="activeDashboard === DashboardType.SIMPLE"
             (click)="selectDashboard(DashboardType.SIMPLE)">
            <mat-icon matListItemIcon>view_module</mat-icon>
            <span matListItemTitle>Simple Dashboard</span>
          </a>
        </mat-nav-list>
      </div>
      
      <mat-divider></mat-divider>
      
      <!-- Devices & tools section -->
      <div class="sidebar-section">
        <h3 class="section-title">Devices & Tools</h3>
        <mat-nav-list>
          <a mat-list-item 
             [class.active]="activeDashboard === DashboardType.DEVICES"
             routerLink="/devices">
            <mat-icon matListItemIcon>devices</mat-icon>
            <span matListItemTitle>Device Browser</span>
          </a>
          <a mat-list-item 
             [class.active]="activeDashboard === DashboardType.DEVICE_TEST"
             routerLink="/device-test">
            <mat-icon matListItemIcon>bug_report</mat-icon>
            <span matListItemTitle>API Test</span>
          </a>
        </mat-nav-list>
      </div>
      
      <div class="sidebar-footer">
        <mat-divider></mat-divider>
        <a mat-list-item routerLink="/login">
          <mat-icon matListItemIcon>exit_to_app</mat-icon>
          <span matListItemTitle>Logout</span>
        </a>
      </div>
    </div>
  `,
  styles: [`
    .sidebar-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 260px;
      background-color: #f5f5f5;
      box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    }
    
    .sidebar-header {
      padding: 16px;
      display: flex;
      align-items: center;
    }
    
    .logo-container {
      display: flex;
      align-items: center;
    }
    
    .title {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
    
    .sidebar-section {
      margin-top: 8px;
    }
    
    .section-title {
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
      color: rgba(0, 0, 0, 0.6);
      margin: 16px 16px 8px;
    }
    
    .sidebar-footer {
      margin-top: auto;
    }
    
    .active {
      background-color: rgba(63, 81, 181, 0.1);
      color: #3f51b5;
    }
    
    .active mat-icon {
      color: #3f51b5;
    }
  `]
})
export class SidebarComponent {
  DashboardType = DashboardType;
  activeDashboard: DashboardType = DashboardType.GRIDSTER;
  
  @Output() dashboardChange = new EventEmitter<DashboardType>();
  
  selectDashboard(type: DashboardType): void {
    this.activeDashboard = type;
    this.dashboardChange.emit(type);
  }
} 