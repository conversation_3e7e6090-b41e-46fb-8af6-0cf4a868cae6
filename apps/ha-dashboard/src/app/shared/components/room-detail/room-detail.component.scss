.room-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1500;
  animation: fadeIn 0.3s ease;
}

.room-modal-container {
  background-color: var(--background-secondary);
  border-radius: 8px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px var(--box-shadow);
  animation: slideIn 0.3s ease;
  position: relative;
  overflow: hidden;
}

.room-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-color);
}

.room-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: var(--text-color);
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
  transition: color 0.2s;
  
  &:hover {
    color: var(--text-color);
  }
}

.room-modal-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  position: relative;
  color: var(--text-color);
}

.room-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.room-image-container {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 10px var(--box-shadow);
}

.room-image {
  width: 100%;
  height: auto;
  display: block;
}

.room-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.room-description {
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-color);
  margin: 0;
}

.room-devices {
  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: var(--text-color);
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
      color: var(--text-secondary);
    }
  }
}

.no-devices {
  padding: 12px;
  background-color: var(--background-color);
  border-radius: 4px;
  text-align: center;
  color: var(--text-secondary);
}

.room-modal-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.room-navigation-hint {
  margin: 0;
  font-size: 13px;
  color: var(--text-secondary);
  font-style: italic;
}

.back-button {
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    background-color: var(--primary-hover);
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive styles */
@media (min-width: 768px) {
  .room-details {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .room-image-container {
    flex: 0 0 60%;
    max-width: 60%;
  }
  
  .room-info {
    flex: 0 0 40%;
    max-width: 40%;
    padding-left: 20px;
  }
}

@media (max-width: 767px) {
  .room-modal-container {
    width: 95%;
    max-height: 95vh;
  }
  
  .room-modal-header h3 {
    font-size: 18px;
  }
  
  .room-modal-content {
    padding: 15px;
  }
  
  .room-modal-footer {
    flex-direction: column;
    gap: 10px;
    
    .room-navigation-hint {
      text-align: center;
      margin-bottom: 10px;
    }
    
    .back-button {
      width: 100%;
      justify-content: center;
    }
  }
} 