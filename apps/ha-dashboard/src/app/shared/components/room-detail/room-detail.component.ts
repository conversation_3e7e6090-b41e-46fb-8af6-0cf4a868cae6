import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Room } from '../../../models/dashboard.models';
import { LoadingSpinnerComponent } from '../loading-spinner/loading-spinner.component';
import { EmptyStateComponent } from '../empty-state/empty-state.component';

@Component({
  selector: 'app-room-detail',
  standalone: true,
  imports: [
    CommonModule,
    LoadingSpinnerComponent,
    EmptyStateComponent
  ],
  templateUrl: './room-detail.component.html',
  styleUrls: ['./room-detail.component.scss']
})
export class RoomDetailComponent {
  @Input() room?: Room;
  @Input() isVisible = false;
  @Output() close = new EventEmitter<void>();
  
  isLoading = false;
  hasError = false;
  errorMessage = '';
  
  /**
   * Close the room detail modal
   */
  closeModal(): void {
    this.close.emit();
  }
  
  /**
   * Determine if image path is valid and exists
   */
  checkImage(path: string): void {
    if (!path) {
      this.hasError = true;
      this.errorMessage = 'No image path provided for this room.';
      return;
    }
    
    this.isLoading = true;
    this.hasError = false;
    
    const img = new Image();
    img.onload = () => {
      this.isLoading = false;
      this.hasError = false;
    };
    
    img.onerror = () => {
      this.isLoading = false;
      this.hasError = true;
      this.errorMessage = `Failed to load room image from: ${path}`;
    };
    
    img.src = path;
  }
  
  /**
   * Called when room data changes
   */
  ngOnChanges(): void {
    if (this.room && this.isVisible && this.room.imagePath) {
      this.checkImage(this.room.imagePath);
    }
  }
} 