<div class="room-modal-overlay" *ngIf="isVisible" (click)="closeModal()">
  <div class="room-modal-container" (click)="$event.stopPropagation()">
    <div class="room-modal-header">
      <h3>{{ room?.name || 'Room Details' }}</h3>
      <button class="close-button" (click)="closeModal()">×</button>
    </div>
    
    <div class="room-modal-content">
      <!-- Loading state -->
      <app-loading-spinner *ngIf="isLoading" [overlay]="true" message="Loading room details..."></app-loading-spinner>
      
      <!-- Error state -->
      <app-empty-state
        *ngIf="hasError"
        title="Image Not Found"
        [description]="errorMessage"
        icon="fa fa-exclamation-triangle">
      </app-empty-state>
      
      <!-- Room details -->
      <div *ngIf="!isLoading && !hasError && room" class="room-details">
        <div class="room-image-container">
          <img [src]="room.imagePath" [alt]="room.name" class="room-image">
        </div>
        
        <div class="room-info">
          <p *ngIf="room.description" class="room-description">{{ room.description }}</p>
          
          <div *ngIf="room.devices && room.devices.length > 0" class="room-devices">
            <h4>Devices</h4>
            <ul>
              <li *ngFor="let device of room.devices">{{ device }}</li>
            </ul>
          </div>
          
          <div *ngIf="!room.devices || room.devices.length === 0" class="no-devices">
            <p>No devices in this room</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="room-modal-footer">
      <p class="room-navigation-hint" *ngIf="room && room.devices && room.devices.length > 0">
        This room contains {{ room.devices.length }} devices that you can control
      </p>
      <button class="back-button" (click)="closeModal()">
        <i class="fa fa-arrow-left"></i> Back to Floor Plan
      </button>
    </div>
  </div>
</div> 