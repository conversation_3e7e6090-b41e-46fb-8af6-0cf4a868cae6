import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-loading-spinner',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div [ngClass]="{'spinner-container': true, 'spinner-overlay': overlay}">
      <div class="spinner">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
      </div>
      <div *ngIf="message" class="spinner-message">{{ message }}</div>
    </div>
  `,
  styles: [`
    .spinner-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }
    
    .spinner-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      z-index: 1000;
    }
    
    .spinner {
      margin: 0 auto;
      width: 70px;
      text-align: center;
    }
    
    .spinner > div {
      width: 12px;
      height: 12px;
      background-color: #007bff;
      border-radius: 100%;
      display: inline-block;
      animation: sk-bouncedelay 1.4s infinite ease-in-out both;
      margin: 0 3px;
    }
    
    .spinner .bounce1 {
      animation-delay: -0.32s;
    }
    
    .spinner .bounce2 {
      animation-delay: -0.16s;
    }
    
    .spinner-message {
      margin-top: 10px;
      font-size: 14px;
      color: #666;
    }
    
    @keyframes sk-bouncedelay {
      0%, 80%, 100% { 
        transform: scale(0);
      } 40% { 
        transform: scale(1.0);
      }
    }
  `]
})
export class LoadingSpinnerComponent {
  @Input() overlay = false;
  @Input() message?: string;
} 