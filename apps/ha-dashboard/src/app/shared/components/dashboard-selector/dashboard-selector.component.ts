import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Router, RouterModule } from '@angular/router';
import { DashboardType } from '../sidebar/sidebar.component';
import { HomeAssistantService } from '../../../services/home-assistant.service';

interface DashboardOption {
  type: DashboardType;
  title: string;
  icon: string;
  description: string;
  route: string;
}

@Component({
  selector: 'app-dashboard-selector',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    RouterModule
  ],
  template: `
    <div class="dashboard-selector-container">
      <h1 class="title">Select Dashboard</h1>
      
      <div class="dashboard-options">
        <div 
          *ngFor="let option of dashboardOptions" 
          class="dashboard-option" 
          (click)="navigateTo(option.route)"
        >
          <div class="dashboard-option-content">
            <mat-icon class="dashboard-icon">{{option.icon}}</mat-icon>
            <h2>{{option.title}}</h2>
            <p>{{option.description}}</p>
          </div>
        </div>
      </div>
      
      <div class="actions">
        <button mat-button (click)="logout()">
          <mat-icon>exit_to_app</mat-icon>
          Logout
        </button>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-selector-container {
      width: 100vw;
      height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 2rem;
      box-sizing: border-box;
    }
    
    .title {
      font-size: 2rem;
      margin-bottom: 3rem;
      text-align: center;
    }
    
    .dashboard-options {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 2rem;
      max-width: 1200px;
    }
    
    .dashboard-option {
      width: 300px;
      height: 250px;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      transition: all 0.3s ease;
      cursor: pointer;
      overflow: hidden;
      position: relative;
    }
    
    .dashboard-option:hover {
      transform: translateY(-5px);
      background-color: rgba(255, 255, 255, 0.2);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }
    
    .dashboard-option-content {
      padding: 2rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100%;
      box-sizing: border-box;
    }
    
    .dashboard-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 1rem;
    }
    
    .dashboard-option h2 {
      margin: 0 0 0.5rem;
      font-size: 1.5rem;
      text-align: center;
    }
    
    .dashboard-option p {
      margin: 0;
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
    }
    
    .actions {
      margin-top: 3rem;
    }
  `]
})
export class DashboardSelectorComponent {
  dashboardOptions: DashboardOption[] = [
    {
      type: DashboardType.GRIDSTER,
      title: 'Gridster Dashboard',
      icon: 'dashboard',
      description: 'Customizable grid-based dashboard with draggable and resizable widgets',
      route: '/gridster'
    },
    {
      type: DashboardType.SIMPLE,
      title: 'Simple Dashboard',
      icon: 'view_module',
      description: 'Simplified dashboard layout with fixed arrangement of widgets',
      route: '/simple'
    },
    {
      type: DashboardType.DEVICE_TEST,
      title: 'API Test',
      icon: 'bug_report',
      description: 'Test and debug Home Assistant API functionality',
      route: '/device-test'
    }
  ];
  
  constructor(
    private router: Router,
    private haService: HomeAssistantService
  ) {
    // Check if Home Assistant is configured
    if (!this.haService.isConfigured()) {
      this.router.navigate(['/login']);
    }
  }
  
  navigateTo(route: string): void {
    this.router.navigate([route]);
  }
  
  logout(): void {
    // Clear any stored credentials
    localStorage.removeItem('ha_config');
    localStorage.removeItem('user_email');
    
    // Stop polling
    this.haService.stopPolling();
    
    // Navigate to login
    this.router.navigate(['/login']);
  }
} 