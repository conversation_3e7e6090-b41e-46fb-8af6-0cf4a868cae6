import { Component, Inject, <PERSON>Init, ViewChild, <PERSON>ement<PERSON>ef, <PERSON><PERSON>estroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Point } from '../../../models/dashboard.models';
import { SafeHtmlPipe } from '../../pipes/safe-html.pipe';

@Component({
  selector: 'app-polygon-creator',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    SafeHtmlPipe
  ],
  template: `
    <div class="polygon-creator">
      <div class="polygon-creator-header">
        <h2>Create Polygon</h2>
        <div class="polygon-controls">
          <span class="vertex-counter">{{ vertices.length }} points</span>
          <button mat-icon-button 
            (click)="undoLastVertex()" 
            [disabled]="vertices.length === 0"
            matTooltip="Undo last point">
            <mat-icon>undo</mat-icon>
          </button>
          <button mat-raised-button 
            (click)="completePolygon()" 
            [disabled]="vertices.length < 3" 
            color="primary">
            Complete
          </button>
          <button mat-icon-button 
            (click)="cancelCreation()" 
            matTooltip="Cancel">
            <mat-icon>close</mat-icon>
          </button>
        </div>
      </div>
      
      <div class="polygon-creator-content">
        <div class="instruction-message" *ngIf="vertices.length === 0">
          <mat-icon>touch_app</mat-icon> Click to add points and create your shape
        </div>
        
        <div class="coordinate-display" *ngIf="currentPosition">
          {{ currentPosition.x.toFixed(1) }}%, {{ currentPosition.y.toFixed(1) }}%
        </div>
        
        <div class="svg-container" #svgContainer [innerHTML]="svgContent | safeHtml"></div>
      </div>
    </div>
  `,
  styles: [`
    .polygon-creator {
      display: flex;
      flex-direction: column;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.9);
      position: fixed;
      top: 0;
      left: 0;
      overflow: hidden;
    }
    
    .polygon-creator-header {
      padding: 16px;
      background-color: rgba(0, 0, 0, 0.8);
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 10;
      
      h2 {
        margin: 0;
        color: white;
        font-size: 20px;
      }
      
      .polygon-controls {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .vertex-counter {
          background-color: rgba(255, 255, 255, 0.1);
          color: white;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 14px;
        }
      }
    }
    
    .polygon-creator-content {
      flex: 1;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      box-sizing: border-box;
      
      .svg-container {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        
        ::ng-deep svg {
          width: 100% !important;
          height: 100% !important;
          object-fit: contain;
          background-color: white;
          border-radius: 4px;
          box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
          cursor: crosshair;
          
          .polygon-preview {
            fill: rgba(49, 119, 255, 0.3);
            stroke: #3177ff;
            stroke-width: 2;
            vector-effect: non-scaling-stroke;
          }
          
          .vertex-marker {
            fill: #ff4444;
            stroke: white;
            stroke-width: 2;
            cursor: move;
            filter: drop-shadow(0px 0px 2px rgba(0, 0, 0, 0.5));
            vector-effect: non-scaling-stroke;
            
            &:hover {
              fill: #ff6666;
              stroke-width: 3;
              r: 8;
            }
          }
        }
      }
    }
    
    .instruction-message {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      z-index: 100;
      
      mat-icon {
        color: #3177ff;
      }
    }
    
    .coordinate-display {
      position: absolute;
      top: 20px;
      right: 20px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 6px 12px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 14px;
      z-index: 100;
    }
  `]
})
export class PolygonCreatorComponent implements OnInit, OnDestroy {
  @ViewChild('svgContainer') svgContainer?: ElementRef;
  
  svgContent: string;
  vertices: Point[] = [];
  currentPosition: Point | null = null;
  private clickListener: ((e: MouseEvent) => void) | null = null;
  private moveListener: ((e: MouseEvent) => void) | null = null;

  constructor(
    public dialogRef: MatDialogRef<PolygonCreatorComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { 
      svgContent: string
    }
  ) {
    this.svgContent = data.svgContent;
  }

  ngOnInit(): void {
    setTimeout(() => {
      this.setupEventListeners();
    }, 100);
  }

  ngOnDestroy(): void {
    this.cleanupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.svgContainer) return;
    
    const container = this.svgContainer.nativeElement;
    const svgElement = container.querySelector('svg');
    if (!svgElement) return;
    
    this.clickListener = this.handleClick.bind(this);
    this.moveListener = this.handleMouseMove.bind(this);
    
    svgElement.addEventListener('click', this.clickListener);
    svgElement.addEventListener('mousemove', this.moveListener);
  }

  private cleanupEventListeners(): void {
    if (!this.svgContainer) return;
    
    const container = this.svgContainer.nativeElement;
    const svgElement = container.querySelector('svg');
    if (!svgElement) return;
    
    if (this.clickListener) {
      svgElement.removeEventListener('click', this.clickListener);
    }
    if (this.moveListener) {
      svgElement.removeEventListener('mousemove', this.moveListener);
    }
  }

  private calculateCoordinates(event: MouseEvent, element: SVGSVGElement): Point {
    const rect = element.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;
    return { x, y };
  }

  private handleClick(event: MouseEvent): void {
    const svgElement = event.currentTarget as SVGSVGElement;
    const point = this.calculateCoordinates(event, svgElement);
    
    this.vertices.push(point);
    this.updatePolygonPreview();
  }

  private handleMouseMove(event: MouseEvent): void {
    const svgElement = event.currentTarget as SVGSVGElement;
    this.currentPosition = this.calculateCoordinates(event, svgElement);
  }

  private updatePolygonPreview(): void {
    if (this.vertices.length < 2) return;
    
    const container = this.svgContainer?.nativeElement;
    if (!container) return;
    
    const svgElement = container.querySelector('svg');
    if (!svgElement) return;
    
    // Remove existing preview elements
    const existingPolygon = svgElement.querySelector('.polygon-preview');
    if (existingPolygon) {
      existingPolygon.remove();
    }
    
    const existingVertices = svgElement.querySelectorAll('.vertex-marker');
    existingVertices.forEach((v: Element) => v.remove());
    
    // Create polygon
    const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
    const points = this.vertices.map(v => `${v.x}% ${v.y}%`).join(' ');
    
    polygon.setAttribute('points', points);
    polygon.setAttribute('class', 'polygon-preview');
    svgElement.appendChild(polygon);
    
    // Add vertex markers
    this.vertices.forEach((vertex, index) => {
      const marker = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      marker.setAttribute('cx', `${vertex.x}%`);
      marker.setAttribute('cy', `${vertex.y}%`);
      marker.setAttribute('r', '6');
      marker.setAttribute('class', 'vertex-marker');
      marker.setAttribute('data-index', index.toString());
      
      svgElement.appendChild(marker);
    });
  }

  undoLastVertex(): void {
    if (this.vertices.length > 0) {
      this.vertices.pop();
      this.updatePolygonPreview();
    }
  }

  completePolygon(): void {
    if (this.vertices.length >= 3) {
      this.dialogRef.close({ vertices: this.vertices });
    }
  }

  cancelCreation(): void {
    this.dialogRef.close();
  }
} 