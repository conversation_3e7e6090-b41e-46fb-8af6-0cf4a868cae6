import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Router, RouterModule } from '@angular/router';
import { HomeAssistantService, HAEntity, HAServices } from '../../services/home-assistant.service';
import { catchError, finalize, tap } from 'rxjs/operators';
import { forkJoin, of } from 'rxjs';
import { environment } from '../../environments/environment';

// Device interface based on Home Assistant API
export interface HADevice {
  id: string;
  name?: string;
  name_by_user?: string;
  model?: string;
  manufacturer?: string;
  identifiers?: string[];
  sw_version?: string;
  hw_version?: string;
  via_device_id?: string;
  area_id?: string;
  configuration_url?: string;
  disabled_by?: string;
  entry_type?: string;
  connections?: [string, string][];
}

// Area interface
export interface HAArea {
  area_id: string;
  name: string;
}

// Config interface
export interface HAConfig {
  components: string[];
  config_dir: string;
  elevation: number;
  latitude: number;
  longitude: number;
  location_name: string;
  time_zone: string;
  unit_system: {
    length: string;
    mass: string;
    temperature: string;
    volume: string;
  };
  version: string;
}

@Component({
  selector: 'app-device-test',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatExpansionModule,
    MatTabsModule,
    MatSelectModule,
    MatInputModule,
    MatFormFieldModule
  ],
  template: `
    <div class="page-container">
      <div class="header-container">
        <h1>Home Assistant API Test</h1>
        
        <div class="header-actions">
          <!-- Back to Dashboard Selector Button -->
          <button mat-raised-button
                 routerLink="/selector" 
                 color="primary">
              <mat-icon>dashboard</mat-icon>
              Back to Dashboards
          </button>
        </div>
      </div>
      
      <p>This page tests the CORS proxy functionality by fetching data from various Home Assistant API endpoints</p>
      
      <div class="actions">
        <button mat-raised-button color="primary" (click)="fetchAllData()" [disabled]="loading">
          <mat-icon>refresh</mat-icon>
          Fetch All Data
        </button>
        
        <button mat-raised-button (click)="clearAllData()" [disabled]="loading">
          <mat-icon>delete</mat-icon>
          Clear All Data
        </button>
      </div>
      
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <span>Loading data...</span>
      </div>
      
      <div *ngIf="error" class="error-container">
        <mat-icon color="warn">error</mat-icon>
        <h3>Error loading data</h3>
        <p>{{ error }}</p>
      </div>
      
      <mat-tab-group *ngIf="!loading && hasData()" animationDuration="200ms">
        <!-- Devices Tab -->
        <mat-tab label="Devices ({{ devices.length }})">
          <ng-template matTabContent>
            <mat-card class="results-card">
              <mat-card-content>
                <mat-list>
                  <div *ngFor="let device of devices; let last = last">
                    <mat-list-item>
                      <mat-icon matListItemIcon>devices</mat-icon>
                      <div matListItemTitle>{{ device.name_by_user || device.name || 'Unknown Device' }}</div>
                      <div matListItemLine *ngIf="device.manufacturer || device.model">
                        <span *ngIf="device.manufacturer">{{ device.manufacturer }}</span>
                        <span *ngIf="device.model"> - {{ device.model }}</span>
                      </div>
                    </mat-list-item>
                    <mat-divider *ngIf="!last"></mat-divider>
                  </div>
                </mat-list>
              </mat-card-content>
            </mat-card>
          </ng-template>
        </mat-tab>
        
        <!-- Areas Tab -->
        <mat-tab label="Areas ({{ areas.length }})">
          <ng-template matTabContent>
            <mat-card class="results-card">
              <mat-card-content>
                <mat-list>
                  <div *ngFor="let area of areas; let last = last">
                    <mat-list-item>
                      <mat-icon matListItemIcon>location_on</mat-icon>
                      <div matListItemTitle>{{ area.name }}</div>
                      <div matListItemLine>ID: {{ area.area_id }}</div>
                    </mat-list-item>
                    <mat-divider *ngIf="!last"></mat-divider>
                  </div>
                </mat-list>
              </mat-card-content>
            </mat-card>
          </ng-template>
        </mat-tab>
        
        <!-- Entities Tab -->
        <mat-tab label="Entities ({{ entities.length }})">
          <ng-template matTabContent>
            <div class="filter-container">
              <mat-form-field appearance="outline" class="filter-field">
                <mat-label>Filter by domain</mat-label>
                <mat-select [(ngModel)]="selectedDomain" (selectionChange)="filterEntities()">
                  <mat-option value="">All domains</mat-option>
                  <mat-option *ngFor="let domain of entityDomains" [value]="domain">
                    {{ domain }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            
            <mat-card class="results-card">
              <mat-card-content>
                <mat-list>
                  <div *ngFor="let entity of filteredEntities; let last = last">
                    <mat-list-item>
                      <mat-icon matListItemIcon>{{ getDomainIcon(entity.entity_id.split('.')[0]) }}</mat-icon>
                      <div matListItemTitle>{{ entity.attributes['friendly_name'] || entity.entity_id }}</div>
                      <div matListItemLine>{{ entity.entity_id }}</div>
                      <div matListItemMeta class="entity-state">
                        {{ entity.state }}
                        <span *ngIf="entity.attributes['unit_of_measurement']">
                          {{ entity.attributes['unit_of_measurement'] }}
                        </span>
                      </div>
                    </mat-list-item>
                    <mat-divider *ngIf="!last"></mat-divider>
                  </div>
                </mat-list>
              </mat-card-content>
            </mat-card>
          </ng-template>
        </mat-tab>
        
        <!-- Config Tab -->
        <mat-tab label="Config">
          <ng-template matTabContent>
            <mat-card class="results-card">
              <mat-card-content *ngIf="config">
                <div class="config-details">
                  <div class="config-item">
                    <span class="label">Location:</span>
                    <span class="value">{{ config.location_name }}</span>
                  </div>
                  <div class="config-item">
                    <span class="label">Coordinates:</span>
                    <span class="value">{{ config.latitude }}, {{ config.longitude }}</span>
                  </div>
                  <div class="config-item">
                    <span class="label">Elevation:</span>
                    <span class="value">{{ config.elevation }} m</span>
                  </div>
                  <div class="config-item">
                    <span class="label">Time Zone:</span>
                    <span class="value">{{ config.time_zone }}</span>
                  </div>
                  <div class="config-item">
                    <span class="label">Version:</span>
                    <span class="value">{{ config.version }}</span>
                  </div>
                  <div class="config-item">
                    <span class="label">Units:</span>
                    <span class="value">
                      Temperature: {{ config.unit_system?.temperature }},
                      Length: {{ config.unit_system?.length }}
                    </span>
                  </div>
                </div>
                
                <h3>Components ({{ config.components?.length || 0 }})</h3>
                <div class="components-grid">
                  <div class="component" *ngFor="let component of config.components">
                    {{ component }}
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </ng-template>
        </mat-tab>
        
        <!-- Services Tab -->
        <mat-tab label="Services ({{ Object.keys(services).length }})">
          <ng-template matTabContent>
            <mat-accordion>
              <mat-expansion-panel *ngFor="let domain of Object.keys(services)">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    {{ domain }}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ services[domain] && services[domain].services ? Object.keys(services[domain].services).length : 0 }} services
                  </mat-panel-description>
                </mat-expansion-panel-header>
                
                <mat-list>
                  <div *ngFor="let serviceName of services[domain] && services[domain].services ? Object.keys(services[domain].services) : []; let last = last">
                    <mat-list-item>
                      <div matListItemTitle>{{ services[domain].services[serviceName].name || serviceName }}</div>
                      <div matListItemLine *ngIf="services[domain].services[serviceName]?.description">
                        {{ services[domain].services[serviceName].description }}
                      </div>
                    </mat-list-item>
                    <mat-divider *ngIf="!last"></mat-divider>
                  </div>
                </mat-list>
              </mat-expansion-panel>
            </mat-accordion>
          </ng-template>
        </mat-tab>
      </mat-tab-group>
      
      <div *ngIf="!loading && !hasData() && !error" class="empty-state">
        <mat-icon>api</mat-icon>
        <p>No data available. Click the button above to fetch data from Home Assistant.</p>
      </div>
      
      <mat-expansion-panel *ngIf="lastRequestInfo" class="debug-panel">
        <mat-expansion-panel-header>
          <mat-panel-title>
            Last Request Info
          </mat-panel-title>
        </mat-expansion-panel-header>
        
        <pre>{{ lastRequestInfo | json }}</pre>
      </mat-expansion-panel>
    </div>
  `,
  styles: [`
    .page-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .header-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .actions {
      display: flex;
      gap: 10px;
      margin: 24px 0;
    }
    
    .loading-container,
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      text-align: center;
    }
    
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 40px 0;
      opacity: 0.6;
    }
    
    .empty-state mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 8px;
    }
    
    .results-card {
      margin-bottom: 24px;
    }
    
    .debug-panel {
      margin-top: 20px;
    }
    
    .filter-container {
      margin: 16px 0;
    }
    
    .filter-field {
      width: 300px;
    }
    
    .entity-state {
      font-weight: 500;
    }
    
    .config-details {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
      margin-bottom: 32px;
    }
    
    .config-item {
      padding: 8px;
    }
    
    .label {
      font-weight: 500;
      margin-right: 8px;
      color: rgba(0,0,0,0.6);
    }
    
    .components-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 8px;
    }
    
    .component {
      background-color: #f5f5f5;
      padding: 8px;
      border-radius: 4px;
      font-size: 0.9em;
    }
    
    .spacer {
      flex: 1;
    }
    
    .service-actions {
      margin-top: 16px;
    }
    
    .service-field {
      width: 100%;
      margin-bottom: 8px;
    }
  `]
})
export class DeviceTestComponent implements OnInit {
  // Make Object available in the template
  Object = Object;
  
  // Data from API
  devices: HADevice[] = [];
  areas: HAArea[] = [];
  entities: HAEntity[] = [];
  filteredEntities: HAEntity[] = [];
  config: HAConfig | null = null;
  services: HAServices = {};
  
  // UI state
  loading = false;
  error = '';
  lastRequestInfo: any = null;
  selectedDomain = '';
  entityDomains: string[] = [];

  constructor(
    private haService: HomeAssistantService,
    private http: HttpClient,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Check if Home Assistant is configured on init
    if (this.haService.isConfigured()) {
      console.log('Home Assistant is configured');
      
      // In the mock environment or when tests are running, auto-fetch data
      if (environment.useLocalMockData) {
        this.fetchAllData();
      }
    } else {
      console.log('Home Assistant is not configured');
      this.error = 'Home Assistant service is not configured';
    }
  }

  hasData(): boolean {
    return this.devices.length > 0 || 
           this.areas.length > 0 || 
           this.entities.length > 0 ||
           !!this.config ||
           Object.keys(this.services).length > 0;
  }

  clearAllData(): void {
    this.devices = [];
    this.areas = [];
    this.entities = [];
    this.filteredEntities = [];
    this.config = null;
    this.services = {};
    this.lastRequestInfo = null;
    this.selectedDomain = '';
    this.entityDomains = [];
  }

  fetchAllData(): void {
    if (!this.haService.isConfigured()) {
      this.error = 'Home Assistant service is not configured';
      return;
    }

    this.loading = true;
    this.error = '';
    this.clearAllData();

    // Use forkJoin to fetch multiple API endpoints in parallel using the HomeAssistantService proxy
    forkJoin({
      devices: this.haService.fetchDevices().pipe(
        catchError(error => {
          console.error('Error fetching devices:', error);
          return of([]);
        })
      ),
      areas: this.haService.fetchAreas().pipe(
        catchError(error => {
          console.error('Error fetching areas:', error);
          return of([]);
        })
      ),
      entities: this.haService.fetchAllEntities().pipe(
        catchError(error => {
          console.error('Error fetching entities:', error);
          return of([]);
        })
      ),
      config: this.haService.fetchConfig().pipe(
        catchError(error => {
          console.error('Error fetching config:', error);
          return of(null);
        })
      ),
      services: this.haService.fetchServices().pipe(
        catchError(error => {
          console.error('Error fetching services:', error);
          return of({});
        })
      )
    }).pipe(
      tap(results => {
        this.devices = results.devices;
        this.areas = results.areas;
        this.entities = results.entities;
        this.filteredEntities = this.entities;
        this.config = results.config;
        this.services = results.services;
        
        // Extract unique entity domains for filtering
        this.entityDomains = [...new Set(
          this.entities.map(entity => entity.entity_id.split('.')[0])
        )].sort();
        
        // Record request info for debugging
        this.lastRequestInfo = {
          timestamp: new Date().toISOString(),
          results: {
            devices: this.devices.length,
            areas: this.areas.length,
            entities: this.entities.length,
            config: !!this.config,
            services: Object.keys(this.services).length
          }
        };
        
        console.log('All data fetched successfully:', results);
      }),
      catchError(error => {
        console.error('Error fetching data:', error);
        this.error = `Error fetching data: ${error.message || 'Unknown error'}`;
        this.lastRequestInfo = {
          timestamp: new Date().toISOString(),
          error: error.message || 'Unknown error',
          status: error.status,
          statusText: error.statusText
        };
        return of(null);
      }),
      finalize(() => {
        this.loading = false;
      })
    ).subscribe();
  }

  filterEntities(): void {
    if (!this.selectedDomain) {
      this.filteredEntities = this.entities;
    } else {
      this.filteredEntities = this.entities.filter(entity => 
        entity.entity_id.startsWith(`${this.selectedDomain}.`)
      );
    }
  }
  
  getDomainIcon(domain: string): string {
    switch (domain) {
      case 'light': return 'lightbulb';
      case 'switch': return 'toggle_on';
      case 'sensor': return 'sensors';
      case 'binary_sensor': return 'fiber_manual_record';
      case 'climate': return 'thermostat';
      case 'camera': return 'videocam';
      case 'media_player': return 'music_note';
      case 'fan': return 'mode_fan';
      case 'cover': return 'vertical_shades';
      case 'lock': return 'lock';
      case 'vacuum': return 'robot';
      case 'automation': return 'smart_toy';
      case 'script': return 'code';
      case 'scene': return 'movie';
      case 'weather': return 'cloud';
      case 'sun': return 'wb_sunny';
      case 'device_tracker': return 'location_on';
      case 'person': return 'person';
      case 'zone': return 'map';
      default: return 'device_unknown';
    }
  }
} 