import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatMenuModule } from '@angular/material/menu';
import { MatGridListModule } from '@angular/material/grid-list';
import { Router, RouterModule } from '@angular/router';
import { HomeAssistantService, HAEntity } from '../../services/home-assistant.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatMenuModule,
    MatGridListModule,
    RouterModule
  ],
  template: `
    <div class="dashboard-container">
      <mat-toolbar color="primary">
        <span>Home Assistant Dashboard</span>
        <span class="spacer"></span>
        
        <!-- Back to Dashboard Selector Button -->
        <button mat-icon-button 
               routerLink="/selector" 
               matTooltip="Back to Dashboard Selector">
            <mat-icon>dashboard</mat-icon>
        </button>
        
        <button mat-icon-button [matMenuTriggerFor]="userMenu">
          <mat-icon>account_circle</mat-icon>
        </button>
        
        <mat-menu #userMenu="matMenu">
          <button mat-menu-item (click)="navigateToDeviceTest()">
            <mat-icon>devices</mat-icon>
            <span>Device Test</span>
          </button>
          <button mat-menu-item (click)="logout()">
            <mat-icon>exit_to_app</mat-icon>
            <span>Logout</span>
          </button>
        </mat-menu>
      </mat-toolbar>
      
      <div class="content-container">
        <h2>Your Devices</h2>
        
        <div class="entity-grid">
          <mat-card *ngFor="let entity of entities" class="entity-card">
            <mat-card-header>
              <mat-icon mat-card-avatar>{{ getEntityIcon(entity) }}</mat-icon>
              <mat-card-title>{{ entity.attributes['friendly_name'] || entity.entity_id }}</mat-card-title>
              <mat-card-subtitle>{{ entity.entity_id }}</mat-card-subtitle>
            </mat-card-header>
            
            <mat-card-content>
              <div class="entity-state">
                {{ entity.state }}
                <span *ngIf="entity.attributes['unit_of_measurement']">
                  {{ entity.attributes['unit_of_measurement'] }}
                </span>
              </div>
            </mat-card-content>
            
            <mat-card-actions *ngIf="canToggle(entity)">
              <button 
                mat-button 
                color="primary" 
                (click)="toggleEntity(entity.entity_id)"
              >
                TOGGLE
              </button>
            </mat-card-actions>
          </mat-card>
          
          <div class="empty-message" *ngIf="entities.length === 0">
            <mat-icon>info</mat-icon>
            <p>No entities available. Add devices to your Home Assistant instance or check your connection.</p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
    
    .spacer {
      flex: 1 1 auto;
    }
    
    .content-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
      width: 100%;
    }
    
    .entity-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    
    .entity-card {
      height: 100%;
    }
    
    .entity-state {
      font-size: 24px;
      margin: 16px 0;
      font-weight: 500;
    }
    
    .empty-message {
      grid-column: 1 / -1;
      text-align: center;
      padding: 40px;
      background-color: #f5f5f5;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .empty-message mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
      opacity: 0.6;
    }
  `]
})
export class DashboardComponent implements OnInit {
  entities: HAEntity[] = [];
  
  constructor(
    private haService: HomeAssistantService,
    private router: Router
  ) {}
  
  ngOnInit(): void {
    // Check if Home Assistant is configured
    if (!this.haService.isConfigured()) {
      this.router.navigate(['/login']);
      return;
    }
    
    // Subscribe to entity updates
    this.haService.entityStates$.subscribe(entities => {
      this.entities = entities;
    });
    
    // Start polling if needed
    if (this.entities.length === 0) {
      this.haService.fetchAllEntities().subscribe();
    }
  }
  
  getEntityIcon(entity: HAEntity): string {
    const domain = entity.entity_id.split('.')[0];
    
    switch (domain) {
      case 'light': return 'lightbulb';
      case 'switch': return 'toggle_on';
      case 'sensor': return 'sensors';
      case 'binary_sensor': return 'fiber_manual_record';
      case 'climate': return 'thermostat';
      case 'camera': return 'videocam';
      case 'media_player': return 'music_note';
      case 'fan': return 'mode_fan';
      case 'cover': return 'vertical_shades';
      case 'lock': return 'lock';
      case 'vacuum': return 'robot';
      default: return 'device_unknown';
    }
  }
  
  canToggle(entity: HAEntity): boolean {
    const domain = entity.entity_id.split('.')[0];
    const toggleableDomains = ['light', 'switch', 'input_boolean', 'automation', 'fan', 'cover'];
    
    return toggleableDomains.includes(domain);
  }
  
  toggleEntity(entityId: string): void {
    this.haService.toggleEntity(entityId).subscribe(result => {
      if (!result.success) {
        console.error('Failed to toggle entity:', result.error);
      }
    });
  }
  
  navigateToDeviceTest(): void {
    this.router.navigate(['/device-test']);
  }
  
  logout(): void {
    // Clear any stored credentials
    localStorage.removeItem('ha_config');
    localStorage.removeItem('user_email');
    
    // Stop polling
    this.haService.stopPolling();
    
    // Navigate to login
    this.router.navigate(['/login']);
  }
} 