import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router } from '@angular/router';
import { HomeAssistantService, HAConfig } from '../../services/home-assistant.service';
import { environment } from '../../environments/environment';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="login-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Login</mat-card-title>
          <mat-card-subtitle>Connect to your Home Assistant instance</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div *ngIf="error" class="error-message">
            <mat-icon>error</mat-icon>
            <span>{{ error }}</span>
          </div>
          
          <div class="login-form">
            <ng-container *ngIf="!haApiMode">
              <!-- Username/Password login -->
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput type="email" [(ngModel)]="email" placeholder="Email">
              </mat-form-field>
              
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Password</mat-label>
                <input matInput type="password" [(ngModel)]="password" placeholder="Password">
              </mat-form-field>
            </ng-container>
            
            <ng-container *ngIf="haApiMode">
              <!-- Home Assistant API Connection -->
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Home Assistant URL</mat-label>
                <input matInput [(ngModel)]="haUrl" placeholder="http://homeassistant.local:8123">
              </mat-form-field>
              
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Long-Lived Access Token</mat-label>
                <input matInput type="password" [(ngModel)]="haToken" placeholder="Access token">
                <mat-hint>Create this in your Home Assistant profile</mat-hint>
              </mat-form-field>
            </ng-container>
            
            <div class="login-actions">
              <button 
                mat-raised-button 
                color="primary" 
                (click)="login()" 
                [disabled]="loading"
              >
                {{ haApiMode ? 'Connect' : 'Login' }}
              </button>
              
              <button 
                mat-button 
                (click)="toggleLoginMode()" 
                [disabled]="loading"
              >
                {{ haApiMode ? 'Use Email Login' : 'Connect to HA API' }}
              </button>
            </div>
            
            <div class="loading-indicator" *ngIf="loading">
              <mat-spinner diameter="30"></mat-spinner>
              <span>{{ haApiMode ? 'Connecting...' : 'Logging in...' }}</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .login-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 80vh;
      padding: 20px;
    }
    
    mat-card {
      width: 100%;
      max-width: 400px;
    }
    
    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }
    
    .login-actions {
      display: flex;
      justify-content: space-between;
      margin-top: 24px;
    }
    
    .error-message {
      background-color: #fdeded;
      color: #d32f2f;
      padding: 12px;
      border-radius: 4px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }
    
    .error-message mat-icon {
      margin-right: 8px;
    }
    
    .loading-indicator {
      display: flex;
      align-items: center;
      margin-top: 20px;
    }
    
    .loading-indicator span {
      margin-left: 10px;
    }
  `]
})
export class LoginComponent implements OnInit {
  // User credentials
  email = '';
  password = '';
  
  // HA API connection
  haUrl = '';
  haToken = '';
  
  // UI state
  loading = false;
  error = '';
  haApiMode = true; // Default to HA API connection mode
  
  constructor(
    private haService: HomeAssistantService,
    private router: Router
  ) {}
  
  ngOnInit(): void {
    // Redirect to dashboard if already authenticated
    if (this.haService.isConfigured()) {
      this.router.navigate(['/dashboard']);
    }
    
    // For development, use the environment API URL
    if (environment.homeAssistantUrl) {
      this.haUrl = environment.homeAssistantUrl;
    }
  }
  
  toggleLoginMode(): void {
    this.haApiMode = !this.haApiMode;
    this.error = ''; // Clear any previous errors
  }
  
  login(): void {
    this.loading = true;
    this.error = '';
    
    // Check if we're using mock data for development
    if (environment.useLocalMockData) {
      // For mock mode, validate against test credentials
      if (this.haApiMode) {
        // Configure with mock HA instance
        this.haService.configure({
          apiUrl: this.haUrl || environment.homeAssistantUrl,
          token: this.haToken || 'mock-token'
        });
        
        setTimeout(() => {
          this.loading = false;
          this.router.navigate(['/dashboard']);
        }, 800); // Add a small delay to simulate network request
      } else {
        // Validate email/password against test credentials
        if (this.email === '<EMAIL>' && this.password === 'testtest') {
          // Success - store user info and redirect
          localStorage.setItem('user_email', this.email);
          
          // Configure with mock HA instance
          this.haService.configure({
            apiUrl: environment.homeAssistantUrl,
            token: 'mock-token'
          });
          
          setTimeout(() => {
            this.loading = false;
            this.router.navigate(['/dashboard']);
          }, 800); // Add a small delay to simulate network request
        } else {
          // Invalid credentials
          setTimeout(() => {
            this.loading = false;
            this.error = 'Invalid email or password';
          }, 800);
        }
      }
      
      return;
    }
    
    // Real authentication logic for production
    if (this.haApiMode) {
      // HA API connection
      if (!this.haUrl) {
        this.loading = false;
        this.error = 'Home Assistant URL is required';
        return;
      }
      
      if (!this.haToken) {
        this.loading = false;
        this.error = 'Access token is required';
        return;
      }
      
      // Configure the service with the provided API URL and token
      const config: HAConfig = {
        apiUrl: this.haUrl,
        token: this.haToken
      };
      
      this.haService.configure(config);
      
      // Check connection
      this.haService.checkConnection().subscribe({
        next: (connected) => {
          this.loading = false;
          if (connected) {
            this.router.navigate(['/dashboard']);
          } else {
            this.error = 'Failed to connect to Home Assistant. Please check your URL and token.';
          }
        },
        error: (err) => {
          this.loading = false;
          this.error = `Connection error: ${err.message || 'Unknown error'}`;
        }
      });
    } else {
      // Email/password login - this would typically call an auth service
      // For now, just show an error since we don't have a backend auth system
      this.loading = false;
      this.error = 'Email login is only available in development mode with test credentials.';
    }
  }
} 