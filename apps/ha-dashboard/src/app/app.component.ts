import { Component, OnInit, OnDestroy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { DashboardLoaderService } from './dashboard/dashboard-loader.service';
import { 
  Auth, 
  signInAnonymously, 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  User 
} from '@angular/fire/auth';
import { Subscription, timer } from 'rxjs';
import { CommonModule } from '@angular/common';
import { HomeAssistantService } from './services/home-assistant.service';
import { NotificationComponent } from './shared/components/notification/notification.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Material Imports
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule } from '@angular/material/snack-bar';

@Component({
  standalone: true,
  imports: [
    RouterModule, 
    CommonModule,
    MatProgressSpinnerModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatDividerModule,
    MatSnackBarModule,
    NotificationComponent
  ],
  selector: 'app-root',
  template: `
    <!-- Authentication loading state -->
    <div *ngIf="isAuthenticating" class="auth-loading mat-elevation-z2">
      <mat-spinner [diameter]="50"></mat-spinner>
      <p>Connecting to Firebase...</p>
    </div>

    <!-- Authentication error state -->
    <mat-card *ngIf="authError && !isAuthenticating" class="auth-error mat-elevation-z4">
      <mat-card-header>
        <mat-icon class="error-icon">error</mat-icon>
        <mat-card-title>Authentication Error</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p>{{ authError }}</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button color="primary" (click)="signInWithTestAccount()">Sign in with TEST ACCOUNT</button>
        <button mat-stroked-button (click)="signInAnonymously()">Try Anonymous Login</button>
      </mat-card-actions>
      <mat-card-footer>
        <p class="hint">Note: For anonymous authentication to work, it must be enabled in the Firebase Console.</p>
      </mat-card-footer>
    </mat-card>

    <!-- Firestore connection error state -->
    <mat-card *ngIf="connectionError && !isAuthenticating && !authError" class="connection-error mat-elevation-z4">
      <mat-card-header>
        <mat-icon class="error-icon">wifi_off</mat-icon>
        <mat-card-title>Connection Error</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p>{{ connectionError }}</p>
        <div class="status" *ngIf="isConnecting">
          <mat-spinner [diameter]="24" class="small-loader"></mat-spinner>
          <p>Attempting to reconnect...</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button color="primary" *ngIf="!isConnecting" (click)="tryReconnect()">
          <mat-icon>refresh</mat-icon> Try Again
        </button>
      </mat-card-actions>
      <mat-card-footer>
        <p class="hint">Make sure you have a stable internet connection and that Firebase services are not blocked.</p>
      </mat-card-footer>
    </mat-card>

    <!-- Main app content when authenticated and connected -->
    <router-outlet *ngIf="user && !authError && !isAuthenticating && (!connectionError || hasFirestoreConnection)"></router-outlet>
    
    <!-- Global notification component -->
    <app-notification></app-notification>
  `,
  styles: [`
    :host {
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow: hidden;
    }
    
    .auth-loading {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 30px;
      border-radius: 8px;
      background-color: white;
      text-align: center;
      z-index: 1000;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }
    
    .auth-error, .connection-error {
      max-width: 500px;
      margin: 40px auto;
      padding: 20px;
    }
    
    .error-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
      color: #f44336;
      margin-right: 12px;
    }
    
    mat-card-actions {
      display: flex;
      gap: 10px;
      padding: 16px;
    }
    
    .hint {
      font-size: 0.8rem;
      color: rgba(0, 0, 0, 0.54);
      margin: 8px 16px;
    }
    
    .status {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 16px;
    }
    
    .small-loader {
      margin-left: auto;
    }
  `]
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'ha-dashboard';
  authError: string | null = null;
  isAuthenticating = false;
  user: User | null = null;
  
  // Connection states
  connectionError: string | null = null;
  isConnecting = false;
  hasFirestoreConnection = true;
  
  // Test account credentials - replace with your own secure credentials in production
  private readonly TEST_EMAIL = '<EMAIL>';
  private readonly TEST_PASSWORD = 'Test123!';
  
  private authStateSubscription?: Subscription;
  private reconnectSubscription?: Subscription;
  
  constructor(
    private dashboardLoader: DashboardLoaderService,
    private auth: Auth,
    private homeAssistantService: HomeAssistantService
  ) {}
  
  ngOnInit(): void {
    // Monitor authentication state
    this.isAuthenticating = true;
    this.authStateSubscription = new Subscription();
    
    // Subscribe to auth state changes
    this.authStateSubscription.add(
      this.auth.onAuthStateChanged((user) => {
        this.user = user;
        this.isAuthenticating = false;
        
        if (user) {
          console.log('User is authenticated:', user.uid);
          this.initializeDashboards();
          
          // Check Home Assistant connection if configured
          if (this.homeAssistantService.isConfigured()) {
            this.homeAssistantService.checkConnection().subscribe(connected => {
              console.log('Home Assistant connection status:', connected ? 'Connected' : 'Disconnected');
            });
          }
        } else {
          console.log('User is not authenticated, signing in with test account');
          this.signInWithTestAccount();
        }
      }, (error) => {
        console.error('Auth state error:', error);
        this.authError = error.message;
        this.isAuthenticating = false;
      })
    );
  }
  
  ngOnDestroy(): void {
    if (this.authStateSubscription) {
      this.authStateSubscription.unsubscribe();
    }
    
    if (this.reconnectSubscription) {
      this.reconnectSubscription.unsubscribe();
    }
  }
  
  /**
   * Sign in with anonymous authentication
   * Note: This requires anonymous auth to be enabled in Firebase console
   */
  public signInAnonymously(): void {
    this.isAuthenticating = true;
    this.authError = null;
    
    signInAnonymously(this.auth).then(() => {
      console.log('Anonymous auth successful');
    }).catch((error) => {
      console.error('Anonymous auth failed:', error);
      
      // If anonymous auth fails, try with test account instead
      if (error.code === 'auth/admin-restricted-operation') {
        console.log('Anonymous auth is disabled, trying test account login');
        this.signInWithTestAccount();
      } else {
        this.authError = error.message;
        this.isAuthenticating = false;
      }
    });
  }
  
  /**
   * Sign in with a test email/password account
   * This is an alternative if anonymous auth is disabled
   */
  public signInWithTestAccount(): void {
    this.isAuthenticating = true;
    this.authError = null;
    
    signInWithEmailAndPassword(this.auth, this.TEST_EMAIL, this.TEST_PASSWORD)
      .then(() => {
        console.log('Test account sign-in successful');
      })
      .catch((error) => {
        console.log('Test account sign-in failed, trying to create account:', error);
        
        // If the account doesn't exist, create it
        if (error.code === 'auth/user-not-found') {
          this.createTestAccount();
        } else {
          this.authError = `Authentication Error: ${error.message}`;
          this.isAuthenticating = false;
        }
      });
  }
  
  /**
   * Create a test account if it doesn't exist
   */
  private createTestAccount(): void {
    createUserWithEmailAndPassword(this.auth, this.TEST_EMAIL, this.TEST_PASSWORD)
      .then(() => {
        console.log('Test account created successfully');
      })
      .catch((error) => {
        console.error('Failed to create test account:', error);
        this.authError = `Failed to create test account: ${error.message}`;
        this.isAuthenticating = false;
      });
  }
  
  /**
   * Initialize dashboards after authentication
   */
  private initializeDashboards(): void {
    // Initialize test dashboards if none exist
    this.dashboardLoader.ensureDashboardsExist().subscribe({
      next: () => {
        console.log('Dashboard initialization completed');
        this.hasFirestoreConnection = true;
        this.connectionError = null;
      },
      error: (error) => {
        console.error('Error initializing dashboards:', error);
        
        // If there's a connection error, set the connection state
        if (error.message && (
            error.message.includes('network') || 
            error.message.includes('connection') || 
            error.message.includes('unavailable') ||
            error.message.includes('timeout') ||
            error.message.includes('WebChannel'))) {
          this.connectionError = 'Unable to connect to Firebase. Please check your internet connection.';
          this.hasFirestoreConnection = false;
          this.startReconnectionAttempts();
        } else {
          // Even if initialization fails, we'll allow the app to continue
          // The dashboard component will show the empty state with a create button
          console.log('Users will be prompted to create a dashboard manually');
        }
      }
    });
  }
  
  /**
   * Try to reconnect to Firestore periodically
   */
  private startReconnectionAttempts(): void {
    // Cancel any existing reconnection attempts
    if (this.reconnectSubscription) {
      this.reconnectSubscription.unsubscribe();
    }
    
    console.log('Starting reconnection attempts...');
    this.isConnecting = true;
    
    // Try to reconnect every 5 seconds
    this.reconnectSubscription = timer(0, 5000).subscribe(() => {
      console.log('Attempting to reconnect...');
      
      this.dashboardLoader.reconnectFirestore()
        .then(() => {
          // Try a test query to see if we're connected
          this.dashboardLoader.getDashboards().subscribe({
            next: () => {
              console.log('Reconnected successfully!');
              this.hasFirestoreConnection = true;
              this.connectionError = null;
              this.isConnecting = false;
              
              // Stop reconnection attempts
              if (this.reconnectSubscription) {
                this.reconnectSubscription.unsubscribe();
                this.reconnectSubscription = undefined;
              }
              
              // Re-initialize
              this.initializeDashboards();
            },
            error: (err) => {
              console.log('Reconnection test query failed:', err);
              // Continue trying...
            }
          });
        })
        .catch(error => {
          console.error('Reconnection attempt failed:', error);
        });
    });
  }
  
  /**
   * Manually trigger a reconnection attempt
   */
  public tryReconnect(): void {
    if (!this.hasFirestoreConnection) {
      this.startReconnectionAttempts();
    }
  }
}
