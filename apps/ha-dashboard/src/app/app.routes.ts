import { Route } from '@angular/router';
import { DeviceTestComponent } from './pages/device-test/device-test.component';
import { LoginComponent } from './pages/login/login.component';
import { DashboardComponent as GridsterDashboardComponent } from './dashboard/dashboard.component';
import { DashboardComponent as SimpleDashboardComponent } from './pages/dashboard/dashboard.component';
import { DashboardSelectorComponent } from './shared/components/dashboard-selector/dashboard-selector.component';

export const appRoutes: Route[] = [
    {
        path: '',
        redirectTo: 'selector',
        pathMatch: 'full'
    },
    {
        path: 'login',
        component: LoginComponent,
    },
    {
        path: 'selector',
        component: DashboardSelectorComponent,
    },
    {
        path: 'gridster',
        component: GridsterDashboardComponent,
    },
    {
        path: 'simple',
        component: SimpleDashboardComponent,
    },
    {
        path: 'device-test',
        component: DeviceTestComponent,
    },
    {
        path: '**',
        redirectTo: 'selector'
    }
];
