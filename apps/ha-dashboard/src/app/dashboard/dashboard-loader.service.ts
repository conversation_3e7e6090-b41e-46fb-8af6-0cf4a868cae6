import { Injectable, Inject } from '@angular/core';
import { Firestore, collection, doc, getDoc, getDocs, addDoc, updateDoc, deleteDoc, query, where, DocumentData, Query, Timestamp, FirestoreError, enableNetwork, disableNetwork, connectFirestoreEmulator } from '@angular/fire/firestore';
import { Observable, from, throwError, of, forkJoin, timer } from 'rxjs';
import { map, catchError, switchMap, retry, timeout, delay } from 'rxjs/operators';
import { GridsterConfig } from 'angular-gridster2';
import { 
  Dashboard, 
  DashboardItem, 
  DashboardSnapshot,
  TileType
} from '../models/dashboard.models';

// Define a FirestoreDocument interface for type safety
interface FirestoreDocument {
  id: string;
  data: () => Record<string, unknown>;
}

@Injectable({
  providedIn: 'root'
})
export class DashboardLoaderService {
  private readonly dashboardsCollection = 'ha-dashboard-dashboards';
  private readonly snapshotsCollection = 'ha-dashboard-snapshots';
  private isNetworkEnabled = true;
  
  constructor(@Inject(Firestore) private firestore: Firestore) { 
    // Add connection state listener
    this.handleConnectionStateChanges();
  }

  /**
   * Monitor Firestore connection state changes
   */
  private handleConnectionStateChanges(): void {
    // Connection state listeners would go here
    // Note: @angular/fire doesn't expose connection state directly like the JS SDK
    // We'll implement retry logic in our queries instead
  }

  /**
   * Try to reconnect to Firestore if disconnected
   */
  public async reconnectFirestore(): Promise<void> {
    try {
      if (!this.isNetworkEnabled) {
        console.log('Attempting to reconnect to Firestore...');
        await enableNetwork(this.firestore);
        this.isNetworkEnabled = true;
        console.log('Reconnected to Firestore successfully');
      }
    } catch (error) {
      console.error('Failed to reconnect to Firestore:', error);
      throw error;
    }
  }

  /**
   * Disable Firestore network (use for offline mode)
   */
  public async goOffline(): Promise<void> {
    try {
      if (this.isNetworkEnabled) {
        await disableNetwork(this.firestore);
        this.isNetworkEnabled = false;
        console.log('Firestore network disabled, using offline cache');
      }
    } catch (error) {
      console.error('Failed to disable network:', error);
      throw error;
    }
  }

  /**
   * Handles Firestore errors with better context and error messages
   */
  private handleFirestoreError(operation: string, error: any): Observable<never> {
    console.error(`Error during ${operation}:`, error);
    
    let errorMessage = 'An unknown error occurred';
    let shouldRetry = false;
    
    // Check if it's a FirestoreError type
    if (error && error.code) {
      switch (error.code) {
        case 'permission-denied':
          errorMessage = 'You do not have permission to access this data. Check Firestore rules.';
          shouldRetry = false;
          break;
        case 'unavailable':
          errorMessage = 'Firebase service is currently unavailable. Please try again later.';
          shouldRetry = true;
          break;
        case 'not-found':
          errorMessage = 'The requested document was not found.';
          shouldRetry = false;
          break;
        case 'unauthenticated':
          errorMessage = 'Authentication required to access this data.';
          shouldRetry = false;
          break;
        case 'cancelled':
          errorMessage = 'The operation was cancelled.';
          shouldRetry = true;
          break;
        case 'deadline-exceeded':
          errorMessage = 'Operation timed out. Please check your network connection.';
          shouldRetry = true;
          break;
        case 'failed-precondition':
          if (error.message && error.message.includes('offline')) {
            errorMessage = 'Firestore is offline. Please check your internet connection.';
            shouldRetry = true;
          } else {
            errorMessage = 'Operation failed: ' + error.message;
            shouldRetry = false;
          }
          break;
        default:
          errorMessage = error.message || 'An error occurred while accessing Firestore';
          shouldRetry = error.code === 'unknown';
      }
    } else if (error && error.message) {
      errorMessage = error.message;
      // Network errors should be retried
      shouldRetry = error.message.includes('network') || 
                   error.message.includes('timeout') || 
                   error.message.includes('connection');
    }
    
    // For network-related errors, try to reconnect
    if (shouldRetry) {
      this.reconnectFirestore().catch(err => console.warn('Failed to reconnect:', err));
      return timer(2000).pipe(
        switchMap(() => throwError(() => new Error(`Failed during ${operation}: ${errorMessage} (Retrying...)`)))
      );
    }
    
    return throwError(() => new Error(`Failed during ${operation}: ${errorMessage}`));
  }

  /**
   * Converts a GridsterConfig to a JSON-serializable object for Firestore
   * by removing any function properties
   */
  private serializeGridsterConfig(config: GridsterConfig): Record<string, unknown> {
    if (!config) return {};
    
    // Create a deep copy and remove function properties
    // This ensures we don't store functions in Firestore documents
    return JSON.parse(JSON.stringify(config));
  }

  /**
   * Get all dashboards for the current user with retry logic
   */
  getDashboards(userId?: string): Observable<Dashboard[]> {
    const dashboardsCollection = collection(this.firestore, this.dashboardsCollection);
    let dashboardsQuery: Query<DocumentData> = query(dashboardsCollection);
    
    if (userId) {
      dashboardsQuery = query(dashboardsCollection, where('userId', '==', userId));
    }
    
    return from(getDocs(dashboardsQuery)).pipe(
      timeout(15000), // Add a timeout to prevent hanging
      retry(2), // Retry failed requests
      map(snapshot => {
        if (snapshot.empty) {
          return [];
        }
        
        return snapshot.docs.map((doc: FirestoreDocument) => {
          const data = doc.data() as Record<string, unknown>;
          return {
            id: doc.id,
            name: data['name'],
            items: data['items'] || [],
            gridsterConfig: data['gridsterConfig'],
            createdAt: data['createdAt'] ? (data['createdAt'] as Timestamp).toDate() : undefined,
            updatedAt: data['updatedAt'] ? (data['updatedAt'] as Timestamp).toDate() : undefined,
            userId: data['userId'],
            description: data['description'],
            isDefault: data['isDefault'] || false,
            tags: data['tags'] || [],
            isEditingEnabled: data['isEditingEnabled'] !== undefined ? data['isEditingEnabled'] : true
          } as Dashboard;
        });
      }),
      catchError(error => this.handleFirestoreError('fetching dashboards', error))
    );
  }

  /**
   * Get the default dashboard or the first one
   */
  getDefaultDashboard(userId?: string): Observable<Dashboard | null> {
    const dashboardsCollection = collection(this.firestore, this.dashboardsCollection);
    let dashboardsQuery: Query<DocumentData>;
    
    if (userId) {
      dashboardsQuery = query(
        dashboardsCollection, 
        where('userId', '==', userId),
        where('isDefault', '==', true)
      );
    } else {
      dashboardsQuery = query(
        dashboardsCollection,
        where('isDefault', '==', true)
      );
    }
    
    return from(getDocs(dashboardsQuery)).pipe(
      map(snapshot => {
        if (snapshot.empty) {
          // If no default dashboard, get any dashboard
          return null;
        }
        
        const data = snapshot.docs[0].data() as Record<string, unknown>;
        return {
          id: snapshot.docs[0].id,
          name: data['name'],
          items: data['items'] || [],
          gridsterConfig: data['gridsterConfig'],
          createdAt: data['createdAt'] ? (data['createdAt'] as Timestamp).toDate() : undefined,
          updatedAt: data['updatedAt'] ? (data['updatedAt'] as Timestamp).toDate() : undefined,
          userId: data['userId'],
          description: data['description'],
          isDefault: data['isDefault'] || false,
          tags: data['tags'] || [],
          isEditingEnabled: data['isEditingEnabled'] !== undefined ? data['isEditingEnabled'] : true
        } as Dashboard;
      }),
      catchError(error => this.handleFirestoreError('fetching default dashboard', error))
    );
  }

  /**
   * Load a dashboard by ID
   */
  getDashboard(id: string): Observable<Dashboard> {
    const docRef = doc(this.firestore, `${this.dashboardsCollection}/${id}`);
    
    return from(getDoc(docRef)).pipe(
      map(docSnapshot => {
        if (!docSnapshot.exists()) {
          throw new Error(`Dashboard with ID ${id} not found`);
        }
        
        const data = docSnapshot.data() as Record<string, unknown>;
        return {
          id: docSnapshot.id,
          name: data['name'],
          items: data['items'] || [],
          gridsterConfig: data['gridsterConfig'],
          createdAt: data['createdAt'] ? (data['createdAt'] as Timestamp).toDate() : undefined,
          updatedAt: data['updatedAt'] ? (data['updatedAt'] as Timestamp).toDate() : undefined,
          userId: data['userId'],
          description: data['description'],
          isDefault: data['isDefault'] || false,
          tags: data['tags'] || [],
          isEditingEnabled: data['isEditingEnabled'] !== undefined ? data['isEditingEnabled'] : true
        } as Dashboard;
      }),
      catchError(error => this.handleFirestoreError(`loading dashboard with ID ${id}`, error))
    );
  }

  /**
   * Save a new dashboard
   */
  createDashboard(dashboard: Dashboard): Observable<string> {
    const dashboardsCollection = collection(this.firestore, this.dashboardsCollection);
    
    // Process the dashboard to make it Firestore-compatible
    const dashboardToSave = {
      ...dashboard,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Ensure the items array exists
    if (!dashboardToSave.items) {
      dashboardToSave.items = [];
    }
    
    // Validate each item to ensure it has required properties
    dashboardToSave.items = dashboardToSave.items.map(item => {
      // Ensure each item has an id and type
      if (!item.id) {
        item.id = this.generateUniqueId();
      }
      
      // Ensure type is one of the valid types
      if (!item.type || !['floorplan', 'default', 'weather', 'camera', 'device', 'sensor', 'chart', 'custom'].includes(item.type)) {
        item.type = 'default';
      }
      
      return item;
    });
    
    // Serialize GridsterConfig to remove functions
    if (dashboardToSave.gridsterConfig) {
      dashboardToSave.gridsterConfig = this.serializeGridsterConfig(dashboardToSave.gridsterConfig as any);
    }
    
    return from(addDoc(dashboardsCollection, dashboardToSave)).pipe(
      map(docRef => {
        // Create an initial snapshot after creation
        this.createSnapshot(docRef.id, dashboardToSave.items, dashboardToSave.gridsterConfig);
        return docRef.id;
      }),
      catchError(error => this.handleFirestoreError('creating dashboard', error))
    );
  }

  /**
   * Generate a unique ID for items if needed
   */
  private generateUniqueId(): string {
    return 'item_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Update an existing dashboard
   */
  updateDashboard(dashboard: Dashboard): Observable<void> {
    if (!dashboard.id) {
      return throwError(() => new Error('Dashboard ID is required for update'));
    }
    
    const docRef = doc(this.firestore, `${this.dashboardsCollection}/${dashboard.id}`);
    
    // Process dashboard for Firestore
    const dashboardToUpdate = {
      ...dashboard,
      updatedAt: new Date()
    };
    
    // Ensure the items array exists
    if (!dashboardToUpdate.items) {
      dashboardToUpdate.items = [];
    }
    
    // Validate each item to ensure it has required properties
    dashboardToUpdate.items = dashboardToUpdate.items.map(item => {
      // Ensure each item has an id and type
      if (!item.id) {
        item.id = this.generateUniqueId();
      }
      
      // Ensure type is one of the valid types
      if (!item.type || !['floorplan', 'default', 'weather', 'camera', 'device', 'sensor', 'chart', 'custom'].includes(item.type)) {
        item.type = 'default';
      }
      
      return item;
    });
    
    // Serialize GridsterConfig to remove functions
    if (dashboardToUpdate.gridsterConfig) {
      dashboardToUpdate.gridsterConfig = this.serializeGridsterConfig(dashboardToUpdate.gridsterConfig as any);
    }
    
    // Remove id to avoid duplication
    const { id, ...dashboardData } = dashboardToUpdate;
    
    return from(updateDoc(docRef, dashboardData)).pipe(
      map(() => {
        // Create a snapshot after update
        this.createSnapshot(dashboard.id as string, dashboard.items, dashboardToUpdate.gridsterConfig);
      }),
      catchError(error => this.handleFirestoreError(`updating dashboard with ID ${dashboard.id}`, error))
    );
  }

  /**
   * Delete a dashboard by ID
   */
  deleteDashboard(id: string): Observable<void> {
    const docRef = doc(this.firestore, `${this.dashboardsCollection}/${id}`);
    
    return from(deleteDoc(docRef)).pipe(
      catchError(error => this.handleFirestoreError(`deleting dashboard with ID ${id}`, error))
    );
  }

  /**
   * Save dashboard layout changes (only update the items and config)
   */
  saveDashboardLayout(id: string, items: DashboardItem[], config?: GridsterConfig): Observable<void> {
    const docRef = doc(this.firestore, `${this.dashboardsCollection}/${id}`);
    
    // Validate items array
    let validatedItems = items || [];
    
    // Validate each item to ensure it has required properties
    validatedItems = validatedItems.map(item => {
      // Ensure each item has an id and type
      if (!item.id) {
        item.id = this.generateUniqueId();
      }
      
      // Ensure type is one of the valid types
      if (!item.type || !['floorplan', 'default', 'weather', 'camera', 'device', 'sensor', 'chart', 'custom'].includes(item.type)) {
        item.type = 'default';
      }
      
      return item;
    });
    
    const update: Record<string, unknown> = {
      items: validatedItems,
      updatedAt: new Date()
    };
    
    if (config) {
      // Serialize GridsterConfig to remove functions
      update['gridsterConfig'] = this.serializeGridsterConfig(config);
    }
    
    return from(updateDoc(docRef, update as { [key: string]: any })).pipe(
      map(() => {
        // Create a snapshot after saving layout
        const gridsterConfig = update['gridsterConfig'] as Record<string, unknown> | undefined;
        this.createSnapshot(id, validatedItems, gridsterConfig);
      }),
      catchError(error => this.handleFirestoreError(`saving layout for dashboard with ID ${id}`, error))
    );
  }

  /**
   * Set a dashboard as default
   */
  setAsDefault(id: string, userId?: string): Observable<void> {
    // First, remove default status from all dashboards for this user
    const dashboardsCollection = collection(this.firestore, this.dashboardsCollection);
    let dashboardsQuery: Query<DocumentData>;
    
    if (userId) {
      dashboardsQuery = query(
        dashboardsCollection, 
        where('userId', '==', userId),
        where('isDefault', '==', true)
      );
    } else {
      dashboardsQuery = query(
        dashboardsCollection,
        where('isDefault', '==', true)
      );
    }
    
    return from(getDocs(dashboardsQuery)).pipe(
      switchMap(snapshot => {
        // Remove default status from previous default dashboards
        const updatePromises: Promise<void>[] = [];
        snapshot.docs.forEach((document: FirestoreDocument) => {
          if (document.id !== id) { // Skip if it's the same dashboard
            const docRef = doc(this.firestore, `${this.dashboardsCollection}/${document.id}`);
            updatePromises.push(updateDoc(docRef, { isDefault: false }));
          }
        });
        
        return Promise.all(updatePromises);
      }),
      switchMap(() => {
        // Now set the new default
        const docRef = doc(this.firestore, `${this.dashboardsCollection}/${id}`);
        return updateDoc(docRef, { 
          isDefault: true,
          updatedAt: new Date()
        });
      }),
      catchError(error => this.handleFirestoreError(`setting dashboard ${id} as default`, error))
    );
  }

  /**
   * Create a snapshot of the current dashboard state for undo/redo
   */
  private createSnapshot(dashboardId: string, items: DashboardItem[], config?: Record<string, unknown>): void {
    const snapshotsCollection = collection(this.firestore, this.snapshotsCollection);
    
    // Validate items array
    let validatedItems = items || [];
    
    // Make a deep copy to avoid reference issues and ensure proper serialization
    validatedItems = JSON.parse(JSON.stringify(validatedItems));
    
    const snapshot: DashboardSnapshot = {
      timestamp: new Date(),
      dashboardId,
      items: validatedItems,
      gridsterConfig: config ? JSON.parse(JSON.stringify(config)) : undefined
    };
    
    // Limit number of snapshots per dashboard (keep last 10)
    const snapshotsQuery = query(
      snapshotsCollection,
      where('dashboardId', '==', dashboardId)
    );
    
    getDocs(snapshotsQuery).then((querySnapshot: any) => {
      // Get all snapshots for this dashboard
      const snapshots = querySnapshot.docs.map((doc: any) => ({
        id: doc.id,
        timestamp: (doc.data()['timestamp'] as Timestamp).toDate()
      }));
      
      // Sort by timestamp (newest first)
      snapshots.sort((a: { timestamp: Date }, b: { timestamp: Date }) => 
        b.timestamp.getTime() - a.timestamp.getTime()
      );
      
      // Add the new snapshot
      addDoc(snapshotsCollection, snapshot).then(() => {
        // If we have more than 10 snapshots, delete the oldest ones
        if (snapshots.length >= 10) {
          const toDelete = snapshots.slice(9);
          toDelete.forEach((oldSnapshot: { id: string }) => {
            const docRef = doc(this.firestore, `${this.snapshotsCollection}/${oldSnapshot.id}`);
            deleteDoc(docRef).catch((error: Error) => {
              console.error(`Failed to delete old snapshot ${oldSnapshot.id}:`, error);
            });
          });
        }
      }).catch((error: Error) => {
        console.error('Failed to create snapshot:', error);
      });
    }).catch((error: Error) => {
      console.error('Failed to get existing snapshots:', error);
      // Still try to add the new snapshot
      addDoc(snapshotsCollection, snapshot).catch((innerError: Error) => {
        console.error('Failed to create snapshot after snapshot query error:', innerError);
      });
    });
  }

  /**
   * Get snapshots for a dashboard (for undo/redo functionality)
   */
  getDashboardSnapshots(dashboardId: string): Observable<DashboardSnapshot[]> {
    const snapshotsCollection = collection(this.firestore, this.snapshotsCollection);
    const snapshotsQuery = query(
      snapshotsCollection,
      where('dashboardId', '==', dashboardId)
    );
    
    return from(getDocs(snapshotsQuery)).pipe(
      map(snapshot => {
        if (snapshot.empty) {
          return [];
        }
        
        const snapshots = snapshot.docs.map((doc: FirestoreDocument) => {
          const data = doc.data() as Record<string, unknown>;
          return {
            id: doc.id,
            timestamp: (data['timestamp'] as Timestamp).toDate(),
            dashboardId: data['dashboardId'] as string,
            items: (data['items'] || []) as DashboardItem[],
            gridsterConfig: data['gridsterConfig'] as Record<string, unknown> | undefined
          } as DashboardSnapshot & { id: string };
        });
        
        // Sort by timestamp (newest first)
        return snapshots.sort((a: DashboardSnapshot, b: DashboardSnapshot) => 
          b.timestamp.getTime() - a.timestamp.getTime()
        );
      }),
      catchError(error => this.handleFirestoreError(`fetching snapshots for dashboard ${dashboardId}`, error))
    );
  }

  /**
   * Restore a dashboard from a snapshot
   */
  restoreSnapshot(snapshotId: string): Observable<void> {
    const snapshotRef = doc(this.firestore, `${this.snapshotsCollection}/${snapshotId}`);
    
    return from(getDoc(snapshotRef)).pipe(
      switchMap(docSnapshot => {
        if (!docSnapshot.exists()) {
          throw new Error(`Snapshot with ID ${snapshotId} not found`);
        }
        
        const data = docSnapshot.data() as DashboardSnapshot;
        const dashboardId = data.dashboardId;
        const dashboardRef = doc(this.firestore, `${this.dashboardsCollection}/${dashboardId}`);
        
        return updateDoc(dashboardRef, {
          items: data.items,
          gridsterConfig: data.gridsterConfig,
          updatedAt: new Date()
        });
      }),
      catchError(error => this.handleFirestoreError(`restoring snapshot ${snapshotId}`, error))
    );
  }

  /**
   * Initialize test dashboards if none exist
   * This is useful for first-time setup or demo purposes
   */
  initializeTestDashboards(): Observable<void> {
    return this.getDashboards().pipe(
      switchMap(existingDashboards => {
        // If dashboards already exist, don't create test ones
        if (existingDashboards.length > 0) {
          console.log('Dashboards already exist, skipping test data creation');
          return of(void 0);
        }

        console.log('Creating test dashboards...');
        
        // Create array for our test dashboards
        const testDashboards: Omit<Dashboard, 'id'>[] = [
          // Default dashboard with floorplan
          {
            name: 'Home Dashboard',
            description: 'Default home dashboard with floorplan view',
            isDefault: true,
            items: [
              // Floorplan tile
              { 
                cols: 6, rows: 12, y: 0, x: 0, 
                type: 'floorplan', 
                id: 'floorplan1',
                imagePath: '/floorplan.svg',
                title: 'Main Floor',
                interactive: true,
                // Sample room data - these would reference your actual room images
                rooms: {
                  'living': {
                    id: 'living',
                    name: 'Living Room',
                    imagePath: '/assets/images/rooms/living.png',
                    type: 'living',
                    description: 'Main living area with comfortable seating and entertainment center',
                    devices: ['Living Room Lights', 'TV', 'Smart Speaker', 'Thermostat']
                  },
                  'kitchen': {
                    id: 'kitchen',
                    name: 'Kitchen',
                    imagePath: '/assets/images/rooms/kitchen.png',
                    type: 'kitchen',
                    description: 'Modern kitchen with island and smart appliances',
                    devices: ['Kitchen Lights', 'Refrigerator', 'Coffee Maker', 'Dishwasher']
                  },
                  'hallway': {
                    id: 'hallway',
                    name: 'Main Hallway',
                    imagePath: '/assets/images/rooms/hallway.png',
                    type: 'other',
                    description: 'Central hallway connecting all rooms',
                    devices: ['Hallway Lights', 'Motion Sensor']
                  }
                }
              },
              // Status overview tile
              { 
                cols: 6, rows: 4, y: 0, x: 6, 
                type: 'default', 
                id: 'status1',
                title: 'Home Status' 
              },
              // Controls tile
              { 
                cols: 6, rows: 4, y: 4, x: 6, 
                type: 'default', 
                id: 'controls1',
                title: 'Quick Controls' 
              },
              // Energy tile
              { 
                cols: 6, rows: 4, y: 8, x: 6, 
                type: 'default', 
                id: 'energy1',
                title: 'Energy Usage' 
              }
            ],
            tags: ['home', 'default', 'floorplan']
          },
          
          // Secondary dashboard - outdoor focused
          {
            name: 'Outdoor Dashboard',
            description: 'Monitor and control outdoor areas and garden',
            isDefault: false,
            items: [
              // Weather tile
              { 
                cols: 4, rows: 6, y: 0, x: 0, 
                type: 'weather', 
                id: 'weather1',
                title: 'Weather' 
              },
              // Garden status
              { 
                cols: 4, rows: 6, y: 0, x: 4, 
                type: 'default', 
                id: 'garden1',
                title: 'Garden Status' 
              },
              // Security cameras
              { 
                cols: 4, rows: 6, y: 0, x: 8, 
                type: 'default', 
                id: 'security1',
                title: 'Security Cameras' 
              },
              // Irrigation controls
              { 
                cols: 6, rows: 6, y: 6, x: 0, 
                type: 'default', 
                id: 'irrigation1',
                title: 'Irrigation Controls' 
              },
              // Outdoor lighting
              { 
                cols: 6, rows: 6, y: 6, x: 6, 
                type: 'default', 
                id: 'lighting1',
                title: 'Outdoor Lighting' 
              }
            ],
            tags: ['outdoor', 'garden', 'security']
          }
        ];

        // Create each dashboard
        const createOperations = testDashboards.map(dashboard => 
          this.createDashboard(dashboard)
        );

        // Wait for all dashboard creations to complete
        return forkJoin(createOperations).pipe(
          map(() => {
            console.log('Test dashboards created successfully');
            return void 0;
          })
        );
      }),
      catchError(error => this.handleFirestoreError('initializing test dashboards', error))
    );
  }

  /**
   * Check if dashboards exist and initialize test data if needed
   * This should be called during application startup
   */
  ensureDashboardsExist(): Observable<void> {
    return this.getDashboards().pipe(
      switchMap(dashboards => {
        if (dashboards.length === 0) {
          console.log('No dashboards found, initializing test data...');
          return this.initializeTestDashboards().pipe(
            catchError(error => {
              console.error('Failed to initialize test dashboards, user will need to create one manually:', error);
              // Return a completed observable instead of throwing an error
              // This allows the app to continue loading even if test dashboard creation fails
              return of(void 0);
            })
          );
        }
        console.log(`Found ${dashboards.length} existing dashboards`);
        return of(void 0);
      })
    );
  }

  /**
   * Update the editing state of a dashboard
   */
  updateDashboardEditState(dashboardId: string, isEditingEnabled: boolean): Observable<void> {
    const docRef = doc(this.firestore, `${this.dashboardsCollection}/${dashboardId}`);
    
    const update = {
      isEditingEnabled,
      updatedAt: new Date()
    };
    
    return from(updateDoc(docRef, update)).pipe(
      catchError(error => this.handleFirestoreError(`updating dashboard edit state for ID ${dashboardId}`, error))
    );
  }
}
