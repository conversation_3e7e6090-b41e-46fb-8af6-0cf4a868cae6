.dashboard-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    position: relative;
    font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    background-color: var(--background-secondary, #ffffff);
    color: var(--text-color, rgba(0, 0, 0, 0.87));
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    z-index: 10;
    height: 64px;
}

.dashboard-header h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: var(--text-color, rgba(0, 0, 0, 0.87));
}

.spacer {
    flex: 1;
}

.connection-status {
    display: flex;
    align-items: center;
    margin-right: 16px;
    font-size: 14px;
    color: var(--text-color-secondary, rgba(0, 0, 0, 0.6));
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #f44336;
    margin-right: 8px;
}

.connection-status.connected .status-dot {
    background-color: #4caf50;
}

.status-text {
    font-weight: 500;
}

.dashboard-actions {
    display: flex;
    align-items: center;
}

.error-message {
    background-color: var(--error-color, #f44336);
    color: white;
    padding: 8px 16px;
    margin: 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.error-message mat-icon {
    margin-right: 8px;
}

.dashboard-grid {
    flex: 1;
    overflow: auto;
    padding: 16px;
    background-color: var(--background-color, #f5f5f5);
    
    /* Ensure smooth scrolling */
    scroll-behavior: smooth;
    
    /* Create space for scrollbar to prevent layout shifts */
    padding-right: 20px;
}

/* Fix for gridster items to ensure consistent sizing */
gridster {
    background: transparent;
}

gridster-item {
    background-color: transparent !important;
    border-radius: 8px;
    overflow: hidden;
}

.item-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Delete button for tiles */
.tile-delete-button {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 100;
    background-color: rgba(244, 67, 54, 0.8);
    color: white;
    border: none;
    border-radius: 0 0 0 8px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.2s ease;
    padding: 0;
}

.tile-delete-button:hover {
    opacity: 1;
    background-color: rgb(244, 67, 54);
}

.tile-delete-button mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

/* Default tile styles */
.tile-default {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.tile-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.tile-controls {
    display: flex;
    gap: 4px;
}

.tile-body {
    flex: 1;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(0, 0, 0, 0.6);
}

/* Empty state placeholder */
.add-tile-placeholder {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    max-width: 90%;
    background-color: var(--background-secondary, #ffffff);
    border-radius: 8px;
    padding: 24px;
    text-align: center;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Floating action button styles */
.floating-add-button {
    position: fixed;
    right: 24px;
    bottom: 24px;
    z-index: 100;
}

/* Auto-save indicator */
.autosave-indicator {
    position: fixed;
    bottom: 16px;
    left: 16px;
    background-color: var(--primary-color);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    animation: fadeIn 0.3s ease;
    z-index: 100;
    box-shadow: 0 2px 8px var(--box-shadow);
}

.autosave-indicator mat-icon {
    margin-right: 8px;
}

/* Snapshots panel (history) */
.snapshots-panel {
    position: fixed;
    right: -250px;
    top: 80px;
    width: 250px;
    max-height: calc(100vh - 100px);
    overflow: auto;
    transition: right 0.3s ease;
    box-shadow: -2px 0 8px var(--box-shadow);
    background-color: var(--background-secondary);
    color: var(--text-color);
    border-radius: 0 0 0 8px;
    padding: 10px;
    
    /* Scrollbar specific styling */
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

.snapshots-panel::before {
    content: "History";
    position: absolute;
    left: -80px;
    top: 30px;
    background-color: var(--background-secondary);
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: var(--text-color);
    font-weight: 500;
    box-shadow: -2px 0 4px var(--box-shadow);
}

.snapshots-hint {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.snapshots-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: calc(100% - 80px);
    overflow-y: auto;
}

.snapshot-item {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.snapshot-time {
    font-weight: 500;
    color: var(--text-color);
}

.snapshot-count {
    color: var(--text-secondary);
    font-size: 12px;
}

/* Responsive styles for mobile */
@media (max-width: 768px) {
    .dashboard-header h1 {
        font-size: 18px;
    }
    
    .snapshots-panel {
        display: none;
    }
    
    .dashboard-grid {
        padding: 8px;
    }
}

.dashboard-actions-bar {
    display: flex;
    justify-content: flex-end;
    padding: 8px 16px;
    margin-bottom: 16px;
}

.dashboard-actions-bar a {
    display: flex;
    align-items: center;
    gap: 8px;
}

.default-tile-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 16px;
    text-align: center;
    
    h3 {
        margin-top: 0;
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 500;
    }
    
    p {
        color: var(--text-secondary);
    }
}
