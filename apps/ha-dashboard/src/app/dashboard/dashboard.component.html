<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>{{ dashboard?.name || 'Dashboard' }}</h1>
        
        <div class="spacer"></div>
        
        <!-- Connection status indicator -->
        <div class="connection-status" [class.connected]="haConnected">
            <span class="status-dot"></span>
            <span class="status-text">{{ haConnected ? 'Connected' : 'Disconnected' }}</span>
        </div>
        
        <div class="dashboard-actions">
            <!-- Back to Dashboard Selector Button -->
            <button mat-icon-button 
                   routerLink="/selector" 
                   matTooltip="Back to Dashboard Selector">
                <mat-icon>dashboard</mat-icon>
            </button>
            
            <!-- Add Tile Button (only visible when editing is enabled) -->
            <button *ngIf="isEditingEnabled" 
                   mat-icon-button 
                   (click)="openAddTileDialog()" 
                   matTooltip="Add Tile">
                <mat-icon>add</mat-icon>
            </button>
            
            <!-- Lock/Unlock Toggle -->
            <button mat-icon-button 
                   (click)="toggleEditMode()" 
                   [matTooltip]="isEditingEnabled ? 'Lock Dashboard (Disable Editing)' : 'Unlock Dashboard (Enable Editing)'">
                <mat-icon>{{ isEditingEnabled ? 'lock_open' : 'lock' }}</mat-icon>
            </button>
            
            <!-- Hamburger Menu -->
            <app-hamburger-menu 
                [config]="menuConfig"
                (toggleAutoSaveEvent)="toggleAutosave($event)"
                (toggleShowGridEvent)="toggleGridDisplay($event)"
                (setAsDefaultEvent)="setAsDefault()"
                (createNewDashboardEvent)="createNewDashboard()"
                (saveLayoutEvent)="saveLayout()"
                (undoLastChangeEvent)="undoLastChange()"
                (configureHomeAssistantEvent)="openHAConfig()"
                (openExplorerEvent)="openHAExplorer()">
            </app-hamburger-menu>
        </div>
    </div>

    <div *ngIf="error" class="error-message">
        <mat-icon>error</mat-icon>
        <span>{{ error }}</span>
    </div>

    <!-- Loading state -->
    <app-loading-spinner *ngIf="isLoading" message="Loading dashboard..."></app-loading-spinner>

    <!-- Empty state when no dashboard is available -->
    <app-empty-state 
        *ngIf="!isLoading && !dashboard"
        title="No Dashboard Available"
        description="There is no dashboard configured yet. Would you like to create one?"
        actionText="Create Dashboard"
        (actionClick)="createNewDashboard()">
    </app-empty-state>

    <!-- Dashboard content -->
    <ng-container *ngIf="!isLoading && dashboard">
        <gridster [options]="options" class="dashboard-grid">
            <!-- Display "Add Tile" placeholder if no items -->
            <div *ngIf="items.length === 0" class="add-tile-placeholder mat-elevation-z3">
                <app-empty-state
                    title="Empty Dashboard"
                    description="Your dashboard doesn't have any tiles yet. Add some tiles to get started."
                    icon="add_circle"
                    actionText="Add Tile"
                    (actionClick)="openAddTileDialog()">
                </app-empty-state>
            </div>
            
            <!-- Regular gridster items -->
            <gridster-item *ngFor="let item of items" [item]="item">
                <div class="item-content">
                    <ng-container [ngSwitch]="getTileType(item)">
                        
                        <!-- Sample Tile (for demonstration) -->
                        <app-sample-tile 
                            *ngSwitchCase="'sample'" 
                            [item]="item"
                            [isEditingEnabled]="isEditingEnabled"
                            [isLocked]="isTileLocked(item)"
                            (lockChange)="onTileLockChange($event)">
                        </app-sample-tile>
                        
                        <!-- Floorplan Tile -->
                        <app-floorplan-tile 
                            *ngSwitchCase="'floorplan'" 
                            [tile]="convertToFloorplanTile(item)"
                            [isEditingEnabled]="isEditingEnabled">
                        </app-floorplan-tile>
                        
                        <!-- Device Status Tile -->
                        <app-device-status-tile 
                            *ngSwitchCase="'device-status'" 
                            [entityId]="getStringFromSettings(item, 'entityId')"
                            [deviceType]="getDeviceType(item)"
                            [title]="item.title"
                            [isEditingEnabled]="isEditingEnabled">
                        </app-device-status-tile>
                        
                        <!-- Entity Browser Tile -->
                        <app-entity-browser-tile 
                            *ngSwitchCase="'entity-browser'" 
                            [title]="item.title"
                            [isEditingEnabled]="isEditingEnabled">
                        </app-entity-browser-tile>
                        
                        <!-- Device Browser Tile -->
                        <app-device-browser-tile 
                            *ngSwitchCase="'device-browser'" 
                            [title]="item.title"
                            [isEditingEnabled]="isEditingEnabled">
                        </app-device-browser-tile>
                        
                        <!-- Default Tile -->
                        <app-base-tile 
                            *ngSwitchDefault
                            [item]="item"
                            [isEditingEnabled]="isEditingEnabled"
                            [isLocked]="isTileLocked(item)"
                            (delete)="removeTile(item)"
                            (lockChange)="onTileLockChange($event)">
                            
                            <div class="default-tile-content">
                                <h3>{{ item.title || 'Unnamed Tile' }}</h3>
                                <p>This is a default tile for type: {{ item.type }}</p>
                            </div>
                        </app-base-tile>
                    </ng-container>
                </div>
            </gridster-item>
        </gridster>
    </ng-container>
</div>
