<!-- Authentication loading state -->
<div *ngIf="isAuthenticating" class="auth-loading mat-elevation-z2">
  <mat-spinner [diameter]="50"></mat-spinner>
  <p>Connecting to Firebase...</p>
</div>

<!-- Authentication error state -->
<mat-card *ngIf="authError && !isAuthenticating" class="auth-error mat-elevation-z4">
  <mat-card-header>
    <mat-icon class="error-icon">error</mat-icon>
    <mat-card-title>Authentication Error</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <p>{{ authError }}</p>
  </mat-card-content>
  <mat-card-actions>
    <button mat-raised-button color="primary" (click)="signInWithTestAccount()">Sign in with TEST ACCOUNT</button>
    <button mat-stroked-button (click)="signInAnonymously()">Try Anonymous Login</button>
  </mat-card-actions>
  <mat-card-footer>
    <p class="hint">Note: For anonymous authentication to work, it must be enabled in the Firebase Console.</p>
  </mat-card-footer>
</mat-card>

<!-- Firestore connection error state -->
<mat-card *ngIf="connectionError && !isAuthenticating && !authError" class="connection-error mat-elevation-z4">
  <mat-card-header>
    <mat-icon class="error-icon">wifi_off</mat-icon>
    <mat-card-title>Connection Error</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <p>{{ connectionError }}</p>
    <div class="status" *ngIf="isConnecting">
      <mat-spinner [diameter]="24" class="small-loader"></mat-spinner>
      <p>Attempting to reconnect...</p>
    </div>
  </mat-card-content>
  <mat-card-actions>
    <button mat-raised-button color="primary" *ngIf="!isConnecting" (click)="tryReconnect()">
      <mat-icon>refresh</mat-icon> Try Again
    </button>
  </mat-card-actions>
  <mat-card-footer>
    <p class="hint">Make sure you have a stable internet connection and that Firebase services are not blocked.</p>
  </mat-card-footer>
</mat-card>

<!-- Navigation Bar -->
<nav *ngIf="user && !authError && !isAuthenticating && (!connectionError || hasFirestoreConnection)" class="app-nav mat-elevation-z2">
  <a routerLink="/" class="app-title">Home Assistant Dashboard</a>
  <div class="nav-links">
    <a routerLink="/" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">Dashboard</a>
    <a routerLink="/devices" routerLinkActive="active">Devices</a>
    <a routerLink="/device-test" routerLinkActive="active">Device Test</a>
  </div>
</nav>

<!-- Main app content when authenticated and connected -->
<router-outlet *ngIf="user && !authError && !isAuthenticating && (!connectionError || hasFirestoreConnection)"></router-outlet>
