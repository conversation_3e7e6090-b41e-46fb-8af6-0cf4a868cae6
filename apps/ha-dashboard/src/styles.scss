/* You can add global styles to this file, and also import other style files */

/* Import pre-built Angular Material themes */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* Additional Material custom styles */
@import 'assets/material-theme.scss';

/* Font Awesome Icons */
@import '@fortawesome/fontawesome-free/css/all.min.css';

/* Define CSS variables for theme colors */
:root {
  /* Light theme (default) */
  --background-color: #f5f5f5;
  --background-secondary: #ffffff;
  --text-color: rgba(0, 0, 0, 0.87); /* Material standard text color */
  --text-secondary: rgba(0, 0, 0, 0.6); /* Material standard secondary text color */
  --border-color: rgba(0, 0, 0, 0.12); /* Material standard divider color */
  --box-shadow: rgba(0, 0, 0, 0.15);
  --primary-color: #3f51b5; /* Material indigo primary */
  --primary-hover: #303f9f; /* Material indigo 700 */
  --secondary-color: #6c757d;
  --secondary-hover: #5a6268;
  --success-color: #4CAF50;
  --error-color: #F44336;
  --menu-bg: #ffffff;
  --menu-text: rgba(0, 0, 0, 0.87);
  --menu-border: rgba(0, 0, 0, 0.12);
  --toggle-bg: #cccccc;
  --toggle-active: #3f51b5;
  --primary-color-rgb: 63, 81, 181;
  
  /* Scrollbar colors */
  --scrollbar-track: rgba(0, 0, 0, 0.05);
  --scrollbar-thumb: rgba(0, 0, 0, 0.2);
  --scrollbar-thumb-hover: rgba(0, 0, 0, 0.3);
}

/* Dark theme */
[data-theme="dark"] {
  --background-color: #121212; /* Material standard dark background */
  --background-secondary: #1e1e1e; /* Material dark surface color */
  --text-color: rgba(255, 255, 255, 0.87); /* Material standard dark text color */
  --text-secondary: rgba(255, 255, 255, 0.6); /* Material standard dark secondary text color */
  --border-color: rgba(255, 255, 255, 0.12); /* Material standard dark divider color */
  --box-shadow: rgba(0, 0, 0, 0.5);
  --primary-color: #7986cb; /* Material indigo 300 (lighter for dark theme) */
  --primary-hover: #9fa8da; /* Material indigo 200 */
  --secondary-color: #909aa2;
  --secondary-hover: #a0abb2;
  --success-color: #66bb6a; /* Material green 400 for dark theme */
  --error-color: #ef5350; /* Material red 400 for dark theme */
  --menu-bg: #1e1e1e;
  --menu-text: rgba(255, 255, 255, 0.87);
  --menu-border: rgba(255, 255, 255, 0.12);
  --toggle-bg: #555555;
  --toggle-active: #7986cb;
  --primary-color-rgb: 121, 134, 203;
  
  /* Dark theme scrollbar colors */
  --scrollbar-track: rgba(255, 255, 255, 0.05);
  --scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
}

/* Apply material theme classes to body */
body {
  &.dark-theme {
    /* Apply material dark theme class */
    .mat-app-background {
      background-color: var(--background-color);
    }
    
    /* Angular Material color overrides for dark mode */
    --mat-primary-default: var(--primary-color);
    --mat-accent-default: var(--secondary-color);
    --mat-warn-default: var(--error-color);
  }
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

/* Clean, modern scrollbar styles */
* {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

/* WebKit/Blink browsers (Chrome, Safari, Edge, etc) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Global icon styles */
.fa {
  vertical-align: middle;
}

/* Button icon styles */
button .fa {
  margin-right: 5px;
}

/* Animation keyframes used by the dashboard */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fa-spin {
  animation: spin 1s linear infinite;
}

/* Global Material component overrides */
.mat-mdc-card {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.mat-mdc-button, 
.mat-mdc-raised-button, 
.mat-mdc-outlined-button, 
.mat-mdc-icon-button {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* Custom button styles to match the existing app */
.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--text-secondary);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.icon-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary-color);
}

[data-theme="dark"] .icon-button:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Form styles */
.form-field {
  width: 100%;
  margin-bottom: 16px;
}

/* Material override for buttons */
.mat-mdc-raised-button.mat-primary {
  background-color: var(--primary-color);
}

.mat-mdc-outlined-button.mat-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Dialog styles */
.mat-mdc-dialog-container .mat-mdc-dialog-surface {
  padding: 24px;
  border-radius: 8px;
  background-color: var(--background-secondary);
  color: var(--text-color);
}

/* Fullscreen dialog styles for dashboard selector */
.fullscreen-dialog {
  max-width: 100vw !important;
  max-height: 100vh !important;
  width: 100vw;
  height: 100vh;
  
  .mat-mdc-dialog-container {
    border-radius: 0;
    padding: 0;
  }
  
  .mat-mdc-dialog-surface {
    padding: 0 !important;
    border-radius: 0 !important;
    background-color: transparent !important;
  }
}

/* Snackbar styles */
.mat-mdc-snack-bar-container {
  margin: 16px;
}

/* Dark Mode Material Component Overrides */
[data-theme="dark"] {
  /* Card overrides */
  .mat-mdc-card {
    background-color: var(--background-secondary);
    color: var(--text-color);
  }
  
  /* Input overrides */
  .mat-mdc-input-element {
    color: var(--text-color);
  }
  
  /* Form field overrides */
  .mat-mdc-form-field-label {
    color: var(--text-secondary);
  }
  
  .mat-mdc-form-field-hint {
    color: var(--text-secondary);
  }
  
  .mat-mdc-form-field-error {
    color: var(--error-color);
  }
  
  /* Dialog overrides */
  .mat-mdc-dialog-container {
    --mdc-dialog-container-color: var(--background-secondary);
    --mdc-dialog-with-divider-divider-color: var(--border-color);
  }
  
  /* Button overrides */
  .mat-mdc-button, .mat-mdc-outlined-button {
    color: var(--text-color);
  }

  /* Checkbox, radio button, and slide toggle colors */
  .mat-mdc-checkbox .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  .mat-mdc-radio-button .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__outer-circle {
    border-color: var(--primary-color);
  }
  
  .mat-mdc-slide-toggle.mat-checked .mdc-switch__track {
    background-color: var(--primary-color);
  }
  
  /* Tables */
  .mat-mdc-table {
    background-color: var(--background-secondary);
  }
  
  .mat-mdc-header-cell, .mat-mdc-cell {
    color: var(--text-color);
  }
  
  /* Tabs */
  .mat-mdc-tab-header {
    background-color: var(--background-color);
  }
  
  .mat-mdc-tab-label-content {
    color: var(--text-secondary);
  }
  
  .mat-mdc-tab.mdc-tab--active .mat-mdc-tab-label-content {
    color: var(--primary-color);
  }
  
  /* Progress spinners/bars */
  .mat-mdc-progress-spinner.mat-primary .mdc-circular-progress__determinate-circle,
  .mat-mdc-progress-spinner.mat-primary .mdc-circular-progress__indeterminate-circle-graphic {
    stroke: var(--primary-color);
  }
  
  .mat-mdc-progress-bar.mat-primary .mdc-linear-progress__bar-inner {
    border-color: var(--primary-color);
  }
}

.dashboard-actions-bar {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px;
  margin-bottom: 16px;
}

.dashboard-actions-bar a {
  display: flex;
  align-items: center;
  gap: 8px;
}

.default-tile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 16px;
  text-align: center;
  
  h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;
  }
  
  p {
    color: var(--text-secondary);
  }
}