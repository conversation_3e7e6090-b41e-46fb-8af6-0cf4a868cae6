# Dashboard Assets

## Floorplan Images

The dashboard supports both SVG and raster image formats (PNG, JPG) for floorplans. 

### SVG Floorplans (Recommended)
For the best quality and scalability, use SVG floorplans. Place your SVG file at:
```
apps/ha-dashboard/src/public/images/floorplan.svg
```

These files will be served from the root URL:
```
/images/floorplan.svg
```

SVG floorplans offer several advantages:
- Vector-based, so they scale perfectly to any size
- Smaller file size for complex floorplans
- Can be styled with CSS
- Better for interactive elements

### Raster Floorplans (PNG/JPG)
For compatibility, the dashboard also supports PNG and JPG formats. Place your image at:
```
apps/ha-dashboard/src/public/images/floorplan.png
```

> Note: The application previously used the assets folder for floorplan images. For better public access, we now use the public folder. For backward compatibility, the app will check both locations.

## Customizing the Floorplan Tile

The floorplan tile is configured to take up:
- Full height of the dashboard
- Half the width of the dashboard

You can modify these dimensions in the `dashboard.component.ts` file by changing the `cols` and `rows` properties of the floorplan tile.

## Adding Interactive Markers

The floorplan tile supports interactive markers that can be placed on your floorplan. These markers can be configured in the `dashboard.component.ts` file or through Firebase.

Each marker has:
- Coordinates (x, y) as percentages of the floorplan dimensions
- A type (light, sensor, device, door, window, custom)
- Optional icon, label, and action properties

Example marker configuration:
```typescript
{
  id: 'light1',
  x: 25,  // 25% from the left edge
  y: 30,  // 30% from the top edge
  type: 'light',
  label: 'Living Room Light',
  icon: 'fa fa-lightbulb',
  actions: [
    {
      id: 'toggle',
      name: 'Toggle Light',
      type: 'toggle'
    }
  ]
}
```