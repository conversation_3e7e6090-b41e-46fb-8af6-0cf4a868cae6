/* Custom styling for Material Design components */

/* Card styles */
.mat-mdc-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--box-shadow) !important;
  background-color: var(--background-secondary) !important;
  color: var(--text-color) !important;
  overflow: hidden;
}

.mat-mdc-card-header {
  background-color: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 16px !important;
}

.mat-mdc-card-title {
  color: var(--text-color) !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  margin-bottom: 0 !important;
}

.mat-mdc-card-content {
  padding: 16px !important;
  color: var(--text-color) !important;
}

/* Button styles */
.mat-mdc-button, 
.mat-mdc-raised-button, 
.mat-mdc-outlined-button, 
.mat-mdc-unelevated-button {
  border-radius: 4px !important;
  text-transform: none !important;
  font-weight: 500 !important;
}

.mat-mdc-raised-button.mat-primary {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.mat-mdc-outlined-button.mat-primary {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* Icon buttons */
.mat-mdc-icon-button {
  color: var(--text-color) !important;
}

/* Typography */
.mat-typography h1, 
.mat-typography h2, 
.mat-typography h3, 
.mat-typography h4, 
.mat-typography h5, 
.mat-typography h6 {
  color: var(--text-color) !important;
}

.mat-typography p,
.mat-typography span,
.mat-typography div {
  color: var(--text-color) !important;
}

/* Expansion panel */
.mat-expansion-panel {
  background-color: var(--background-secondary) !important;
  color: var(--text-color) !important;
}

.mat-expansion-panel-header-title,
.mat-expansion-panel-header-description {
  color: var(--text-color) !important;
}

/* Form fields */
.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-form-field-label {
  color: var(--text-secondary) !important;
}

.mat-mdc-text-field-wrapper {
  background-color: var(--background-secondary) !important;
}

.mat-mdc-form-field-input-control {
  color: var(--text-color) !important;
}

/* Input field placeholder styling */
::placeholder {
  color: var(--text-secondary) !important;
  opacity: 0.7 !important;
}

::-webkit-input-placeholder {
  color: var(--text-secondary) !important;
  opacity: 0.7 !important;
}

::-moz-placeholder {
  color: var(--text-secondary) !important;
  opacity: 0.7 !important;
}

:-ms-input-placeholder {
  color: var(--text-secondary) !important;
  opacity: 0.7 !important;
}

/* Material form field placeholder styles */
.mat-mdc-form-field .mat-mdc-input-element::placeholder,
.mat-mdc-form-field .mat-mdc-textarea-element::placeholder {
  color: var(--text-secondary) !important;
  opacity: 0.7 !important;
}

/* Checkbox */
.mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.mat-mdc-checkbox .mdc-form-field label {
  color: var(--text-color) !important;
}

/* Tabs */
.mat-mdc-tab-group {
  background-color: var(--background-secondary) !important;
}

.mat-mdc-tab-header {
  background-color: var(--background-color) !important;
}

.mat-mdc-tab {
  color: var(--text-secondary) !important;
}

.mat-mdc-tab.mdc-tab--active {
  color: var(--primary-color) !important;
}

/* Dialog */
.mat-mdc-dialog-container {
  --mdc-dialog-container-color: var(--background-secondary) !important;
}

.mat-mdc-dialog-surface {
  color: var(--text-color) !important;
}

.mat-mdc-dialog-title {
  color: var(--text-color) !important;
}

.mat-mdc-dialog-content {
  color: var(--text-color) !important;
}

/* List components */
.mat-mdc-list-item {
  color: var(--text-color) !important;
}

.mat-mdc-list-item-title {
  color: var(--text-color) !important;
}

.mat-mdc-list-item-line {
  color: var(--text-secondary) !important;
}

/* Snackbar */
.mat-mdc-snack-bar-container {
  &.app-notification-success {
    --mdc-snackbar-container-color: var(--success-color);
    --mat-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }

  &.app-notification-error {
    --mdc-snackbar-container-color: var(--error-color);
    --mat-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }

  &.app-notification-info {
    --mdc-snackbar-container-color: var(--primary-color);
    --mat-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }
}

/* Toolbar */
.mat-toolbar {
  background-color: var(--background-secondary) !important;
  color: var(--text-color) !important;
  box-shadow: 0 1px 3px var(--box-shadow);
}

.mat-toolbar.mat-primary {
  background-color: var(--background-secondary) !important;
  color: var(--text-color) !important;
}

/* Progress spinner/bar */
.mat-progress-spinner.mat-primary .mat-progress-spinner-fill {
  stroke: var(--primary-color) !important;
}

.mat-progress-bar.mat-primary .mat-progress-bar-fill {
  background-color: var(--primary-color) !important;
}

/* Icons */
.mat-icon {
  color: inherit;
}

/* Menu */
.mat-mdc-menu-panel {
  background-color: var(--background-secondary) !important;
}

.mat-mdc-menu-item {
  color: var(--text-color) !important;
}

.mat-mdc-menu-item .mat-icon {
  color: var(--text-color) !important;
}

.mat-mdc-menu-item:hover:not([disabled]) {
  background-color: rgba(var(--primary-color-rgb), 0.1) !important;
}

/* Divider */
.mat-divider {
  border-top-color: var(--border-color) !important;
}

/* Tooltip */
.mat-tooltip {
  background-color: rgba(var(--background-secondary), 0.9) !important;
  color: var(--text-color) !important;
}

/* Typography improvements */
body, .mat-typography {
  font-family: Roboto, "Helvetica Neue", sans-serif !important;
  line-height: 1.5;
}

h1, h2, h3, h4, h5, h6, .mat-mdc-card-title, .mat-toolbar {
  font-family: Roboto, "Helvetica Neue", sans-serif !important;
  letter-spacing: normal !important;
}

/* Table */
.mat-mdc-table {
  background-color: var(--background-secondary) !important;
}

.mat-mdc-header-cell {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
}

.mat-mdc-cell {
  color: var(--text-color) !important;
}

.mat-mdc-row:hover {
  background-color: rgba(var(--primary-color-rgb), 0.04) !important;
}

/* Selection list */
.mat-mdc-list-option .mdc-list-item__primary-text {
  color: var(--text-color) !important;
}

.mat-mdc-list-option .mdc-list-item__secondary-text {
  color: var(--text-secondary) !important;
}

/* Dark theme specific adjustments */
[data-theme="dark"] {
  /* Adjust elevation overlay for dark theme */
  --mdc-elevated-card-container-color: var(--background-secondary);
  
  /* Input fields */
  .mat-mdc-form-field-focus-overlay {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  /* Adjust hover states for dark mode */
  .mat-mdc-button:hover:not([disabled]),
  .mat-mdc-outlined-button:hover:not([disabled]) {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  /* Chip adjustments */
  .mat-mdc-chip {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--text-color) !important;
  }
  
  /* Placeholder text specific adjustments for dark mode */
  ::placeholder,
  ::-webkit-input-placeholder,
  ::-moz-placeholder,
  :-ms-input-placeholder,
  .mat-mdc-form-field .mat-mdc-input-element::placeholder,
  .mat-mdc-form-field .mat-mdc-textarea-element::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
  }
  
  /* Material form field hints */
  .mat-mdc-form-field-hint {
    color: rgba(255, 255, 255, 0.5) !important;
  }
} 