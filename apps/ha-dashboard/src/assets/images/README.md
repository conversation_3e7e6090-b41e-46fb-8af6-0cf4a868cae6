# Floorplan Images Guidelines

## Using SVG Floorplans

The dashboard supports SVG files for floorplans, which offer several advantages over raster images:

1. **Vector-Based**: SVGs scale perfectly to any size without losing quality
2. **Smaller File Size**: For detailed floorplans, SVGs are often smaller than equivalent PNG files
3. **Styleable**: You can modify colors and styles with CSS
4. **Interactive**: Elements within SVGs can be individually targeted and made interactive

### Getting Started with SVG Floorplans

1. Place your SVG file at:
   ```
   apps/ha-dashboard/src/assets/images/floorplan.svg
   ```

2. If you need a starter template, you can use the `sample-floorplan.svg` file included in this directory.

### Creating Your Own SVG Floorplan

You can create SVG floorplans using:

- Adobe Illustrator
- Inkscape (free, open-source)
- Figma
- DrawIO
- SVG-Edit (browser-based)

### Best Practices for SVG Floorplans

- Keep your SVG files simple - remove unnecessary metadata and elements
- Use a consistent structure for rooms, doors, and windows
- Add ids to elements you may want to make interactive later
- Use clearly defined layers for different types of elements
- Test your SVG in the dashboard to ensure it's being rendered correctly

### Adding Interactive Markers

The dashboard supports adding interactive markers to your floorplan:

```typescript
// Example marker configuration
{
  id: 'light1',
  x: 25,  // 25% from the left edge of the floorplan
  y: 30,  // 30% from the top edge of the floorplan
  type: 'light',
  label: 'Living Room Light',
  icon: 'fa fa-lightbulb',
  actions: [
    {
      id: 'toggle',
      name: 'Toggle Light',
      type: 'toggle'
    }
  ]
}
```

Marker Types:
- light
- sensor
- door
- window
- switch
- custom

### Troubleshooting SVG Issues

If your SVG doesn't display correctly:

1. Verify the file path is correct
2. Check for any CORS issues if loading from external sources
3. Validate your SVG using an online validator
4. Simplify complex SVGs that might be causing performance issues
5. Make sure your SVG doesn't contain script elements that might be blocked 