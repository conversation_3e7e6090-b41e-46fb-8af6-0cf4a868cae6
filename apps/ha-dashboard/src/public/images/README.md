# Floorplan Images (Public Folder)

This directory contains the floorplan SVG and image files used by the dashboard application.

## File Naming

The default floorplan filenames expected by the application are:
- `floorplan.svg` (recommended)
- `floorplan.png` (alternative)

You can use different filenames, but you'll need to update the dashboard configuration to point to your custom filenames.

## Using SVG Files

SVG files are the recommended format for floorplans as they:
- Scale perfectly to any size
- Can be styled with CSS
- Support interactive elements
- Typically have smaller file sizes for complex layouts

### SVG Requirements

For the best experience with SVG floorplans:

1. Make sure your SVG has proper room/area elements with IDs that start with `room-` 
   (e.g., `room-livingroom`, `room-kitchen`)

2. Use clean, optimized SVG files without unnecessary metadata

3. Test your SVG in the dashboard to ensure it renders correctly

## Troubleshooting

If your floorplan isn't displaying correctly:

1. Check the browser console for errors
2. Verify the file exists at the correct path
3. Ensure the file has the correct permissions
4. Try the reload button in the floorplan tile UI

## Public vs. Assets Directory

The dashboard will look for floorplan images in these locations:
1. First in the public directory: `/images/floorplan.svg`
2. If not found, falls back to the assets directory: `/assets/images/floorplan.svg`

For best results, always place your floorplan images in this public directory. 