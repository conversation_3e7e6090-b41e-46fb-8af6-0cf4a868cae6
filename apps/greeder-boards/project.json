{"name": "greeder-boards", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "greed", "sourceRoot": "apps/greeder-boards/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/greeder-boards", "index": "apps/greeder-boards/src/index.html", "browser": "apps/greeder-boards/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/greeder-boards/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/greeder-boards/public"}], "styles": ["apps/greeder-boards/src/styles.scss", "node_modules/@angular/material/prebuilt-themes/indigo-pink.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "greeder-boards:build:production"}, "development": {"buildTarget": "greeder-boards:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "greeder-boards:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/greeder-boards/jest.config.ts"}}}}