.people-container {
  padding: 20px;

  h2 {
    margin-bottom: 24px;
    font-size: 24px;
    font-weight: 500;
  }
}

.people-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.person-card {
  mat-card-header {
    margin-bottom: 16px;

    img {
      width: 48px;
      height: 48px;
      object-fit: cover;
    }
  }

  .connections {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.6);

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }

  .interests {
    margin-bottom: 16px;

    mat-chip-set {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  mat-card-actions {
    padding: 16px;
    display: flex;
    gap: 8px;

    button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
  }
} 