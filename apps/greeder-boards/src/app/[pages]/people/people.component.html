<div class="people-container">
  <h2>People</h2>
  <div class="people-grid">
    <mat-card class="person-card" *ngFor="let person of people">
      <mat-card-header>
        <img mat-card-avatar [src]="person.imageUrl" [alt]="person.name">
        <mat-card-title>{{person.name}}</mat-card-title>
        <mat-card-subtitle>{{person.role}}</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <div class="connections">
          <mat-icon>people</mat-icon>
          <span>{{person.connections}} connections</span>
        </div>
        
        <div class="interests">
          <mat-chip-set>
            <mat-chip *ngFor="let interest of person.interests">
              {{interest}}
            </mat-chip>
          </mat-chip-set>
        </div>
      </mat-card-content>
      
      <mat-card-actions>
        <button mat-button color="primary">
          <mat-icon>person_add</mat-icon>
          Connect
        </button>
        <button mat-button>
          <mat-icon>message</mat-icon>
          Message
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div> 