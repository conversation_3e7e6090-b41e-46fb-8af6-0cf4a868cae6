.categories-container {
  padding: 20px;

  h2 {
    margin-bottom: 24px;
    font-size: 24px;
    font-weight: 500;
  }
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.category-card {
  position: relative;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-4px);

    .category-overlay {
      background-color: rgba(0, 0, 0, 0.5);
    }
  }
}

.category-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  transition: background-color 0.3s ease;
}

.category-name {
  color: white;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.event-count {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
} 