<div class="app-container">
  <mat-toolbar color="primary">
    <button mat-icon-button (click)="sidenav.toggle()">
      <mat-icon>menu</mat-icon>
    </button>
    <span>{{getCurrentPageTitle()}}</span>
  </mat-toolbar>

  <mat-sidenav-container>
    <mat-sidenav #sidenav mode="over" [opened]="false" [position]="'end'">
      <mat-nav-list>
        <a mat-list-item 
           *ngFor="let item of navItems" 
           [routerLink]="item.path" 
           routerLinkActive="active">
          <mat-icon matListItemIcon>{{item.icon}}</mat-icon>
          <span matListItemTitle>{{item.title}}</span>
        </a>
      </mat-nav-list>
    </mat-sidenav>

    <mat-sidenav-content>
      <div class="content">
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>
