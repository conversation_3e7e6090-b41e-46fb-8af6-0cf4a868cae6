.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

mat-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
}

mat-sidenav-container {
  flex: 1;
}

mat-sidenav {
  width: 250px;
  background-color: #fafafa;
}

.content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.active {
  background-color: rgba(0, 0, 0, 0.04);
}

mat-nav-list {
  padding-top: 0;
}

a[mat-list-item] {
  margin: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}
