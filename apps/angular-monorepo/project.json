{"name": "angular-monorepo", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/angular-monorepo/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/angular-monorepo", "index": "apps/angular-monorepo/src/index.html", "browser": "apps/angular-monorepo/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/angular-monorepo/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/angular-monorepo/public"}], "styles": ["apps/angular-monorepo/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "5kb", "maximumError": "10kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "angular-monorepo:build:production"}, "development": {"buildTarget": "angular-monorepo:build:development"}, "local-network": {"buildTarget": "angular-monorepo:build:development", "options": {"host": "0.0.0.0", "port": 4200, "publicHost": "127.0.0.1:4200"}}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "angular-monorepo:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/angular-monorepo/jest.config.ts"}}}}