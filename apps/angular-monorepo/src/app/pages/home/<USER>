import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { FormsModule } from '@angular/forms';

interface ComponentCategory {
  name: string;
  route: string;
  icon: string;
  description: string;
  componentCount: number;
  implemented: boolean;
  tags?: string[];
}

@Component({
  selector: 'app-showcase-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatChipsModule,
    FormsModule
  ],
  templateUrl: './showcase-home.component.html',
  styleUrls: ['./showcase-home.component.scss']
})
export class ShowcaseHomeComponent {
  searchTerm = '';
  
  categories: ComponentCategory[] = [
    {
      name: 'Buttons',
      route: 'buttons',
      icon: 'smart_button',
      description: 'Interactive button components including icons, toggles, and more',
      componentCount: 5,
      implemented: true,
      tags: ['interactive', 'forms']
    },
    {
      name: 'Cards',
      route: 'cards',
      icon: 'dashboard',
      description: 'Flexible container components for grouping related content',
      componentCount: 4,
      implemented: true,
      tags: ['containers', 'layout']
    },
    {
      name: 'Containers',
      route: 'containers',
      icon: 'view_module',
      description: 'Layout containers for organizing page structure',
      componentCount: 6,
      implemented: true,
      tags: ['layout', 'structure']
    },
    {
      name: 'Timeline',
      route: 'timeline',
      icon: 'timeline',
      description: 'Components for displaying chronological events',
      componentCount: 2,
      implemented: true,
      tags: ['data-display', 'visualization']
    },
    {
      name: 'Wizards',
      route: 'wizards',
      icon: 'assistant',
      description: 'Multi-step form components with progress tracking',
      componentCount: 3,
      implemented: true,
      tags: ['forms', 'navigation']
    },
    {
      name: 'Inputs',
      route: 'inputs',
      icon: 'input',
      description: 'Form input components with validation and styling',
      componentCount: 10,
      implemented: false,
      tags: ['forms', 'validation']
    },
    {
      name: 'Tables',
      route: 'tables',
      icon: 'table_chart',
      description: 'Data table components with sorting and filtering',
      componentCount: 2,
      implemented: false,
      tags: ['data-display', 'lists']
    },
    {
      name: 'Lists',
      route: 'lists',
      icon: 'list',
      description: 'List components for displaying collections of data',
      componentCount: 6,
      implemented: false,
      tags: ['data-display', 'layout']
    },
    {
      name: 'Modals',
      route: 'modals',
      icon: 'open_in_new',
      description: 'Modal and popup components for overlays',
      componentCount: 1,
      implemented: false,
      tags: ['overlays', 'interactive']
    },
    {
      name: 'Dialogs',
      route: 'dialogs',
      icon: 'chat_bubble',
      description: 'Dialog components for user interactions',
      componentCount: 1,
      implemented: false,
      tags: ['overlays', 'interactive']
    },
    {
      name: 'Chips',
      route: 'chips',
      icon: 'label',
      description: 'Compact elements for tags and selections',
      componentCount: 0,
      implemented: false,
      tags: ['forms', 'data-display']
    },
    {
      name: 'Layouts',
      route: 'layouts',
      icon: 'dashboard_customize',
      description: 'Page layout components and templates',
      componentCount: 2,
      implemented: false,
      tags: ['layout', 'structure']
    },
    {
      name: 'Typography',
      route: 'typography',
      icon: 'text_fields',
      description: 'Text styling and typography components',
      componentCount: 0,
      implemented: false,
      tags: ['styling', 'text']
    },
    {
      name: 'Themes',
      route: 'themes',
      icon: 'palette',
      description: 'Theme customization and color schemes',
      componentCount: 1,
      implemented: false,
      tags: ['styling', 'customization']
    },
    {
      name: 'Notifications',
      route: 'notifications',
      icon: 'notifications',
      description: 'Alert and notification components',
      componentCount: 0,
      implemented: false,
      tags: ['feedback', 'interactive']
    },
    {
      name: 'Placeholders',
      route: 'placeholders',
      icon: 'image',
      description: 'Loading states and placeholder components',
      componentCount: 3,
      implemented: false,
      tags: ['loading', 'states']
    },
    {
      name: 'Motion',
      route: 'motion',
      icon: 'animation',
      description: 'Animation and transition components',
      componentCount: 0,
      implemented: false,
      tags: ['animation', 'interactive']
    },
    {
      name: 'Accessibility',
      route: 'a11y',
      icon: 'accessibility',
      description: 'Accessibility-focused components and utilities',
      componentCount: 0,
      implemented: false,
      tags: ['a11y', 'utilities']
    },
    {
      name: 'Internationalization',
      route: 'i18n',
      icon: 'language',
      description: 'Components for multi-language support',
      componentCount: 0,
      implemented: false,
      tags: ['i18n', 'utilities']
    }
  ];

  filteredCategories = [...this.categories];

  get totalComponents(): number {
    return this.categories.reduce((sum, cat) => sum + cat.componentCount, 0);
  }

  get implementedCategories(): number {
    return this.categories.filter(cat => cat.implemented).length;
  }

  get progressPercentage(): number {
    return Math.round((this.implementedCategories / this.categories.length) * 100);
  }

  filterCategories(): void {
    const term = this.searchTerm.toLowerCase();
    this.filteredCategories = this.categories.filter(category => 
      category.name.toLowerCase().includes(term) ||
      category.description.toLowerCase().includes(term) ||
      category.tags?.some(tag => tag.toLowerCase().includes(term))
    );
  }
}
