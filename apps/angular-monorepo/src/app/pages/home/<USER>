<div class="showcase-container">
  <!-- Hero Section -->
  <div class="hero-section">
    <div class="hero-content">
      <h1 class="hero-title">RJ UI Component Library</h1>
      <p class="hero-subtitle">A comprehensive collection of reusable Angular components for building modern web applications</p>
      
      <!-- Search Bar -->
      <div class="search-container">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search components</mat-label>
          <input matInput [(ngModel)]="searchTerm" (input)="filterCategories()" placeholder="Try 'buttons', 'forms', or 'layout'...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </div>

      <!-- Quick Stats -->
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-value">{{totalComponents}}</span>
          <span class="stat-label">Components</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{categories.length}}</span>
          <span class="stat-label">Categories</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{progressPercentage}}%</span>
          <span class="stat-label">Complete</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Categories Grid -->
  <div class="categories-section">
    <h2 class="section-title">Component Categories</h2>
    
    <div class="categories-grid">
      <mat-card 
        *ngFor="let category of filteredCategories" 
        class="category-card"
        [class.implemented]="category.implemented"
        [routerLink]="['/', category.route]"
        [class.disabled]="!category.implemented">
        
        <mat-card-header>
          <mat-icon mat-card-avatar class="category-icon">{{category.icon}}</mat-icon>
          <mat-card-title>{{category.name}}</mat-card-title>
          <mat-card-subtitle>
            <span class="component-count">{{category.componentCount}} components</span>
          </mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <p class="category-description">{{category.description}}</p>
          
          <div class="tags" *ngIf="category.tags && category.tags.length > 0">
            <mat-chip-listbox>
              <mat-chip *ngFor="let tag of category.tags" [disabled]="true">
                {{tag}}
              </mat-chip>
            </mat-chip-listbox>
          </div>
        </mat-card-content>
        
        <mat-card-actions>
          <button mat-button color="primary" [disabled]="!category.implemented">
            <mat-icon>{{category.implemented ? 'arrow_forward' : 'construction'}}</mat-icon>
            {{category.implemented ? 'View Components' : 'Coming Soon'}}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="filteredCategories.length === 0">
      <mat-icon>search_off</mat-icon>
      <h3>No components found</h3>
      <p>Try adjusting your search terms</p>
      <button mat-button color="primary" (click)="searchTerm = ''; filterCategories()">
        Clear Search
      </button>
    </div>
  </div>

  <!-- Getting Started Section -->
  <div class="getting-started-section">
    <h2 class="section-title">Getting Started</h2>
    <div class="info-cards">
      <mat-card class="info-card">
        <mat-card-header>
          <mat-icon mat-card-avatar>code</mat-icon>
          <mat-card-title>Installation</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Install the RJ UI Components library via npm:</p>
          <code class="code-block">npm install &#64;rjui/components</code>
        </mat-card-content>
      </mat-card>

      <mat-card class="info-card">
        <mat-card-header>
          <mat-icon mat-card-avatar>integration_instructions</mat-icon>
          <mat-card-title>Import Components</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Import components in your Angular modules:</p>
          <code class="code-block">import {{'{'}} ButtonsComponent {{'}'}} from '&#64;rjui';</code>
        </mat-card-content>
      </mat-card>

      <mat-card class="info-card">
        <mat-card-header>
          <mat-icon mat-card-avatar>brush</mat-icon>
          <mat-card-title>Theming</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Customize components with built-in theme support</p>
          <code class="code-block">&#64;import '&#64;rjui/themes/basic';</code>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
