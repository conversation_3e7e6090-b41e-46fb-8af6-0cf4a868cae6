@import '../../../../../../libs/rj-ui-components/src/lib/themes/_basic-theme.scss';

.showcase-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// Hero Section
.hero-section {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .hero-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 16px;
    letter-spacing: -0.5px;
  }

  .hero-subtitle {
    font-size: 1.25rem;
    font-weight: 300;
    opacity: 0.9;
    margin-bottom: 40px;
    line-height: 1.6;
  }
}

// Search Container
.search-container {
  max-width: 500px;
  margin: 0 auto 40px;

  .search-field {
    width: 100%;

    ::ng-deep {
      .mat-mdc-form-field-wrapper {
        background-color: white;
        border-radius: 28px;
      }

      .mat-mdc-text-field-wrapper {
        border-radius: 28px !important;
      }

      .mat-mdc-form-field-focus-overlay {
        border-radius: 28px;
      }

      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: rgba(255, 255, 255, 0.5) !important;
      }

      .mat-mdc-form-field-subscript-wrapper {
        display: none;
      }
    }
  }
}

// Stats Row
.stats-row {
  display: flex;
  justify-content: center;
  gap: 60px;
  flex-wrap: wrap;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .stat-value {
      font-size: 2.5rem;
      font-weight: 700;
      line-height: 1;
    }

    .stat-label {
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 1px;
      opacity: 0.8;
      margin-top: 8px;
    }
  }
}

// Categories Section
.categories-section {
  padding: 60px 20px;
  max-width: 1200px;
  margin: 0 auto;

  .section-title {
    text-align: center;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 40px;
    color: #333;
  }
}

// Categories Grid
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 60px;
}

// Category Card
.category-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover:not(.disabled) {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    border-color: #1976d2;
  }

  &.disabled {
    opacity: 0.7;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  .category-icon {
    background-color: #1976d2;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    font-size: 28px;
  }

  &.implemented .category-icon {
    background-color: #4caf50;
  }

  mat-card-header {
    margin-bottom: 16px;
  }

  mat-card-title {
    font-size: 1.25rem;
    font-weight: 600;
  }

  .component-count {
    color: #666;
    font-size: 0.875rem;
  }

  mat-card-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .category-description {
    color: #666;
    line-height: 1.5;
    margin-bottom: 16px;
  }

  .tags {
    margin-top: auto;

    mat-chip {
      font-size: 0.75rem;
      height: 24px;
      padding: 0 8px;
    }
  }

  mat-card-actions {
    padding: 16px;
    margin: 0;
    border-top: 1px solid #e0e0e0;

    button {
      width: 100%;
      
      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;

  mat-icon {
    font-size: 72px;
    width: 72px;
    height: 72px;
    color: #ccc;
    margin-bottom: 24px;
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 8px;
  }

  p {
    margin-bottom: 24px;
  }
}

// Getting Started Section
.getting-started-section {
  background-color: white;
  padding: 60px 20px;
  border-top: 1px solid #e0e0e0;

  .section-title {
    text-align: center;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 40px;
    color: #333;
  }

  .info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    max-width: 1000px;
    margin: 0 auto;
  }

  .info-card {
    border: 1px solid #e0e0e0;

    mat-card-header {
      margin-bottom: 16px;

      mat-icon {
        background-color: #ff4081;
        color: white;
        width: 40px;
        height: 40px;
        font-size: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .code-block {
      display: block;
      background-color: #f5f5f5;
      padding: 12px 16px;
      border-radius: 4px;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 0.875rem;
      margin-top: 12px;
      color: #333;
      border: 1px solid #e0e0e0;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .hero-section {
    padding: 40px 20px;

    .hero-title {
      font-size: 2rem;
    }

    .hero-subtitle {
      font-size: 1rem;
    }
  }

  .stats-row {
    gap: 30px;

    .stat-value {
      font-size: 2rem;
    }
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .info-cards {
    grid-template-columns: 1fr;
  }
}
