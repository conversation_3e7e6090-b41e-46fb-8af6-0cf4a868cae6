@import '../../../../libs/rj-ui-components/src/lib/themes/_basic-theme.scss';

.header{
    display:flex;
    @include header($z-index:2,$font-size:get-theme(font-size-large), $font-color:get-theme(primary-color));
    background-color:get-theme(primary-color);
    justify-content:flex-start;
    font-family:theme(font-family);

    .title{
        display:flex;
        justify-self: flex-start;
        flex: 0 0 auto;
        color:white;
    }
    
    .layout-buttons{
        display:flex;
        flex: 1 1 50%;
        justify-self:flex-start;
        align-self:center;
    }

    .home-assistant {
        display: flex;
        flex-direction: column;
        margin-left: auto;
        margin-right: 20px;
        color: white;
        
        .ha-status {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            
            .ha-title {
                font-weight: bold;
                margin-right: 15px;
            }
            
            .connection-status {
                display: flex;
                align-items: center;
                margin-right: 15px;
                color: #ff6b6b;
                
                &.connected {
                    color: #4ecdc4;
                }
                
                i {
                    margin-right: 5px;
                }
            }
            
            .entity-stats {
                font-size: 0.9em;
                color: #ddd;
            }
        }
        
        .ha-inputs {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            
            input {
                margin-right: 10px;
                padding: 5px 10px;
                border-radius: 4px;
                border: 1px solid #ccc;
                background-color: rgba(255, 255, 255, 0.9);
                
                &:focus {
                    outline: none;
                    border-color: #4ecdc4;
                }
                
                &:disabled {
                    background-color: rgba(255, 255, 255, 0.6);
                }
            }
            
            button {
                background-color: #3d5a80;
                color: white;
                
                &:hover {
                    background-color: #2c3e50;
                }
            }
        }
        
        .dashboard-selector {
            display: flex;
            align-items: center;
            
            .dashboard-dropdown {
                flex: 1;
                margin-right: 10px;
                
                button {
                    width: 100%;
                    text-align: left;
                    background-color: rgba(255, 255, 255, 0.15);
                    color: white;
                    justify-content: space-between;
                    padding: 4px 12px;
                    
                    span {
                        margin-right: 10px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    
                    i {
                        font-size: 0.8em;
                    }
                    
                    &:hover {
                        background-color: rgba(255, 255, 255, 0.25);
                    }
                }
            }
            
            .view-dashboard-btn {
                background-color: #5bc0de;
                color: white;
                padding: 4px 12px;
                
                &:hover {
                    background-color: #31b0d5;
                }
                
                i {
                    margin-right: 5px;
                }
            }
        }
    }
}

.jump-to-bar{
    display:flex;
    justify-content:flex-start;
    background-color:get-theme(secondary-color);
    padding:theme(padding);
}


.test-modal{
    display:flex;
    background-color:White;
    border: 1px solid black;
    border-radius:7px;
    height:200px;
    width:200px;
    align-items:center;
    justify-content:center;

}

.overlay{
    display:flex;
    flex: 1 1 100%;
    padding:3rem;
    background-color:lightgray;
    align-self:stretch;
    justify-self: stretch;
}

// Additional styles for mat-menu items
::ng-deep {
  .mat-menu-panel {
    .no-dashboards-message {
      padding: 16px;
      text-align: center;
      color: #666;
      font-style: italic;
    }

    .mat-menu-item {
      &.active {
        background-color: rgba(0, 0, 0, 0.04);
      }

      i.fa {
        margin-right: 8px;
        width: 16px;
      }
    }
  }
}