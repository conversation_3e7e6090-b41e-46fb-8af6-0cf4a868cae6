// @tailwind base;
// @tailwind components;
// @tailwind utilities;
// @import 'tailwindcss/base';
// @import 'tailwindcss/components';
// @import 'tailwindcss/utilities';

/* You can add global styles to this file, and also import other style files */
@import '../../../libs/rj-ui-components/src/lib/styles/_mixins.scss';
@import "@angular/material/prebuilt-themes/indigo-pink.css";
@import "../../../libs/rj-ui-components/src/lib/themes/_basic-theme.scss";

html, body {
    height: 100vh;
    width: 100%;
    margin: 0;
    padding: 0;
}

// Add dark mode support to dialogs - particularly targeting the chart dialog
.chart-dialog-container {
  .mat-mdc-dialog-surface {
    color: var(--mat-dialog-text-color, rgba(0, 0, 0, 0.87)) !important;
    background-color: var(--mat-dialog-background-color, white) !important;
  }
  
  .mat-mdc-dialog-actions {
    background-color: var(--mat-dialog-background-color, white) !important;
  }
  
  h2.mat-mdc-dialog-title {
    color: var(--mat-dialog-title-text-color, rgba(0, 0, 0, 0.87)) !important;
  }
  
  .mat-mdc-dialog-content {
    color: var(--mat-dialog-text-color, rgba(0, 0, 0, 0.87)) !important;
  }
  
  // Form field improvements
  .mat-mdc-form-field-label {
    color: var(--mat-form-field-label-text-color, rgba(0, 0, 0, 0.6)) !important;
  }
  
  .mat-mdc-text-field-wrapper {
    background-color: var(--mat-form-field-background-color, transparent) !important;
  }
  
  // Make input text visible
  .mat-mdc-input-element,
  .mdc-text-field__input,
  input.mat-mdc-input-element {
    color: var(--mat-form-field-input-text-color, rgba(0, 0, 0, 0.87)) !important;
  }
  
  .mdc-text-field--outlined .mdc-notched-outline__leading,
  .mdc-text-field--outlined .mdc-notched-outline__notch,
  .mdc-text-field--outlined .mdc-notched-outline__trailing {
    border-color: var(--mat-form-field-outline-color, rgba(0, 0, 0, 0.38)) !important;
  }
  
  .mdc-text-field:not(.mdc-text-field--disabled) .mdc-floating-label {
    color: var(--mat-form-field-label-text-color, rgba(0, 0, 0, 0.6)) !important;
  }
  
  .mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input {
    color: var(--mat-form-field-input-text-color, rgba(0, 0, 0, 0.87)) !important;
  }
  
  // Ensure hint and error text are visible
  .mat-mdc-form-field-hint-wrapper,
  .mat-mdc-form-field-error-wrapper {
    color: var(--mat-form-field-hint-text-color, rgba(0, 0, 0, 0.6)) !important;
  }
  
  .mat-mdc-form-field-hint {
    color: var(--mat-form-field-hint-text-color, rgba(0, 0, 0, 0.6)) !important;
  }
  
  .mat-mdc-form-field-error {
    color: var(--mat-form-field-error-text-color, #f44336) !important;
  }
  
  // Make sure select text is visible
  .mat-mdc-select-value-text {
    color: var(--mat-select-value-text-color, rgba(0, 0, 0, 0.87)) !important;
  }
  
  .mat-mdc-select-placeholder {
    color: var(--mat-form-field-placeholder-text-color, rgba(0, 0, 0, 0.6)) !important;
  }
  
  // Ensure select dropdown text is visible
  .mat-mdc-select-panel {
    background-color: var(--mat-select-panel-background-color, white) !important;
  }
  
  .mat-mdc-option {
    color: var(--mat-option-text-color, rgba(0, 0, 0, 0.87)) !important;
  }
  
  .mat-mdc-option .mdc-list-item__primary-text {
    color: var(--mat-option-text-color, rgba(0, 0, 0, 0.87)) !important;
  }
  
  // Button text visibility
  .mat-mdc-button {
    color: var(--mat-button-text-color, rgba(0, 0, 0, 0.87)) !important;
  }
  
  .mat-primary {
    &.mat-mdc-raised-button {
      color: var(--mat-primary-contrast-color, white) !important;
    }
  }
}

// Global dark mode support for Angular Material components
.mat-mdc-menu-panel {
  background-color: var(--mat-menu-background-color, white) !important;
  color: var(--mat-menu-text-color, rgba(0, 0, 0, 0.87)) !important;
}

.mat-mdc-menu-item {
  color: var(--mat-menu-item-text-color, rgba(0, 0, 0, 0.87)) !important;
}

.mat-mdc-option {
  color: var(--mat-option-text-color, rgba(0, 0, 0, 0.87)) !important;
}

.mat-mdc-option:hover:not(.mdc-list-item--disabled), 
.mat-mdc-option.mat-mdc-option-active {
  background-color: var(--mat-option-selected-state-layer-color, rgba(0, 0, 0, 0.04)) !important;
}

.mat-mdc-select-panel,
.mat-autocomplete-panel {
  background-color: var(--mat-select-panel-background-color, white) !important;
}

// Fix for chart canvas text in dark mode
.chart-container {
  canvas {
    color: var(--chart-text-color, rgba(0, 0, 0, 0.87));
  }
}

// Ensures that Material button text is visible in dark mode
.mat-mdc-raised-button.mat-primary {
  color: var(--mat-primary-contrast-color, white) !important;
}

.mat-mdc-button:not(:disabled) {
  color: var(--mat-button-text-color, rgba(0, 0, 0, 0.87)) !important;
}

.mat-mdc-icon-button {
  color: var(--mat-icon-button-icon-color, rgba(0, 0, 0, 0.54)) !important;
}

// Add specific styles for dropdown option text
.mat-mdc-option .mdc-list-item__primary-text {
  color: var(--mat-option-text-color, rgba(0, 0, 0, 0.87)) !important;
}

// Add dark theme variables - these will be applied in dark mode
@media (prefers-color-scheme: dark) {
  :root {
    // Dialog colors
    --mat-dialog-text-color: rgba(255, 255, 255, 0.87);
    --mat-dialog-title-text-color: rgba(255, 255, 255, 0.95);
    --mat-dialog-background-color: #303030;
    
    // Form field colors
    --mat-form-field-input-text-color: rgba(255, 255, 255, 0.87);
    --mat-form-field-label-text-color: rgba(255, 255, 255, 0.6);
    --mat-form-field-hint-text-color: rgba(255, 255, 255, 0.5);
    --mat-form-field-outline-color: rgba(255, 255, 255, 0.3);
    --mat-form-field-placeholder-text-color: rgba(255, 255, 255, 0.6);
    
    // Select and dropdown colors
    --mat-select-value-text-color: rgba(255, 255, 255, 0.87);
    --mat-option-text-color: rgba(255, 255, 255, 0.87);
    --mat-select-panel-background-color: #424242;
    --mat-option-selected-state-layer-color: rgba(255, 255, 255, 0.08);
    
    // Menu colors
    --mat-menu-background-color: #424242;
    --mat-menu-text-color: rgba(255, 255, 255, 0.87);
    --mat-menu-item-text-color: rgba(255, 255, 255, 0.87);
    
    // Button colors
    --mat-button-text-color: rgba(255, 255, 255, 0.87);
    --mat-icon-button-icon-color: rgba(255, 255, 255, 0.7);
    
    // Card colors
    --mat-card-background-color: #303030;
    --mat-card-text-color: rgba(255, 255, 255, 0.87);
    --mat-card-title-text-color: rgba(255, 255, 255, 0.95);
    
    // Primary colors
    --mat-primary-color: #7b1fa2; /* Appropriate for dark theme */
    --mat-primary-contrast-color: white;
    
    // General text colors
    --mat-text-primary-color: rgba(255, 255, 255, 0.87);
    --mat-text-secondary-color: rgba(255, 255, 255, 0.6);
    --mat-text-hint-color: rgba(255, 255, 255, 0.38);
    
    // Chart colors
    --chart-text-color: rgba(255, 255, 255, 0.87);
  }
  
  // Direct style overrides for dark mode to reinforce color settings
  .chart-dialog-container {
    .mat-mdc-dialog-surface, .mat-mdc-dialog-content, h2.mat-mdc-dialog-title {
      color: rgba(255, 255, 255, 0.87) !important;
      background-color: #303030 !important;
    }
    
    input.mat-mdc-input-element, .mat-mdc-input-element, .mdc-text-field__input {
      color: rgba(255, 255, 255, 0.87) !important;
    }
    
    .mat-mdc-form-field-label, .mdc-floating-label {
      color: rgba(255, 255, 255, 0.6) !important;
    }
    
    .mat-mdc-select-value-text {
      color: rgba(255, 255, 255, 0.87) !important;
    }
    
    .mat-mdc-form-field-hint {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }
  
  // Override select panel and options in dark mode
  .mat-mdc-select-panel, .cdk-overlay-pane .mat-mdc-option {
    background-color: #424242 !important;
    color: rgba(255, 255, 255, 0.87) !important;
  }
  
  .mat-mdc-option .mdc-list-item__primary-text {
    color: rgba(255, 255, 255, 0.87) !important;
  }
  
  // Ensure buttons have proper contrast
  .mat-mdc-button:not(.mat-primary) {
    color: rgba(255, 255, 255, 0.87) !important;
  }
  
  // Add a stronger text color override for the dialog container
  .mat-mdc-dialog-container .mdc-dialog__surface {
    color: white !important;
  }
  
  // Direct override for dropdown panel text
  .cdk-overlay-pane {
    .mdc-list-item__primary-text {
      color: white !important;
    }
    
    .mat-mdc-option {
      color: white !important;
    }
    
    .mat-mdc-select-panel, .mat-mdc-menu-panel, .mat-mdc-autocomplete-panel {
      background-color: #424242 !important;
    }
  }
  
  // Override dialog form text
  .chart-dialog-container {
    h2.mat-mdc-dialog-title,
    .mat-mdc-dialog-content,
    .mat-mdc-dialog-actions,
    input, 
    .mat-mdc-input-element,
    .mat-select-value-text,
    .mat-mdc-select-value,
    .mat-mdc-select-arrow,
    .mat-mdc-form-field label,
    .mdc-floating-label,
    .mat-form-field-label,
    .mat-hint,
    .mat-form-field-hint,
    .mat-error,
    .mat-button-wrapper {
      color: white !important;
    }
    
    .mat-mdc-dialog-container, 
    .mdc-dialog__container, 
    .mdc-dialog__surface {
      color: white !important;
      background-color: #303030 !important;
    }
  }
  
  // Force label colors
  .mdc-floating-label,
  .mdc-text-field:not(.mdc-text-field--disabled) .mdc-floating-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }
  
  // Force input text color
  .mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input {
    color: white !important;
  }
  
  // Force field outline colors
  .mdc-text-field--outlined .mdc-notched-outline__leading,
  .mdc-text-field--outlined .mdc-notched-outline__notch,
  .mdc-text-field--outlined .mdc-notched-outline__trailing {
    border-color: rgba(255, 255, 255, 0.3) !important;
  }
  
  // Force hint text color
  .mat-mdc-form-field-hint-wrapper .mat-mdc-form-field-hint {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  
  // Force dialog button text
  .mat-mdc-dialog-actions .mat-mdc-button {
    color: white !important;
  }
}

// Additional selector for cdkOverlay elements which could contain dropdowns
.cdk-overlay-container {
  .cdk-overlay-pane {
    .mat-mdc-select-panel, .mat-mdc-autocomplete-panel, .mat-mdc-menu-panel {
      background-color: var(--mat-select-panel-background-color, white) !important;
    }
    
    .mat-mdc-option, .mat-mdc-menu-item {
      color: var(--mat-option-text-color, rgba(0, 0, 0, 0.87)) !important;
    }
    
    .mdc-list-item__primary-text {
      color: var(--mat-option-text-color, rgba(0, 0, 0, 0.87)) !important;
    }
  }
}

// Remove margin-bottom from all mat-cards
.mat-mdc-card {
  margin-bottom: 0 !important;
}

// Force removal of margin-bottom with stronger selectors
.mat-mdc-card,
.mat-card,
div.mat-mdc-card,
.mat-mdc-card.mat-elevation-z1,
.mat-mdc-card.mat-elevation-z2,
.mat-mdc-card.mat-elevation-z3,
.mat-mdc-card.mat-elevation-z4,
.mat-mdc-card.mat-elevation-z5,
.mat-mdc-card.mat-elevation-z6,
.mat-mdc-card.mat-elevation-z7,
.mat-mdc-card.mat-elevation-z8 {
  margin-bottom: 0 !important;
}

// Ensure container elements don't add margins
.mat-mdc-card-content {
  margin-bottom: 0 !important;
}

// Target any possible card wrapper classes
div[class*="card"],
div[class*="tile"],
div[class*="panel"],
.chart-card {
  margin-bottom: 0 !important;
}

// Override specific card styling that may include these properties
.mat-mdc-card,
div.mat-mdc-card,
.mat-card,
div[class*="card"],
div[class*="tile"],
div[class*="panel"],
.chart-card,
[style*="margin-bottom: 16px"][style*="border-radius: 8px"][style*="overflow: hidden"],
.mdc-card {
  margin-bottom: 0 !important;
  & > * {
    margin-bottom: 0 !important;
  }
}

/* Target elements with these specific styles */
*[style*="margin-bottom: 16px"][style*="border-radius: 8px"][style*="overflow: hidden"] {
  margin-bottom: 0 !important;
}

/* Target chart cards specifically which might have these styles */
.chart-card,
div.chart-card,
.chart-container .mat-mdc-card,
.chart-dashboard-container .mat-mdc-card {
  margin-bottom: 0 !important;
  border-radius: 8px;
  overflow: hidden;
}