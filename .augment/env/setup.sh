#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Check Node.js version (should be compatible)
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"

# Enable Corepack (comes with Node.js 16.9+)
sudo corepack enable

# Navigate to workspace directory
cd /mnt/persist/workspace

# Corepack will automatically use the correct Yarn version specified in package.json
corepack prepare yarn@4.7.0 --activate

# Verify Yarn installation
echo "Yarn version: $(yarn --version)"

# Install dependencies using Yarn
yarn install

# Add NX to PATH for easier access
echo 'export PATH="$PATH:./node_modules/.bin"' >> $HOME/.profile

echo "Setup completed successfully!"
echo "The following projects have working unit tests:"
echo "- opc-component-library: 3 tests passing"
echo "- rj-ui-components: 1 test passing (some tests have deprecated Angular testing patterns)"
echo ""
echo "Other projects have test configuration issues that would need to be fixed:"
echo "- functions: Missing firebase-functions-test dependency"
echo "- angular-monorepo, greeder-boards: Missing NxWelcomeComponent and other dependencies"
echo "- ha-dashboard: Playwright e2e tests mixed with Jest unit tests, Firebase fetch issues"