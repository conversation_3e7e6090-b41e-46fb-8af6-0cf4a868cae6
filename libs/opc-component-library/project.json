{"name": "opc-component-library", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/opc-component-library/src", "prefix": "opc", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/opc-component-library/ng-package.json", "styles": ["node_modules/material-icons/iconfont/material-icons.css", "node_modules/material-design-icons/iconfont/material-icons.css", "./node_modules/@angular/material/prebuilt-themes/indigo-pink.css"]}, "configurations": {"production": {"tsConfig": "libs/opc-component-library/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/opc-component-library/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/opc-component-library/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}, "storybook": {"executor": "@storybook/angular:start-storybook", "options": {"port": 4400, "configDir": "libs/opc-component-library/.storybook", "browserTarget": "opc-component-library:build-storybook", "compodoc": false}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@storybook/angular:build-storybook", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/storybook/opc-component-library", "configDir": "libs/opc-component-library/.storybook", "browserTarget": "opc-component-library:build-storybook", "compodoc": false, "styles": ["node_modules/material-icons/iconfont/material-icons.css", "node_modules/material-design-icons/iconfont/material-icons.css", "./node_modules/@angular/material/prebuilt-themes/indigo-pink.css"]}, "configurations": {"ci": {"quiet": true}}}, "test-storybook": {"executor": "nx:run-commands", "options": {"command": "test-storybook -c libs/opc-component-library/.storybook --url=http://localhost:4400"}}, "static-storybook": {"executor": "@nx/web:file-server", "options": {"buildTarget": "opc-component-library:build-storybook", "staticFilePath": "dist/storybook/opc-component-library", "spa": true}, "configurations": {"ci": {"buildTarget": "opc-component-library:build-storybook:ci"}}}}}