.app-layout {
  height: 100%;
  width: 100%;
}

.component-layout {
  height: min-content;
  width: min-content;
}

.layout-container {
  // border-radius: 25px;
  display: flex;
  // border: 1px solid gray;
  // outline: 1px solid gray;

  display: grid;
  grid-template-rows: auto 8fr auto;
  /* Header, main content, footer */
  grid-template-columns: auto 8fr auto;
  /* Left sidebar, main content, right sidebar */
  grid-template-areas:
    "tl-corner top tr-corner"
    "left content right"
    "bl-corner bottom br-corner";


  .layout-content {
    z-index: 3;
    grid-area: content;
    flex: 1 1 100%;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    // box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  }

  // .content-layer-2 {
  //   grid-area: content;
  //   flex: 1 1 100%;
  //   width: 100%;
  //   height: 100%;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  //   background: white;
  //   box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  // }

}


.edge {
  transition: height 0.2s, width 0.2s, opacity 0.2s;
  width: 0;
  height: 0;
}

.top-edge {
  grid-area: top;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &.show {
    width: 100%;
  }
}

.bottom-edge {
  grid-area: bottom;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &.show {

    width: 100%;
  }
}

.left-edge {
  grid-area: left;
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  align-items: stretch;
  height: 100%;

  &.show {
    height: 100%;

  }
}

.right-edge {
  grid-area: right;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &.show {
    // width: 10vw;
    height: 100%;
  }
}

.border-radius-tl {
  border-top-left-radius: 25px;
}

.border-radius-tr {
  border-top-right-radius: 25px;
}

.border-radius-bl {
  border-bottom-left-radius: 25px;
}

.border-radius-br {
  border-bottom-right-radius: 25px;
}