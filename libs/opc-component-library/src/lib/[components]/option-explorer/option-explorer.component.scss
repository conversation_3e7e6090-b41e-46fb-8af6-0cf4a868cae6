:host {
  width: 100%;
  height: 100%;
}

.option-explorer-container {
  outline: 1px solid black;
  width: 100%;
  height: 100%;
  // min-height:300px;
  display: flex;
  flex-direction: column;

  color: var(--primary-font-color-2, #fff);
  background: var(--light-gray, #272727);
  border-radius: 25px 25px 0 0; // make this a variable
}

mat-toolbar {
  border-radius: 25px 25px 0 0; // make this a variable
  background-color: var(--primary-background-color-1, #373737);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  color: var(--primary-font-color-2);
}


mat-tab-group {
  display: flex;
  flex: 1 1 100%;
  overflow: auto;
  height: 100%;
  width: 100%;
  color: var(--primary-font-color-1, #fff);
  background-color: transparent;

  mat-tab {
    overflow: auto;
  }
}

/***
    .mat-tab-body-wrapper set in styles.scss

  */

.tab-content {
  border: 1px solid black;
  align-self: center;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  background: #292929;
  overflow: hidden;
  position: relative;
  left: 10px;
  top: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  transition: all 500ms;




  #inset-placeholder {
    z-index: 2;
    content: '';
    display: block;
    box-shadow: inset 0 0 25px black;
    position: absolute;
    right: 0;
    bottom: 0;
    // border-radius: 12px;
    height: 100%;
    width: 100%;
    pointer-events: none;
    opacity: 1;

    // animation-name: fadeIn;
    // animation-duration: 3s;

  }

  .inner-tab-content {
    background-color: #1c1c1c;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    gap: 10px 10px;
    overflow-y: auto;
    overflow-x: hidden;
    // background: darken(#292929, 5%);
    // background: (var(--background-image-1));


    // background-image: url('../../../../assets/images/DALL·E 2023-01-27 11.00.09.png');

    .card-container {
      height: 130px;
      perspective: 500px;
      transform-style: preserve-3d;
      background-color: transparent;

      option-card {
        backface-visibility: hidden;
        max-height: 130px;

        &:C {
          transform: rotateX(0deg);
        }

        &:P {
          transform: rotateX(180deg);
        }

        &:first-child {
          margin-top: 10px;
        }

        &:last-child {
          margin-bottom: 10px;
        }


        flex: 1 1 auto;
        display: flex;
        margin: 0 10px 0 10px;
      }
    }
  }

}

.strike-marker {
  width: 100%;
  color: black;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-family: monospace;
  min-height: 30px;
  flex-direction: row;
  background: black;
  color: rgb(171, 171, 171);
  transition: all 0.3s;
  border: 1px solid var(--primary-font-color-1);
  // border-radius: 25px;
}

.no-ticker {
  height: 100%;
  width: 100%;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgb(171, 171, 171);
}

::ng-deep .mat-mdc-tab-body-wrapper {
  height: 100%;
  display: flex;
}