button {
  transition: all 500ms;
  color: inherit;
  height: 50px;
  width: 50px;

  border-radius: 150px;
  box-shadow: 2px 2px 5px var(--shadow-dark, #1b1b1b),
    -2px -2px 5px var(--shadow-light, #373737);
  outline-style: none;
  border-style: none;
  background: inherit;


  &:active {
    box-shadow: inset 2px 2px 5px var(--shadow-inner-dark, #0d0d0d),
      inset -2px -2px 5px var(--shadow-inner-light, #2b2b2b);
    background: #373737;
  }
}