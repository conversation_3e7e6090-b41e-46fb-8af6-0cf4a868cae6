{"name": "rj-ui-components", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/rj-ui-components/src", "prefix": "r<PERSON>i", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/rj-ui-components/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/rj-ui-components/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/rj-ui-components/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/rj-ui-components/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}