# rj-ui-components

A comprehensive Angular UI component library featuring modern, responsive components built with Angular 19's signals and RxJS.

## Library Structure

This library is organized by component type, with each folder under `src/lib` containing a collection of related components:

- **animations**: Animation utilities and components
- **buttons**: Button variants and controls
- **cards**: Card components for displaying content in containers
- **containers**: Layout container components
- **dialogs**: Dialog and popup components
- **form**: Form-related components and utilities
- **inputs**: Form input components (selectors, checkboxes, etc.)
- **layouts**: Page layout components
- **lists**: List and list item components
- **modals**: Modal window components
- **page**: Page structure components
- **pages**: Showcase pages demonstrating each component type (used in the showcase app)
- **placeholders**: Placeholder and loading components
- **rj-ui-components**: Core components
- **styles**: Global styles and CSS utilities
- **tables**: Table components and data grids
- **themes**: Theming utilities and components
- **timeline**: Timeline components for sequential data
- **wizards**: Wizard components for multi-step processes, including:
  - **fs-wizard**: Fullscreen responsive wizard components
  - **wizard-container**: Container components for wizards
  - **wizard-step**: Step components for wizards
  - **wizard-overlay**: Overlay services for modal wizards

## Usage

Import components directly from the library:

```typescript
import { FsWizardService } from '@your-org/rj-ui-components';
```

## Development

This library was generated with [Nx](https://nx.dev).

### Running unit tests

Run `nx test rj-ui-components` to execute the unit tests.
