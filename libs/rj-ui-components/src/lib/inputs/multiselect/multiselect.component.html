<!-- <div class="cuppa-dropdown" (clickOutside)="closeDropdown()">
    <div class="selected-list">
        <div class="c-btn" (click)="toggleDropdown($event)" [class.disabled]="settings.disabled">
            <span class="c-placeholder" *ngIf="selectedItems?.length === 0">{{settings.placeholder}}</span>
            <span *ngIf="settings.singleSelection">
                <span *ngFor="let item of selectedItems;trackBy: trackByFn;">
                    {{item[labelKey]}}
                </span>
            </span>
            <div class="c-list" *ngIf="selectedItems?.length > 0 && !settings.singleSelection">
                <div class="c-token" *ngFor="let item of selectedItems;trackBy: trackByFn;let k = index" [hidden]="k > settings.badgeShowLimit-1">
                    <span class="c-label">{{item[labelKey]}}</span>
                    <span class="close" (click)="onItemClick(item,k,$event)" disabled="true"> ×</span>
                </div>
            </div>
            <span *ngIf="selectedItems?.length > settings.badgeShowLimit">+{{selectedItems?.length - settings.badgeShowLimit }}</span> -->
            <!-- <span class="fa" [ngClass]="{'fa-angle-down': !isActive,'fa-angle-up':isActive}"></span> -->
        <!-- </div>
    </div>
    <div class="dropdown-list" [hidden]="!isActive">
        <div class="list-area"> -->
            <!-- <div class="pure-checkbox select-all" *ngIf="settings.enableCheckAll && !settings.singleSelection && !settings.limitSelection" (click)="toggleSelectAll()">
                <label>
                    <span [hidden]="isSelectAll">{{settings.selectAllText}}</span>
                    <span [hidden]="!isSelectAll">{{settings.unSelectAllText}}</span>
                </label>
            </div>    -->
            <!-- <div class="list-filter" *ngIf="settings.enableSearchFilter">
                <span class="fa fa-search"></span>
                <input type="text" [placeholder]="settings.searchPlaceholderText" [(ngModel)]="filter[labelKey]">
            </div>
            <ul [style.maxHeight]="settings.maxHeight+'px'">
                <li *ngFor="let item of data" (click)="onItemClick(item)" class="pure-checkbox">
                    <span>{{item[labelKey]}}</span>
                </li>
            </ul>
            <h5 class="list-message" *ngIf="data?.length === 0">No Data Available</h5>
        </div>
    </div>
</div> -->