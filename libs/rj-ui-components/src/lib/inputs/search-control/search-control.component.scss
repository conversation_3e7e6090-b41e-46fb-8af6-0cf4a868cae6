.search-control {
    display: flex;
    position: relative;
    margin-right:10px;
}

.search-control .search-input {
    display: flex;
    border-radius: 25px;
}

.search-control .search-icon {
    position: absolute;
    top: 12px;
    right: 10px;
    font-size: 14px;
    color: #0073bf;
}
.form-control{
    width: 100%;
    height: 35px;
    padding-left:15px;
    padding-right:15px;
    color: #515d6e;
    background-color: #fff;
    background-image: none;
    border: 1px solid #dbd9d9;
    border-radius: 2px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    -webkit-transition: border-color ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
}