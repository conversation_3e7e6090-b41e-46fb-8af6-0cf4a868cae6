
@import '../../themes/_basic-theme.scss';



.header{
    @include header($z-index:2);
    display:flex;
    flex-direction: row;
    justify-content: space-between;
    .menu-toggle{
        display:flex;
        justify-content: flex-end;
        flex: 1 1 100%;
    }
}

.card-list{
    display:flex;
    flex-direction: row;
    justify-content: space-evenly;
}

.test-modal{
    display:flex;
    background-color:White;
    border: 1px solid black;
    border-radius:7px;
    height:200px;
    width:200px;
    align-items:center;
    justify-content:center;

}