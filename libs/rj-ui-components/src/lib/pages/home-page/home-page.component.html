<rjui-layout-1 #layout>

  <ng-template #nb name="navbar">
    <div class="header">
      <div class="title">
        Home
      </div>
      <div class="menu-toggle">
        <i class="fa fa-bars" (click)="layout.toggleMenu('rom')"></i>
      </div>
    </div>
  </ng-template>

  <ng-template #lsm name="leftsidemenu">
  </ng-template>

  <ng-template #rom name="rightoverlaymenu">
    <mat-nav-list style="background-color:white;height:100%;">
      <a mat-list-item href="#" [routerLink]="['/home']">
        <b>Home</b>
      </a>
      <a mat-list-item href="#" [routerLink]="['/a11y']">Accessibility</a>
      <a mat-list-item href="#" [routerLink]="['/buttons']">Buttons</a>
      <a mat-list-item href="#" [routerLink]="['/cards']">Cards</a>
      <a mat-list-item href="#" [routerLink]="['/chips']">Chips</a>
      <a mat-list-item href="#" [routerLink]="['/containers']">Containers</a>
      <a mat-list-item href="#" [routerLink]="['/dialogs']">Dialogs</a>
      <a mat-list-item href="#" [routerLink]="['/inputs']">Inputs</a>
      <a mat-list-item href="#" [routerLink]="['/i18n']">Internationalization</a>
      <a mat-list-item href="#" [routerLink]="['/layouts']">Layouts</a>
      <a mat-list-item href="#" [routerLink]="['/lists']">Lists</a>
      <a mat-list-item href="#" [routerLink]="['/modals']">Modals</a>
      <a mat-list-item href="#" [routerLink]="['/motion']">Motion</a>
      <a mat-list-item href="#" [routerLink]="['/notifications']">Notifications</a>
      <a mat-list-item href="#" [routerLink]="['/placeholders']">Placeholders</a>
      <a mat-list-item href="#" [routerLink]="['/resources']">Resources</a>
      <a mat-list-item href="#" [routerLink]="['/tables']">Tables</a>
      <a mat-list-item href="#" [routerLink]="['/themes']">Themes</a>
      <a mat-list-item href="#" [routerLink]="['/typography']">Typography</a>
      <!-- <a mat-list-item href="#" [routerLink]="['/WFD']" [queryParams]="{'sections':[]}">WFD</a> -->
    </mat-nav-list>
  </ng-template>

  <ng-template #modal name="modal">
    <div class="test-modal">
      this is a test modal
    </div>
  </ng-template>

  <ng-template #mc name="maincontent">
    <section class="container">
      <h1>Welcome to RJ UI Components</h1>
      <p>A comprehensive collection of Angular UI components for your applications.</p>
      
      <div class="info-section">
        <h2>Available Components</h2>
        <ul>
          <li>Buttons - Various button styles and types</li>
          <li>Cards - Flexible card layouts</li>
          <li>Containers - Layout containers</li>
          <li>Timeline - Interactive timeline component</li>
          <li>More components coming soon...</li>
        </ul>
      </div>
    </section>
  </ng-template>

  <ng-template #rsm name="rightsidemenu">
  </ng-template>

</rjui-layout-1>
