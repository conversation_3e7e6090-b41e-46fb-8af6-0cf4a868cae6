<rjui-generic-page title="Wizards">
  <rjui-documentation id="Overview" title="Overview">
    <p>Wizards guide users through complex processes by breaking them into manageable steps. They enhance user experience 
    by providing a clear path to completion and context for each decision point.</p>
  </rjui-documentation>

  <rjui-documentation id="Basic Usage" title="Basic Usage">
    <p>The Wizard component system consists of several parts:</p>
    <ul>
      <li><strong>WizardContainerComponent</strong>: The main container for the wizard</li>
      <li><strong>WizardStepComponent</strong>: Individual steps within the wizard</li>
      <li><strong>WizardOverlayService</strong>: Service to display wizards as overlay modals</li>
      <li><strong>FsWizardService</strong>: Service to display fullscreen wizards</li>
    </ul>

    <h3>Basic Implementation</h3>
    <pre><code>
&lt;rjui-wizard-container 
  title="Registration Wizard" 
  [showNavigation]="true"
  (completed)="wizardComplete($event)"
  (cancelled)="wizardCancelled()">
  
  &lt;rjui-wizard-step title="Step 1" [isValid]="validateStep1()">
    &lt;!-- Step 1 content -->
  &lt;/rjui-wizard-step>
  
  &lt;rjui-wizard-step title="Step 2">
    &lt;!-- Step 2 content -->
  &lt;/rjui-wizard-step>
  
&lt;/rjui-wizard-container>
    </code></pre>
  </rjui-documentation>

  <rjui-documentation id="Examples" title="Examples">
    <div class="fullscreen-wizard-demo">
      <h3>Fullscreen Wizard Demo</h3>
      <p>
        Fullscreen wizards provide an immersive experience that focuses the user's attention on the task at hand.
        Click the button below to launch a fullscreen wizard with animated transitions.
      </p>
      <button mat-raised-button color="primary" (click)="openFullscreenWizard()">
        <mat-icon>fullscreen</mat-icon>
        Launch Fullscreen Wizard
      </button>
    </div>

    <h3>Basic Wizard Example</h3>
    <p>A basic wizard with multiple steps for collecting user information.</p>
    
    <div class="example-container">
      <rjui-wizard-container 
        title="Registration Wizard" 
        [showNavigation]="true"
        (completed)="wizardComplete($event)"
        (cancelled)="wizardCancelled()"
        (stepChange)="stepChanged($event)">
        
        <rjui-wizard-step title="Basic Information" [isValid]="validateBasicInfo()">
          <h3>Personal Information</h3>
          <p>Please enter your basic contact information.</p>
          
          <div class="form-group">
            <mat-form-field appearance="outline">
              <mat-label>First Name</mat-label>
              <input matInput [(ngModel)]="basicInfo['firstName']" required>
            </mat-form-field>
          </div>
          
          <div class="form-group">
            <mat-form-field appearance="outline">
              <mat-label>Last Name</mat-label>
              <input matInput [(ngModel)]="basicInfo['lastName']" required>
            </mat-form-field>
          </div>
          
          <div class="form-group">
            <mat-form-field appearance="outline">
              <mat-label>Email</mat-label>
              <input matInput type="email" [(ngModel)]="basicInfo['email']" required>
            </mat-form-field>
          </div>
        </rjui-wizard-step>
        
        <rjui-wizard-step title="Address" [isValid]="validateAddressInfo()">
          <h3>Address Information</h3>
          <p>Please enter your address details.</p>
          
          <div class="form-group">
            <mat-form-field appearance="outline">
              <mat-label>Street Address</mat-label>
              <input matInput [(ngModel)]="addressInfo['street']" required>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <mat-form-field appearance="outline">
                <mat-label>City</mat-label>
                <input matInput [(ngModel)]="addressInfo['city']" required>
              </mat-form-field>
            </div>
            
            <div class="form-group">
              <mat-form-field appearance="outline">
                <mat-label>State</mat-label>
                <mat-select [(ngModel)]="addressInfo['state']" required>
                  <mat-option value="AL">Alabama</mat-option>
                  <mat-option value="AK">Alaska</mat-option>
                  <mat-option value="AZ">Arizona</mat-option>
                  <mat-option value="CA">California</mat-option>
                  <mat-option value="CO">Colorado</mat-option>
                  <mat-option value="NY">New York</mat-option>
                  <mat-option value="TX">Texas</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            
            <div class="form-group">
              <mat-form-field appearance="outline">
                <mat-label>ZIP Code</mat-label>
                <input matInput [(ngModel)]="addressInfo['zip']" required>
              </mat-form-field>
            </div>
          </div>
        </rjui-wizard-step>
        
        <rjui-wizard-step title="Preferences">
          <h3>User Preferences</h3>
          <p>Set your preferences for the application.</p>
          
          <div class="form-group">
            <h4>Theme</h4>
            <mat-radio-group [(ngModel)]="preferenceInfo['theme']">
              <mat-radio-button value="light">Light</mat-radio-button>
              <mat-radio-button value="dark">Dark</mat-radio-button>
              <mat-radio-button value="system">System Default</mat-radio-button>
            </mat-radio-group>
          </div>
          
          <div class="form-group">
            <mat-checkbox [(ngModel)]="preferenceInfo['notifications']">
              Enable notifications
            </mat-checkbox>
          </div>
          
          <div class="form-group">
            <mat-checkbox [(ngModel)]="preferenceInfo['marketingEmails']">
              Receive marketing emails
            </mat-checkbox>
          </div>
        </rjui-wizard-step>
        
        <rjui-wizard-step title="Review & Confirm">
          <h3>Review Your Information</h3>
          <p>Please review your information before submitting.</p>
          
          <div class="review-section">
            <h4>Personal Information</h4>
            <p><strong>Name:</strong> {{basicInfo['firstName']}} {{basicInfo['lastName']}}</p>
            <p><strong>Email:</strong> {{basicInfo['email']}}</p>
          </div>
          
          <div class="review-section">
            <h4>Address</h4>
            <p><strong>Street:</strong> {{addressInfo['street']}}</p>
            <p><strong>City:</strong> {{addressInfo['city']}}</p>
            <p><strong>State:</strong> {{addressInfo['state']}}</p>
            <p><strong>ZIP:</strong> {{addressInfo['zip']}}</p>
          </div>
          
          <div class="review-section">
            <h4>Preferences</h4>
            <p><strong>Theme:</strong> {{preferenceInfo['theme']}}</p>
            <p><strong>Notifications:</strong> {{preferenceInfo['notifications'] ? 'Enabled' : 'Disabled'}}</p>
            <p><strong>Marketing Emails:</strong> {{preferenceInfo['marketingEmails'] ? 'Yes' : 'No'}}</p>
          </div>
        </rjui-wizard-step>
      </rjui-wizard-container>
    </div>
  </rjui-documentation>

  <rjui-documentation id="Fullscreen Wizard" title="Fullscreen Wizard" description="A modern, responsive fullscreen wizard designed for multi-step user interfaces.">
    <div class="content">
      <h3>Responsive Fullscreen Wizard</h3>
      <p>
        The Fullscreen Wizard provides a distraction-free environment for complex multi-step processes.
        It adapts to different screen sizes, providing an optimized experience on both desktop and mobile.
      </p>
      
      <div class="wizard-demo-buttons">
        <button 
          mat-raised-button 
          color="primary" 
          (click)="openResponsiveFullscreenWizard()">
          Open Responsive Fullscreen Wizard
        </button>
      </div>
      
      <h4>Features</h4>
      <ul>
        <li>Fullscreen interface for focused user interaction</li>
        <li>Responsive design with mobile-specific layouts</li>
        <li>Step validation with customizable validation functions</li>
        <li>Progress tracking with visual indicators</li>
        <li>Built with Angular 19 signals for reactive state management</li>
      </ul>
      
      <h4>Code Example</h4>
      <pre><code>
// Import statements
import &#123; Component, inject &#125; from '&#64;angular/core';
import &#123; FsWizardService &#125; from '&#64;org/rj-ui-components';

// Component decorator and class
&#64;Component(&#123;
  selector: 'app-my-component',
  template: `&lt;button (click)="openWizard()"&gt;Open Wizard&lt;/button&gt;`
&#125;)
export class MyComponent &#123;
  // Inject the service
  private fsWizardService = inject(FsWizardService);
  
  // Method to open the wizard
  openWizard(): void &#123;
    this.fsWizardService.openWizard(&#123;
      title: 'My Fullscreen Wizard',
      steps: [
        // Step 1
        &#123;
          title: 'Step 1',
          subtitle: 'First step subtitle',
          icon: 'description',
          content: '&lt;h3&gt;Step 1 Content&lt;/h3&gt;'
        &#125;,
        // Additional steps...
      ],
      onComplete: (data) => console.log('Complete'),
      onCancel: () => console.log('Cancelled')
    &#125;);
  &#125;
&#125;
      </code></pre>
    </div>
  </rjui-documentation>

  <rjui-documentation id="API Reference" title="API Reference">
    <h3>Wizard Container Properties</h3>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>title</td>
          <td>string</td>
          <td>Title displayed in the wizard header</td>
        </tr>
        <tr>
          <td>showNavigation</td>
          <td>boolean</td>
          <td>Whether to show the navigation buttons</td>
        </tr>
        <tr>
          <td>currentStep</td>
          <td>number</td>
          <td>Index of the current active step</td>
        </tr>
        <tr>
          <td>totalSteps</td>
          <td>number</td>
          <td>Total number of steps in the wizard</td>
        </tr>
      </tbody>
    </table>

    <h3>Wizard Container Events</h3>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>completed</td>
          <td>EventEmitter&lt;Record&lt;string, unknown&gt;&gt;</td>
          <td>Emitted when the wizard is completed</td>
        </tr>
        <tr>
          <td>cancelled</td>
          <td>EventEmitter&lt;void&gt;</td>
          <td>Emitted when the wizard is cancelled</td>
        </tr>
        <tr>
          <td>stepChange</td>
          <td>EventEmitter&lt;number&gt;</td>
          <td>Emitted when the active step changes</td>
        </tr>
        <tr>
          <td>nextStep</td>
          <td>EventEmitter&lt;void&gt;</td>
          <td>Emitted when the next button is clicked</td>
        </tr>
        <tr>
          <td>previousStep</td>
          <td>EventEmitter&lt;void&gt;</td>
          <td>Emitted when the previous button is clicked</td>
        </tr>
      </tbody>
    </table>

    <h3>Wizard Step Properties</h3>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>title</td>
          <td>string</td>
          <td>Title of the step</td>
        </tr>
        <tr>
          <td>subtitle</td>
          <td>string</td>
          <td>Optional subtitle for the step</td>
        </tr>
        <tr>
          <td>icon</td>
          <td>string</td>
          <td>Optional Material icon name</td>
        </tr>
        <tr>
          <td>active</td>
          <td>boolean</td>
          <td>Whether the step is currently active</td>
        </tr>
        <tr>
          <td>isValid</td>
          <td>boolean</td>
          <td>Whether the step content is valid</td>
        </tr>
      </tbody>
    </table>
  </rjui-documentation>
</rjui-generic-page>
