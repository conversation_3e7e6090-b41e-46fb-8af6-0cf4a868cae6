import { Component, ViewChildren, Query<PERSON>ist, inject, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatCheckboxModule } from '@angular/material/checkbox';

import { DocumentationComponent } from '../../containers/documentation/documentation.component';
import { GenericPageComponent } from "../generic-page/generic-page.component";
import { WizardContainerComponent, WizardStepComponent, WizardOverlayService, FsWizardService } from '../../wizards/fs-wizard';

export type WizardData = Record<string, unknown>;

@Component({
  selector: 'rjui-wizards-page',
  templateUrl: './wizards-page.component.html',
  styleUrls: ['./wizards-page.component.scss'],
  standalone: true,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatCheckboxModule,
    GenericPageComponent,
    DocumentationComponent,
    WizardContainerComponent,
    WizardStepComponent
  ]
})
export class WizardsPageComponent {
  @ViewChildren(DocumentationComponent) sections: QueryList<DocumentationComponent> = new QueryList<DocumentationComponent>();

  sectionList: string[] = [
    'Overview',
    'Basic Usage',
    'Examples',
    'Fullscreen Wizard',
    'API Reference'
  ];

  // User data models for the different steps
  basicInfo: Record<string, string | boolean> = {
    firstName: '',
    lastName: '',
    email: ''
  };

  addressInfo: Record<string, string | boolean> = {
    street: '',
    city: '',
    state: '',
    zip: ''
  };

  preferenceInfo: Record<string, string | boolean> = {
    theme: 'light',
    notifications: true,
    marketingEmails: false
  };

  private wizardService = inject(WizardOverlayService);
  private fsWizardService = inject(FsWizardService);

  /**
   * Opens a fullscreen wizard using the wizard overlay service
   */
  openFullscreenWizard(): void {
    this.wizardService.openWizard({
      title: 'My Fullscreen Wizard',
      steps: [
        {
          title: 'Personal Information',
          subtitle: 'Tell us about yourself',
          icon: 'person',
          content: 'personal-info',
          data: this.basicInfo,
          validationFn: () => this.validateBasicInfo()
        },
        {
          title: 'Address',
          subtitle: 'Where should we send your items?',
          icon: 'home',
          content: 'address-info',
          data: this.addressInfo,
          validationFn: () => this.validateAddressInfo()
        },
        {
          title: 'Preferences',
          subtitle: 'Set your account preferences',
          icon: 'settings',
          content: 'preferences',
          data: this.preferenceInfo
        },
        {
          title: 'Review',
          subtitle: 'Review and confirm your information',
          icon: 'check_circle',
          content: 'review'
        }
      ],
      onComplete: (data: Record<string, unknown>) => this.wizardComplete(data),
      onCancel: () => this.wizardCancelled()
    });
  }

  // Wizard event handlers
  wizardComplete(data: WizardData): void {
    console.log('Wizard completed with data:', data);
  }

  wizardCancelled(): void {
    console.log('Wizard cancelled');
  }

  stepChanged(stepIndex: number): void {
    console.log('Step changed to:', stepIndex);
  }

  /**
   * Validate the basic information step
   */
  validateBasicInfo(): boolean {
    const info = this.basicInfo;
    return !!(info['firstName'] && info['lastName'] && info['email']);
  }

  /**
   * Validate the address information step
   */
  validateAddressInfo(): boolean {
    const info = this.addressInfo;
    return !!(info['street'] && info['city'] && info['state'] && info['zip']);
  }

  // Open the new responsive fullscreen wizard
  openResponsiveFullscreenWizard(): void {
    this.fsWizardService.openWizard({
      title: 'Responsive Fullscreen Wizard',
      steps: [
        {
          title: 'Personal Information',
          subtitle: 'Tell us about yourself',
          icon: 'person',
          content: `
            <div style="padding: 1rem;">
              <h3>Personal Information</h3>
              <p>Please provide your basic personal information below:</p>
              <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">First Name</label>
                <input type="text" style="width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">
              </div>
              <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Last Name</label>
                <input type="text" style="width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">
              </div>
              <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Email Address</label>
                <input type="email" style="width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">
              </div>
            </div>
          `,
          data: this.basicInfo,
          validationFn: () => this.validateBasicInfo()
        },
        {
          title: 'Address',
          subtitle: 'Where should we send your items?',
          icon: 'home',
          content: `
            <div style="padding: 1rem;">
              <h3>Address Information</h3>
              <p>Please provide your shipping address:</p>
              <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Street Address</label>
                <input type="text" style="width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">
              </div>
              <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">City</label>
                <input type="text" style="width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">
              </div>
              <div style="display: flex; gap: 1rem;">
                <div style="flex: 1; margin-bottom: 1rem;">
                  <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">State</label>
                  <input type="text" style="width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">
                </div>
                <div style="flex: 1; margin-bottom: 1rem;">
                  <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Zip Code</label>
                  <input type="text" style="width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">
                </div>
              </div>
            </div>
          `,
          data: this.addressInfo,
          validationFn: () => this.validateAddressInfo()
        },
        {
          title: 'Preferences',
          subtitle: 'Set your account preferences',
          icon: 'settings',
          content: `
            <div style="padding: 1rem;">
              <h3>Preferences</h3>
              <p>Customize your account settings:</p>
              <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Theme</label>
                <select style="width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="system">System Default</option>
                </select>
              </div>
              <div style="margin-bottom: 1rem;">
                <label style="display: flex; align-items: center; cursor: pointer;">
                  <input type="checkbox" checked style="margin-right: 0.5rem;">
                  <span>Enable notifications</span>
                </label>
              </div>
              <div style="margin-bottom: 1rem;">
                <label style="display: flex; align-items: center; cursor: pointer;">
                  <input type="checkbox" style="margin-right: 0.5rem;">
                  <span>Receive marketing emails</span>
                </label>
              </div>
            </div>
          `,
          data: this.preferenceInfo,
          optional: true
        },
        {
          title: 'Review',
          subtitle: 'Review and confirm your information',
          icon: 'check_circle',
          content: `
            <div style="padding: 1rem;">
              <h3>Review Your Information</h3>
              <p>Please review the information you've provided:</p>
              
              <div style="margin: 1.5rem 0; padding: 1rem; border: 1px solid #e0e0e0; border-radius: 4px; background-color: #f5f5f5;">
                <h4 style="margin-top: 0;">Personal Information</h4>
                <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 0.5rem;">
                  <div style="font-weight: 500;">Name:</div>
                  <div>John Doe</div>
                  <div style="font-weight: 500;">Email:</div>
                  <div><EMAIL></div>
                </div>
              </div>
              
              <div style="margin: 1.5rem 0; padding: 1rem; border: 1px solid #e0e0e0; border-radius: 4px; background-color: #f5f5f5;">
                <h4 style="margin-top: 0;">Address</h4>
                <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 0.5rem;">
                  <div style="font-weight: 500;">Street:</div>
                  <div>123 Main St</div>
                  <div style="font-weight: 500;">City:</div>
                  <div>Anytown</div>
                  <div style="font-weight: 500;">State:</div>
                  <div>CA</div>
                  <div style="font-weight: 500;">Zip:</div>
                  <div>12345</div>
                </div>
              </div>
              
              <div style="margin: 1.5rem 0; padding: 1rem; border: 1px solid #e0e0e0; border-radius: 4px; background-color: #f5f5f5;">
                <h4 style="margin-top: 0;">Preferences</h4>
                <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 0.5rem;">
                  <div style="font-weight: 500;">Theme:</div>
                  <div>Light</div>
                  <div style="font-weight: 500;">Notifications:</div>
                  <div>Enabled</div>
                  <div style="font-weight: 500;">Marketing:</div>
                  <div>Not subscribed</div>
                </div>
              </div>
            </div>
          `
        }
      ],
      onComplete: (data: WizardData) => this.wizardComplete(data),
      onCancel: () => this.wizardCancelled()
    });
  }
}
