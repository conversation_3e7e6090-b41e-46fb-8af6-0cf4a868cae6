// Styles for the wizards page component

// Basic page styles
:host {
  display: block;
}

.fullscreen-wizard-demo {
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: #f5f7fa;
  border-radius: 8px;
  
  h3 {
    margin-top: 0;
    color: var(--primary-color, #3f51b5);
  }
  
  button {
    margin-top: 1rem;
  }
}

.example-container {
  margin: 2rem 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.form-group {
  margin-bottom: 1rem;
  
  mat-form-field {
    width: 100%;
  }
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.5rem;
  
  .form-group {
    flex: 1 1 200px;
    padding: 0 0.5rem;
    min-width: 0;
  }
}

mat-radio-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  
  mat-radio-button {
    margin: 0.5rem 0;
  }
}

.review-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
  
  h4 {
    margin-bottom: 0.5rem;
    color: #333;
  }
  
  p {
    margin: 0.25rem 0;
    color: #555;
  }
}

.api-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 2rem;
  
  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
  }
  
  th {
    background-color: #f5f5f5;
    font-weight: 500;
  }
  
  tr:hover {
    background-color: #f9f9f9;
  }
}

.wizard-demo-buttons {
  display: flex;
  gap: 1rem;
  margin: 1.5rem 0;
}

// Responsive styles
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    
    .form-group {
      flex: 1 1 100%;
    }
  }
  
  .wizard-demo-buttons {
    flex-direction: column;
    align-items: stretch;
  }
}
