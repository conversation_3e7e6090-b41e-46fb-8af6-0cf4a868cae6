# Pages Directory

## Purpose

This directory contains page components that are imported into the showcase app. These components serve as demonstration and documentation pages for the component library.

## Usage

Each subdirectory contains a standalone page component that demonstrates a specific component or component group from the library. These pages are directly referenced in the main application's routing to create the showcase/demo site.

## Structure

- Each page component is named with the `-page` suffix (e.g., `wizards-page`)
- Pages typically provide:
  - Overview and description of the component(s)
  - Usage examples
  - API documentation
  - Interactive demos

## Distinction from Component Definitions

The components in this directory are **showcase pages only** and should not be imported into consumer applications. They are meant to demonstrate and document the actual reusable components.

The actual reusable component definitions live in their own respective directories (e.g., `/lib/wizards`, `/lib/buttons`, etc.).

## Example

The `wizards-page` component imports and demonstrates the actual wizard components from `/lib/wizards/` directory. It shows how to use these components and documents their APIs, but is not itself a reusable component intended for consumer applications. 