/**
 * Pages Package
 * 
 * This directory contains page components that are imported into the showcase app.
 * These are NOT regular components intended for reuse in consumer applications.
 * 
 * The pages in this directory are:
 * - Documentation pages used to demonstrate component usage
 * - Demonstration pages with examples and API documentation
 * - Pages that compose various components to show how they work together
 * 
 * The actual reusable component definitions live in their own respective directories
 * (e.g., /lib/wizards/, /lib/buttons/, etc.).
 */

export * from './wizards-page/wizards-page.component';
export * from './typography-page/typography-page.component';
export * from './themes-page/themes-page.component';
export * from './timeline-page/timeline-page.component';
export * from './placeholders-page/placeholders-page.component';
export * from './resources-page/resources-page.component';
export * from './tables-page/tables-page.component';
export * from './motion-page/motion-page.component';
export * from './notifications-page/notifications-page.component';
export * from './home-page/home-page.component';
export * from './i18n-page/i18n-page.component';
export * from './modal-page/modal-page.component';
export * from './frames-page/frames-page.component';
export * from './generic-page/generic-page.component';
export * from './container-page/container-page.component';
export * from './dialogs-page/dialogs-page.component';
export * from './a11y-page/a11y-page.component'; 