import { Component, ViewChildren, QueryList } from '@angular/core';
import { DocumentationComponent } from '../../containers/documentation/documentation.component';
import { GenericPageComponent } from "../generic-page/generic-page.component";

@Component({
  selector: 'rjui-frames-page',
  templateUrl: './frames-page.component.html',
  styleUrls: ['./frames-page.component.scss'],
  imports: [GenericPageComponent]
})
export class FramesPageComponent {
  @ViewChildren(DocumentationComponent) sections: QueryList<DocumentationComponent> = new QueryList<DocumentationComponent>();

  sectionList: string[] = [
  ];
}
