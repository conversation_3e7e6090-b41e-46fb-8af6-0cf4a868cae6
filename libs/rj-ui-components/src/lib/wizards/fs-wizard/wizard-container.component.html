<div class="wizard-container">
  <div class="wizard-header">
    <h2 class="wizard-title">{{ title }}</h2>
    <button mat-icon-button (click)="cancel()" aria-label="Close wizard">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  
  <!-- Navigation Steps -->
  <div class="wizard-steps" *ngIf="totalSteps > 0">
    <div class="step-indicators">
      <ng-container *ngFor="let i of [].constructor(totalSteps); let index = index">
        <div class="step-indicator" 
             [class.active]="currentStep === index"
             [class.completed]="currentStep > index">
          <div class="step-number">
            <mat-icon *ngIf="currentStep > index">check</mat-icon>
            <span *ngIf="currentStep <= index">{{ index + 1 }}</span>
          </div>
        </div>
        <div class="step-connector" *ngIf="index < totalSteps - 1"></div>
      </ng-container>
    </div>
  </div>
  
  <!-- Content area -->
  <div class="wizard-content">
    <ng-content></ng-content>
  </div>
  
  <!-- Navigation buttons -->
  <div class="wizard-footer" *ngIf="showNavigation">
    <button 
      mat-button 
      [disabled]="isFirstStep" 
      (click)="goToPreviousStep()"
      aria-label="Go to previous step">
      <mat-icon>arrow_back</mat-icon>
      Back
    </button>
    
    <div class="spacer"></div>
    
    <div class="step-counter" *ngIf="totalSteps > 0">
      Step {{ currentStep + 1 }} of {{ totalSteps }}
    </div>
    
    <button 
      mat-button 
      (click)="cancel()"
      aria-label="Cancel wizard">
      Cancel
    </button>
    
    <button 
      mat-raised-button 
      color="primary" 
      [disabled]="!canProceed"
      *ngIf="!isLastStep" 
      (click)="goToNextStep()"
      aria-label="Go to next step">
      Next
      <mat-icon>arrow_forward</mat-icon>
    </button>
    
    <button 
      mat-raised-button 
      color="primary" 
      [disabled]="!canProceed"
      *ngIf="isLastStep" 
      (click)="complete()"
      aria-label="Complete wizard">
      Complete
    </button>
  </div>
</div> 