:host {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background-color: var(--rjui-background-color, white);
  overflow: hidden;
}

.fs-wizard {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1050;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    
    rjui-wizard-container {
      width: 100%;
      max-width: 900px;
      height: 90vh;
      max-height: 800px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
      border-radius: 8px;
      overflow: hidden;
    }
  }
  
  &.mobile-view {
    padding: 0;
    
    rjui-wizard-container {
      width: 100%;
      height: 100%;
      max-width: none;
      max-height: none;
      border-radius: 0;
    }
  }
  
  &.tablet-view {
    rjui-wizard-container {
      width: 90%;
      height: 85vh;
    }
  }
}

.fs-wizard-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100vh;
  overflow: hidden;
  position: relative;
  
  &.mobile-view {
    .fs-wizard-progress {
      padding: 0.5rem;
      
      .step-indicators {
        .step-indicator {
          width: 2rem;
          height: 2rem;
          margin: 0 0.25rem;
          
          .step-number {
            font-size: 0.875rem;
          }
        }
        
        .step-connector {
          margin: 0 0.25rem;
        }
      }
    }
    
    .fs-wizard-content {
      padding: 1rem;
    }
    
    .fs-wizard-footer {
      padding: 0.75rem 1rem;
      
      button {
        .mat-icon {
          margin-right: 0;
        }
      }
    }
  }
  
  &.tablet-view {
    .fs-wizard-progress {
      padding: 0.75rem 1.5rem;
      
      .step-indicators {
        .step-indicator {
          .step-label {
            .step-subtitle {
              display: none;
            }
          }
        }
      }
    }
    
    .fs-wizard-content {
      padding: 1.25rem;
    }
  }
}

.fs-wizard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--rjui-border-color, #e0e0e0);
  background-color: var(--rjui-primary-color, #3f51b5);
  color: white;
  position: sticky;
  top: 0;
  z-index: 10;
  
  .fs-wizard-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 48px);
  }
  
  .close-button {
    color: white;
  }
}

.fs-wizard-progress {
  padding: 1rem 2rem;
  border-bottom: 1px solid var(--rjui-border-color, #e0e0e0);
  background-color: var(--rjui-surface-color, #f5f5f5);
  position: sticky;
  top: 0;
  z-index: 5;
  transition: all 0.3s ease;
  
  .step-indicators {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    
    .step-indicator {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease-in-out;
      
      &:hover {
        transform: scale(1.05);
      }
      
      &.active {
        .step-number {
          background-color: var(--rjui-primary-color, #3f51b5);
          color: white;
          transform: scale(1.1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .step-title {
          color: var(--rjui-primary-color, #3f51b5);
          font-weight: 500;
        }
      }
      
      &.completed {
        .step-number {
          background-color: var(--rjui-success-color, #4caf50);
          color: white;
        }
      }
      
      &.optional {
        .step-title::after {
          content: " (optional)";
          font-size: 0.75rem;
          font-style: italic;
          color: var(--rjui-hint-color, #9e9e9e);
        }
      }
      
      .step-number {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        background-color: var(--rjui-disabled-color, #bdbdbd);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        transition: all 0.3s ease;
      }
      
      .step-label {
        margin-left: 0.75rem;
        
        .step-title {
          font-weight: 400;
          transition: color 0.3s ease;
        }
        
        .step-subtitle {
          font-size: 0.75rem;
          color: var(--rjui-hint-color, #9e9e9e);
        }
      }
    }
    
    .step-connector {
      flex: 1;
      height: 2px;
      background-color: var(--rjui-border-color, #e0e0e0);
      margin: 0 0.5rem;
      position: relative;
      overflow: hidden;
      
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 0;
        background-color: var(--rjui-success-color, #4caf50);
        transition: width 0.3s ease;
      }
    }
  }
}

.fs-wizard-content {
  flex: 1;
  padding: 1.5rem 2rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  
  .step-header {
    margin-bottom: 1.5rem;
    
    .step-title {
      font-size: 1.25rem;
      font-weight: 500;
      margin: 0 0 0.5rem;
    }
    
    .step-subtitle {
      font-size: 0.875rem;
      color: var(--rjui-hint-color, #9e9e9e);
      margin: 0;
    }
  }
  
  .step-content {
    max-width: 1000px;
    margin: 0 auto;
    animation: fadeIn 0.3s ease-in-out;
  }
}

.fs-wizard-footer {
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  border-top: 1px solid var(--rjui-border-color, #e0e0e0);
  background-color: var(--rjui-surface-color, #f5f5f5);
  position: sticky;
  bottom: 0;
  z-index: 5;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  
  .spacer {
    flex: 1;
  }
  
  .step-counter {
    margin: 0 1rem;
    color: var(--rjui-secondary-text-color, #757575);
    font-size: 0.875rem;
    white-space: nowrap;
  }
  
  button {
    margin: 0 0.5rem;
    
    &:first-child {
      margin-left: 0;
    }
    
    &:last-child {
      margin-right: 0;
    }
    
    mat-icon {
      transition: transform 0.2s ease;
    }
    
    &.next-button:hover mat-icon {
      transform: translateX(3px);
    }
    
    &.back-button:hover mat-icon {
      transform: translateX(-3px);
    }
  }
}

// Mobile design improvements
@media (max-width: 767px) {
  .fs-wizard-header {
    padding: 0.75rem 1rem;
    
    .fs-wizard-title {
      font-size: 1.25rem;
    }
  }
  
  .fs-wizard-progress {
    padding: 0.5rem;
    
    .step-indicators {
      justify-content: center;
      
      .step-label {
        display: none;
      }
    }
  }
  
  .fs-wizard-content {
    padding: 1rem;
  }
  
  .fs-wizard-footer {
    padding: 0.75rem 1rem;
    flex-wrap: wrap;
    
    .back-button, .cancel-button {
      font-size: 0.875rem;
      min-width: auto;
      padding: 0 8px;
    }
    
    .next-button, .complete-button {
      flex-grow: 1;
    }
    
    button {
      margin: 0.25rem;
    }
  }
}

// Tablet design adjustments
@media (min-width: 768px) and (max-width: 1023px) {
  .fs-wizard-content {
    padding: 1.25rem;
  }
  
  .step-indicator .step-label .step-subtitle {
    display: none;
  }
}

// Animations
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

// Print styles - hide wizard for printing
@media print {
  :host {
    display: none !important;
  }
} 