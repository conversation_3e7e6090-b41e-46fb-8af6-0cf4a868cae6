<div class="fs-wizard" [class.fullscreen]="isFullscreen()" [class.mobile-view]="isMobileView()" [class.tablet-view]="isTabletView()">
  <rjui-wizard-container 
    [title]="title()" 
    [currentStep]="currentStepIndex()" 
    [totalSteps]="totalSteps()"
    (nextStep)="nextStep()" 
    (previousStep)="previousStep()" 
    (completeWizard)="completeWizard()" 
    (cancelWizard)="cancelWizard()"
    [canProceed]="canProceed()"
    [isFirstStep]="isFirstStep()"
    [isLastStep]="isLastStep()">
    
    <ng-container *ngFor="let step of steps(); let i = index; trackBy: trackByIndex">
      <rjui-wizard-step 
        *ngIf="i === currentStepIndex()"
        [title]="step.title || ''"
        [subtitle]="step.subtitle || ''"
        [icon]="step.icon || ''"
        [active]="i === currentStepIndex()">
        
        <!-- Dynamic content -->
        <ng-container *ngIf="step.content">
          <!-- String content -->
          <div *ngIf="typeof step.content === 'string'" [innerHTML]="step.content"></div>
          
          <!-- HTMLElement content - rendered via ViewContainerRef in component -->
          <div *ngIf="typeof step.content !== 'string'" #dynamicContent></div>
        </ng-container>
      </rjui-wizard-step>
    </ng-container>
    
  </rjui-wizard-container>
</div> 