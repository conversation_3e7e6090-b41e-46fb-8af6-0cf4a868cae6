<div class="wizard-step" [class.active]="active">
  <div class="wizard-step-header" *ngIf="title || hasSubtitle || hasIcon">
    <mat-icon *ngIf="hasIcon" class="step-icon">{{ icon }}</mat-icon>
    <div class="step-titles">
      <h3 class="step-title" *ngIf="title">{{ title }}</h3>
      <p class="step-subtitle" *ngIf="hasSubtitle">{{ subtitle }}</p>
    </div>
  </div>
  
  <div class="wizard-step-content">
    <ng-content></ng-content>
  </div>
</div> 