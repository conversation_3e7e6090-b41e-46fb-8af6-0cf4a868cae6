.wizard-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  
  .wizard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    background-color: #f5f5f5;
    
    .wizard-title {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
    }
  }
  
  .wizard-steps {
    padding: 16px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    
    .step-indicators {
      display: flex;
      align-items: center;
      
      .step-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: #e0e0e0;
        color: rgba(0, 0, 0, 0.7);
        font-weight: 500;
        position: relative;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        
        &.active {
          background-color: var(--primary-color, #1976d2);
          color: white;
        }
        
        &.completed {
          background-color: var(--success-color, #4caf50);
          color: white;
        }
        
        .step-number {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      
      .step-connector {
        flex: 1;
        height: 2px;
        background-color: #e0e0e0;
        margin: 0 8px;
      }
    }
  }
  
  .wizard-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }
  
  .wizard-footer {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.12);
    background-color: #f5f5f5;
    
    .spacer {
      flex: 1;
    }
    
    .step-counter {
      margin: 0 16px;
      font-size: 0.875rem;
      color: rgba(0, 0, 0, 0.6);
    }
    
    button {
      margin-left: 8px;
      
      &:first-child {
        margin-left: 0;
      }
    }
  }
} 