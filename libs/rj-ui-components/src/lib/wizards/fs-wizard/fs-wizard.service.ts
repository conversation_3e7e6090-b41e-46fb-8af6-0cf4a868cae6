import { ApplicationRef, createComponent, DestroyRef, EnvironmentInjector, inject, Injectable, Injector } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FsWizardComponent, FsWizardStep } from './fs-wizard.component';

export interface FsWizardConfig {
  title: string;
  steps: FsWizardStep[];
  onComplete?: (data: Record<string, unknown>) => void;
  onCancel?: () => void;
}

@Injectable({
  providedIn: 'root'
})
export class FsWizardService {
  private appRef = inject(ApplicationRef);
  private injector = inject(Injector);
  private environmentInjector = inject(EnvironmentInjector);
  private destroyRef = inject(DestroyRef);
  
  private wizardComponentRef: any = null;
  
  openWizard(config: FsWizardConfig): void {
    // Close any existing wizard first
    this.closeWizard();
    
    // Create the component
    const componentRef = createComponent(FsWizardComponent, {
      environmentInjector: this.environmentInjector,
      elementInjector: this.injector
    });
    
    // Set inputs
    componentRef.setInput('title', config.title);
    componentRef.setInput('steps', config.steps);
    
    // Handle events
    componentRef.instance.completed.subscribe((data: Record<string, unknown>) => {
      if (config.onComplete) {
        config.onComplete(data);
      }
      this.closeWizard();
    });
      
    componentRef.instance.cancelled.subscribe(() => {
      if (config.onCancel) {
        config.onCancel();
      }
      this.closeWizard();
    });
    
    // Attach to the DOM
    this.appRef.attachView(componentRef.hostView);
    
    // Get DOM element from component
    const domElement = ((componentRef.hostView as any).rootNodes[0] as HTMLElement);
    
    // Append to body
    document.body.appendChild(domElement);
    
    // Store reference
    this.wizardComponentRef = componentRef;
  }
  
  closeWizard(): void {
    if (this.wizardComponentRef) {
      // Detach the view
      this.appRef.detachView(this.wizardComponentRef.hostView);
      
      // Destroy the component
      this.wizardComponentRef.destroy();
      
      // Clear reference
      this.wizardComponentRef = null;
    }
  }
} 