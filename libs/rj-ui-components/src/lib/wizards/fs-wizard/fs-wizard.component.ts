import { DOCUMENT } from '@angular/common';
import { Component, computed, DestroyRef, effect, inject, input, model, OnDestroy, OnInit, output, signal, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { fromEvent, Subject } from 'rxjs';
import { debounceTime, filter } from 'rxjs/operators';
import { WizardContainerComponent } from './wizard-container.component';
import { WizardStepComponent } from './wizard-step.component';

export interface FsWizardStep {
  title: string;
  subtitle?: string;
  icon?: string;
  content: string | HTMLElement;
  data?: Record<string, unknown>;
  validationFn?: () => boolean;
  optional?: boolean;
}

// FsWizardConfig is defined in the service

@Component({
  selector: 'rjui-fs-wizard',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    WizardContainerComponent,
    WizardStepComponent
  ],
  templateUrl: './fs-wizard.component.html',
  styleUrls: ['./fs-wizard.component.scss']
})
export class FsWizardComponent implements OnInit, OnDestroy {
  private destroyRef = inject(DestroyRef);
  private document = inject(DOCUMENT);
  private originalBodyStyles = { overflow: '', position: '', width: '', top: '', paddingRight: '' };
  private resizeSubject = new Subject<void>();
  private scrollbarWidth = 0;
  
  // Inputs and Outputs using Angular 19 signals
  title = input<string>('');
  steps = input<FsWizardStep[]>([]);
  completed = output<Record<string, unknown>>();
  cancelled = output<void>();
  
  // Internal state signals
  currentStepIndex = signal(0);
  isFullscreen = signal(true);
  viewportSize = signal<{ width: number; height: number }>({ 
    width: window.innerWidth, 
    height: window.innerHeight 
  });
  isTransitioning = signal(false);
  
  // Computed state
  currentStep = computed(() => this.steps()[this.currentStepIndex()]);
  isFirstStep = computed(() => this.currentStepIndex() === 0);
  isLastStep = computed(() => this.currentStepIndex() === this.steps().length - 1);
  totalSteps = computed(() => this.steps().length);
  isMobileView = computed(() => this.viewportSize().width < 768);
  isTabletView = computed(() => this.viewportSize().width >= 768 && this.viewportSize().width < 1024);
  
  ngOnInit(): void {
    this.lockBodyScroll();
    this.setupKeyboardNavigation();
    this.setupResizeHandling();
    this.getScrollbarWidth();
  }
  
  ngOnDestroy(): void {
    this.unlockBodyScroll();
  }
  
  private getScrollbarWidth(): void {
    // Create a temporary div to measure scrollbar width
    const outer = this.document.createElement('div');
    outer.style.visibility = 'hidden';
    outer.style.overflow = 'scroll';
    this.document.body.appendChild(outer);
    
    const inner = this.document.createElement('div');
    outer.appendChild(inner);
    
    // Calculate the scrollbar width
    this.scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
    
    // Remove the divs
    outer.parentNode?.removeChild(outer);
  }
  
  private lockBodyScroll(): void {
    const body = this.document.body;
    
    // Store original styles
    this.originalBodyStyles = {
      overflow: body.style.overflow,
      position: body.style.position,
      width: body.style.width,
      top: body.style.top,
      paddingRight: body.style.paddingRight
    };
    
    // Check if body has a scrollbar
    const hasScrollbar = window.innerWidth > document.documentElement.clientWidth;
    
    // Lock the body scroll
    body.style.overflow = 'hidden';
    body.style.position = 'relative';
    
    // Compensate for scrollbar disappearance to prevent content shift
    if (hasScrollbar) {
      body.style.width = `calc(100% - ${this.scrollbarWidth}px)`;
      body.style.paddingRight = `${this.scrollbarWidth}px`;
    }
  }
  
  private unlockBodyScroll(): void {
    const body = this.document.body;
    
    // Restore original styles
    body.style.overflow = this.originalBodyStyles.overflow;
    body.style.position = this.originalBodyStyles.position;
    body.style.width = this.originalBodyStyles.width;
    body.style.top = this.originalBodyStyles.top;
    body.style.paddingRight = this.originalBodyStyles.paddingRight;
  }
  
  private setupKeyboardNavigation(): void {
    fromEvent<KeyboardEvent>(this.document, 'keydown')
      .pipe(
        filter((event: KeyboardEvent) => {
          // Only respond to keyboard events when the wizard is open
          return ['Escape', 'ArrowLeft', 'ArrowRight', 'Enter'].includes(event.key);
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((event: KeyboardEvent) => {
        switch (event.key) {
          case 'Escape':
            this.cancelWizard();
            break;
          case 'ArrowLeft':
            if (!this.isFirstStep() && !this.isTransitioning()) {
              this.previousStep();
            }
            break;
          case 'ArrowRight':
            if (!this.isLastStep() && this.canProceed() && !this.isTransitioning()) {
              this.nextStep();
            }
            break;
          case 'Enter':
            if (this.isLastStep() && this.canProceed() && !this.isTransitioning()) {
              this.completeWizard();
            } else if (!this.isLastStep() && this.canProceed() && !this.isTransitioning()) {
              this.nextStep();
            }
            break;
        }
      });
  }
  
  private setupResizeHandling(): void {
    // Handle responsive behavior using RxJS
    fromEvent(window, 'resize')
      .pipe(
        debounceTime(150),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        this.viewportSize.set({ 
          width: window.innerWidth, 
          height: window.innerHeight 
        });
      });
    
    // Orientation change handler for mobile devices
    fromEvent(window, 'orientationchange')
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        // Small delay to ensure dimensions are updated
        setTimeout(() => {
          this.viewportSize.set({ 
            width: window.innerWidth, 
            height: window.innerHeight 
          });
        }, 100);
      });
  }
  
  nextStep(): void {
    if (!this.isLastStep() && !this.isTransitioning()) {
      this.isTransitioning.set(true);
      this.currentStepIndex.update(index => index + 1);
      // Add a small delay to prevent rapid clicking
      setTimeout(() => this.isTransitioning.set(false), 300);
    }
  }
  
  previousStep(): void {
    if (!this.isFirstStep() && !this.isTransitioning()) {
      this.isTransitioning.set(true);
      this.currentStepIndex.update(index => index - 1);
      // Add a small delay to prevent rapid clicking
      setTimeout(() => this.isTransitioning.set(false), 300);
    }
  }
  
  goToStep(index: number): void {
    if (index >= 0 && index < this.totalSteps() && !this.isTransitioning()) {
      // Only allow navigation to steps that are either:
      // 1. Previous steps
      // 2. The next step if the current step is valid
      // 3. Optional steps
      const step = this.steps()[index];
      const currentIndex = this.currentStepIndex();
      
      if (index < currentIndex || 
          (index === currentIndex + 1 && this.canProceed()) || 
          (step.optional && index > currentIndex)) {
        this.isTransitioning.set(true);
        this.currentStepIndex.set(index);
        // Add a small delay to prevent rapid clicking
        setTimeout(() => this.isTransitioning.set(false), 300);
      }
    }
  }
  
  completeWizard(data: Record<string, unknown> = {}): void {
    if (!this.isTransitioning()) {
      // Collect data from all steps
      const collectedData = this.steps().reduce((result, step, index) => {
        if (step.data) {
          result[`step${index + 1}`] = step.data;
        }
        return result;
      }, {} as Record<string, unknown>);
      
      // Emit combined data
      this.completed.emit({ ...collectedData, ...data });
    }
  }
  
  cancelWizard(): void {
    if (!this.isTransitioning()) {
      this.cancelled.emit();
    }
  }
  
  isStepValid(index: number): boolean {
    const step = this.steps()[index];
    return step.optional || !step.validationFn || step.validationFn();
  }
  
  canProceed(): boolean {
    return this.isStepValid(this.currentStepIndex());
  }
  
  trackByIndex(index: number): number {
    return index;
  }
} 