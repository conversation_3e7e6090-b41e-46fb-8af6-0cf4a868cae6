import { Injectable, Injector, ApplicationRef, ComponentRef, createComponent } from '@angular/core';
import { FsWizardComponent } from './fs-wizard.component';
import { FsWizardStep } from './fs-wizard.component';

export interface WizardConfig {
  title: string;
  steps: FsWizardStep[];
  onComplete?: (data: Record<string, unknown>) => void;
  onCancel?: () => void;
}

@Injectable({
  providedIn: 'root'
})
export class WizardOverlayService {
  private appRef: ApplicationRef;
  private injector: Injector;
  private wizardComponentRef: ComponentRef<FsWizardComponent> | null = null;
  
  constructor(appRef: ApplicationRef, injector: Injector) {
    this.appRef = appRef;
    this.injector = injector;
  }
  
  openWizard(config: WizardConfig): void {
    // Close any existing wizard first
    this.closeWizard();
    
    // Create the component
    const componentRef = createComponent(FsWizardComponent, {
      environmentInjector: null as any,
      elementInjector: this.injector
    });
    
    // Set input properties through the component instance
    // With Angular 19 signal-based inputs, we need to use the property directly
    // The signals are exposed as properties on the component instance
    componentRef.setInput('title', config.title);
    componentRef.setInput('steps', config.steps);
    
    // Handle events
    componentRef.instance.completed.subscribe((data: Record<string, unknown>) => {
      if (config.onComplete) {
        config.onComplete(data);
      }
      this.closeWizard();
    });
      
    componentRef.instance.cancelled.subscribe(() => {
      if (config.onCancel) {
        config.onCancel();
      }
      this.closeWizard();
    });
    
    // Attach to the DOM
    this.appRef.attachView(componentRef.hostView);
    
    // Get DOM element from component
    const domElement = ((componentRef.hostView as any).rootNodes[0] as HTMLElement);
    
    // Append to body
    document.body.appendChild(domElement);
    
    // Store reference
    this.wizardComponentRef = componentRef;
  }
  
  closeWizard(): void {
    if (this.wizardComponentRef) {
      // Detach the view
      this.appRef.detachView(this.wizardComponentRef.hostView);
      
      // Destroy the component
      this.wizardComponentRef.destroy();
      
      // Clear reference
      this.wizardComponentRef = null;
    }
  }
} 