import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'rjui-wizard-step',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule
  ],
  templateUrl: './wizard-step.component.html',
  styleUrls: ['./wizard-step.component.scss']
})
export class WizardStepComponent {
  @Input() title = '';
  @Input() subtitle = '';
  @Input() icon = '';
  @Input() active = false;
  @Input() isValid = true;
  
  get hasIcon(): boolean {
    return !!this.icon && this.icon.length > 0;
  }
  
  get hasSubtitle(): boolean {
    return !!this.subtitle && this.subtitle.length > 0;
  }
} 