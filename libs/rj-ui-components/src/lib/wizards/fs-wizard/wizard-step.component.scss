.wizard-step {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 16px;
  
  &.active {
    display: flex;
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .wizard-step-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    
    .step-icon {
      margin-right: 16px;
      font-size: 24px;
      height: 24px;
      width: 24px;
    }
    
    .step-titles {
      .step-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 500;
      }
      
      .step-subtitle {
        margin: 4px 0 0;
        font-size: 0.875rem;
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }
  
  .wizard-step-content {
    flex: 1;
    overflow-y: auto;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 