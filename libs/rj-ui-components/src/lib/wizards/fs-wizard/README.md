# Fullscreen Wizard Component

A modern, responsive fullscreen wizard component designed for multi-step processes, built with Angular 19 signals and RxJS.

## Features

- **Fullscreen Interface**: Provides a distraction-free environment for complex multi-step processes
- **Responsive Design**: Automatically adapts to different screen sizes with mobile-specific layouts
- **Signal-Based Architecture**: Uses Angular 19's signals for reactive state management
- **Progress Tracking**: Visual indicators showing completed and current steps
- **Validation Support**: Each step can define custom validation functions
- **Keyboard Navigation**: Support for keyboard shortcuts for better accessibility

## Usage

The fullscreen wizard can be easily implemented using the `FsWizardService`:

```typescript
import { Component, inject } from '@angular/core';
import { FsWizardService, FsWizardConfig } from '@your-org/rj-ui-components';

@Component({
  selector: 'app-my-component',
  template: `
    <button (click)="openWizard()">Open Fullscreen Wizard</button>
  `
})
export class MyComponent {
  private wizardService = inject(FsWizardService);
  
  openWizard(): void {
    const config: FsWizardConfig = {
      title: 'My Fullscreen Wizard',
      steps: [
        {
          title: 'Step 1',
          subtitle: 'First step subtitle',
          icon: 'description',
          content: '<h3>Step 1 Content</h3><p>This is the content for step 1.</p>'
        },
        {
          title: 'Step 2',
          subtitle: 'Second step subtitle',
          icon: 'settings',
          content: '<h3>Step 2 Content</h3><p>This is the content for step 2.</p>',
          validationFn: () => true // Custom validation logic
        },
        {
          title: 'Final Step',
          subtitle: 'Review and submit',
          icon: 'check_circle',
          content: '<h3>Review</h3><p>Please review and submit.</p>'
        }
      ],
      onComplete: (data) => {
        console.log('Wizard completed with data:', data);
      },
      onCancel: () => {
        console.log('Wizard was cancelled');
      }
    };
    
    this.wizardService.openWizard(config);
  }
}
```

## API Reference

### FsWizardService

The service used to open and manage fullscreen wizards.

#### Methods

- `openWizard(config: FsWizardConfig): void` - Opens a fullscreen wizard with the provided configuration
- `closeWizard(): void` - Closes the currently open wizard

### FsWizardConfig

The configuration interface for the fullscreen wizard.

#### Properties

- `title: string` - The title displayed in the wizard header
- `steps: FsWizardStep[]` - Array of step configurations
- `onComplete?: (data: Record<string, unknown>) => void` - Callback when wizard is completed
- `onCancel?: () => void` - Callback when wizard is cancelled

### FsWizardStep

The configuration interface for individual wizard steps.

#### Properties

- `title: string` - The title of the step
- `subtitle?: string` - Optional subtitle for additional context
- `icon?: string` - Optional Material icon name
- `content: string | HTMLElement` - The content to display (HTML string or element)
- `data?: Record<string, unknown>` - Optional data associated with the step
- `validationFn?: () => boolean` - Optional function to validate the step
- `optional?: boolean` - Whether the step is optional 