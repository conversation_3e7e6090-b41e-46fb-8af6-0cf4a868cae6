import { Component, ContentChildren, EventEmitter, Input, Output, QueryList } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { WizardStepComponent } from './wizard-step.component';

@Component({
  selector: 'rjui-wizard-container',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule],
  templateUrl: './wizard-container.component.html',
  styleUrls: ['./wizard-container.component.scss']
})
export class WizardContainerComponent {
  @ContentChildren(WizardStepComponent) steps!: QueryList<WizardStepComponent>;
  
  @Input() title = 'Wizard';
  @Input() showNavigation = true;
  @Input() currentStep = 0;
  @Input() totalSteps = 0;
  @Input() canProceed = true;
  @Input() isFirstStep = true;
  @Input() isLastStep = false;
  
  @Output() completed = new EventEmitter<Record<string, unknown>>();
  @Output() cancelled = new EventEmitter<void>();
  @Output() stepChange = new EventEmitter<number>();
  @Output() nextStep = new EventEmitter<void>();
  @Output() previousStep = new EventEmitter<void>();
  @Output() completeWizard = new EventEmitter<void>();
  @Output() cancelWizard = new EventEmitter<void>();
  
  activeStepIndex = 0;
  
  goToNextStep(): void {
    if (this.hasNextStep && this.canProceed) {
      this.nextStep.emit();
      this.activeStepIndex++;
      this.stepChange.emit(this.activeStepIndex);
    }
  }
  
  goToPreviousStep(): void {
    if (this.hasPreviousStep) {
      this.previousStep.emit();
      this.activeStepIndex--;
      this.stepChange.emit(this.activeStepIndex);
    }
  }
  
  complete(): void {
    if (this.canProceed) {
      this.completeWizard.emit();
      this.completed.emit({});
    }
  }
  
  cancel(): void {
    this.cancelWizard.emit();
    this.cancelled.emit();
  }
  
  get hasNextStep(): boolean {
    return this.isLastStep ? false : true;
  }
  
  get hasPreviousStep(): boolean {
    return this.isFirstStep ? false : true;
  }
  
  get currentStepComponent(): WizardStepComponent | null {
    return this.steps ? this.steps.toArray()[this.activeStepIndex] : null;
  }
  
  get isCurrentStepValid(): boolean {
    return this.currentStepComponent ? this.currentStepComponent.isValid : true;
  }
} 