
.panel{
    position:relative;
    margin-bottom:15px;
    background-color:white;
        .panel-title{
            color:white;
            font-size:1.1em;
            font-weight:500;
            padding:.75em 1em;

            .fa-plus{
                text-align: center;
                float: right;
                display: inline-block;
                width: 1.75em;
                height: 1.75em;
                text-align: center;
                border: 1px solid #20936a;
                border-radius: 50%;
                color: #fff;
                background-color: #41b516;
                cursor: pointer;
                -webkit-transition: all 0.2s ease-in;
                transition: all 0.2s ease-in;
                padding: 5px;
            }
            .fa-times{
                text-align: center;
                float: right;
                display: inline-block;
                width: 1.75em;
                height: 1.75em;
                text-align: center;
                border: 1px solid darkred;
                border-radius: 50%;
                color: #fff;
                background-color: red;
                cursor: pointer;
                -webkit-transition: all 0.2s ease-in;
                transition: all 0.2s ease-in;
                padding: 5px;
            }
        }
    

    .panel-body{
        padding:15px;
    }
}
.panel.panel-empty {
	background: none;
	box-shadow: none;
	margin-bottom:13px;
	margin-top: 13px;

	> .panel-title {
		text-align: center;
		height: 0px;
		padding: 0;
		margin: 0;

		> a, > span {
			display: inline-block;
			padding: 15px 10px;
			position: relative;
			top: -13px;
		}

		> a {
			&:hover, &:focus, &:active {
				color: blueviolet !important;
			}
		}
	}
}