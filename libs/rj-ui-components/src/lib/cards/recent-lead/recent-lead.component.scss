/* You can add global styles to this file, and also import other style files */
$content-bg: #f7f7f7;
$navbar-bg: #0074c0;
$green:#28a745;
.info-item {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  font-size: .85em;
  margin: .5em 0;
  overflow: hidden;
  cursor: pointer;

  &.nowrap {
    height: 4em;
    white-space: nowrap;
  }

  &:hover, &.active {
    background-color: $content-bg;
  }

  .item-lob-icon, .item-check-icon, .item-input {
    flex: 1;
    text-align: center;
  }

  .item-lob-icon {
    min-width: 2em;
    max-width: 4em;
    padding-top: 1.1em;
    color: #eee;
    background-color: $navbar-bg;
  }

  .item-check-icon {
    position: relative;
    font-size: 1.25em;
    min-width: 3.25em;
    max-width: 3.25em;
    color: $green;
    border: 1px solid $green;

    i {
      display: block;
      position: absolute;
      top: 50%;
      left: 35%;
      transform: translateY(-50%);
    }
  }

  .item-input {
    position: relative;
    font-size: 1.25em;
    min-width: 3.25em;
    max-width: 3.25em;
    color: $green;
    background-color: #eee;
    border: 1px solid #ccc;

    input {
      display: block;
      position: absolute;
      top: 50%;
      left: 40%;
      transform: translateY(-50%);
    }
  }

  .item-body {
    flex: 5;
    border: 1px solid #ccc;
    border-left: none;
    padding: .5em .5em .5em .75em;

    &.flex {
      display: flex;
      flex-direction: row;

      .flex-size-1 {
        flex: 1;
      }
      .flex-size-2 {
        flex: 2;
      }
      .flex-size-3 {
        flex: 3;
      }
      .flex-size-4 {
        flex: 4;
      }
    }

    .item-name {
      font-weight: 500;
    }

    .item-source {
      font-style: italic;
    }

    .item-date, .itme-source {
      color: #666;
    }
  }
}
