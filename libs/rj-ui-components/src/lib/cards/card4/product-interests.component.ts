// import { Component, Input, OnInit } from '@angular/core';
// // import {Opportunity} from "../../models/opportunity";

// @Component({
//     selector: 'AA-product-interests',
//     templateUrl: "./product-interests.component.html",
//     styleUrls: ["./product-interests.component.scss"]
// })
// // export class ProductInterestComponent implements OnInit {
//     // @Input() contactId: guid;
//     // opportunities:  Opportunity[];

//     constructor() {}

//     ngOnInit() {
//         // this.opportunities = this.getMockOpportunities(this.contactId);
//     }

//     // getMockOpportunities(contactId: guid): Opportunity[] {
//     //     // const opportunities: Opportunity[] = [];

//     //     // const homeowners: Opportunity = new Opportunity();
//     //     // homeowners.lineOfBusinessName = 'Home';
//     //     // homeowners.productName = 'Homeowners';
//     //     // homeowners.stageName = 'New';
//     //     // homeowners.statusName = 'Quoted';
//     //     // homeowners.statusChangedDate = new Date('2018-02-14');
//     //     // homeowners.targetName = 'Jeff Reed';
//     //     // homeowners.eventDate = new Date('2018-04-12');
//     //     // opportunities.push(homeowners);

//     //     // const personalAuto: Opportunity = new Opportunity();
//     //     // personalAuto.lineOfBusinessName = 'Auto';
//     //     // personalAuto.productName = 'Personal Auto';
//     //     // personalAuto.stageName = 'Retain';
//     //     // personalAuto.statusName = 'Quoted';
//     //     // personalAuto.statusChangedDate = new Date('2018-02-25');
//     //     // personalAuto.targetName = 'Sarah Reed';
//     //     // personalAuto.eventDate = new Date('2018-07-12');
//     //     // opportunities.push(personalAuto);

//     //     return opportunities;
//     // }
// }