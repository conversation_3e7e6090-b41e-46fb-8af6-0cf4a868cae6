@import '../../themes/_basic-theme.scss';

$feature-name:ipz-card-3;

.#{$feature-name}{
    display:flex;
    flex: 1 1 100%;
    flex-direction:column;
    align-items:space-between;
    justify-content:center;
    background-color:white;
    color: black;
    border: theme(border);
    min-width:350px;
    
    .#{$feature-name}-header{
        display:flex;
        flex: 1 1 100%;
        flex-direction: row;
        align-items:stretch;
        justify-content:space-between;
        border-bottom: theme(border);


        .#{$feature-name}-title,.#{$feature-name}-date,.#{$feature-name}-time{
            display:flex;
            flex: 1 1 30%;
            font-size:theme(font-size);
            font-family:theme(font-family);
            margin:theme(margin, 0.5);
        }       
        .#{$feature-name}-icon-container{
            background-color:theme(primary-color);
            display:flex;
            align-items:center;
            justify-content:center;
            flex: 0 0 35px;

            .#{$feature-name}-icon{
            }
        }
        .#{$feature-name}-title{
            display:flex;
            flex: 1 30%;
            font-weight: theme(font-weight, 2);
            color:gray;
            text-overflow: ellipsis;
        }
        .#{$feature-name}-date{
            display:flex;
            flex: 1 30%;
            font-weight: theme(font-weight, 1.5);
            color:lightgrey;
        }
        .#{$feature-name}-time{
            display:flex;
            flex: 1 30%;
            font-weight: theme(font-weight, 1.5);
            color:lightgrey;
        }
    }
    .#{$feature-name}-body{
        display:flex;
        padding:theme(padding);
        text-overflow: ellipsis;
    }
}