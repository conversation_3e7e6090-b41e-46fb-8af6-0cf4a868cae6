<rjui-generic-page title="Buttons">

  <rjui-documentation id="doc1" title="Add-Plus">
    <ng-container example>
      <rjui-add-plus></rjui-add-plus>
    </ng-container>
    <ng-container api>
      None
    </ng-container>
    <ng-container usage>
      <pre>
          <code>
            &lt;add-plus&gt;&lt;/add-plus&gt;
          </code>
        </pre>
    </ng-container>
    <ng-container dependencies>
      <ul>
        <li> Bootstrap class names </li>
        <li> Font-awesome </li>
      </ul>
    </ng-container>
  </rjui-documentation>

  <rjui-documentation id="doc2" title="Delete-X">
    <ng-container example>
      <rjui-delete-x></rjui-delete-x>
    </ng-container>
    <ng-container api>
      None
    </ng-container>
    <ng-container usage>
      <pre>
          <code>
            &lt;delete-x&gt;&lt;delete-x&gt;
          </code>
        </pre>
    </ng-container>
    <ng-container dependencies>
      <ul>
        <li> Font awesome </li>
      </ul>
    </ng-container>
  </rjui-documentation>

  <rjui-documentation id="doc3" title="Link Block">
    <ng-container example>
      <rjui-link-block [text]="'link block'" [icon]="'fa fa-external-link'"></rjui-link-block>
    </ng-container>
    <ng-container api>
      Inputs
      <ul>
        <li>text</li>
        <li>icon</li>
        <li>highlight?</li>
        <li>class</li>
        <li>routerLink</li>
      </ul>
    </ng-container>
    <ng-container usage>
      <pre>
          <code>
            &lt;link-block&gt;&lt;link-block&gt;
          </code>
        </pre>
    </ng-container>
    <ng-container dependencies>
      <ul>
        <li> highlight pipe </li>
      </ul>
    </ng-container>
  </rjui-documentation>

  <rjui-documentation id="doc4" title="Icon Chip" [labels]="['AA','WIP']">
    <ng-container example>
      <rjui-icon-chip-button [name]="'call'" [icon]="'phone'">
      </rjui-icon-chip-button>
    </ng-container>
    <ng-container api>
      Material icons
    </ng-container>
    <ng-container usage>

    </ng-container>
    <ng-container dependencies>

    </ng-container>
  </rjui-documentation>

  <rjui-documentation id="doc5" title="Round Add Button" [labels]="['AA','WIP']">
    <ng-container example>
      <rjui-round-add-button>
      </rjui-round-add-button>
    </ng-container>
    <ng-container api>
      Material icons
    </ng-container>
    <ng-container usage>

    </ng-container>
    <ng-container dependencies>

    </ng-container>
  </rjui-documentation>

</rjui-generic-page>
