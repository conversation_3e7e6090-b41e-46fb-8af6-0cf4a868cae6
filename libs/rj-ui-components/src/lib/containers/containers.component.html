<rjui-generic-page title="Containers">
  <rjui-documentation title="Documentation Container" id="doc1">
    <ng-container example>
      <rjui-documentation [title]="'Example'">
        <ng-container example>
          Place an example of the component/directive here.
        </ng-container>
        <ng-container api> Give a description of the API here. </ng-container>
        <ng-container usage> Show usage code here. </ng-container>
        <ng-container dependencies> Show dependencies here. </ng-container>
      </rjui-documentation>
    </ng-container>
    <ng-container api> </ng-container>
    <ng-container usage>
      <pre>
        <!-- <code [innerHTML]="doc1"></code> -->
      </pre>
    </ng-container>
  </rjui-documentation>

  <rjui-documentation title="Horizontal Scroller" id="doc2">
    <ng-container example>
      <div>
        <rjui-horizontal-card-scroller [centered]="true">
          <div #cardListItem id="1" class="org-tile">
            <i class="fa fa-building"></i> tile
          </div>
          <!-- Repeat for other tiles -->
        </rjui-horizontal-card-scroller>
      </div>
      <div style="margin-top: 30px">
        <button class="btn btn-default" (click)="removeCard()">remove card</button>
        <button class="btn btn-default" (click)="addCard()">add card</button>
        <button class="btn btn-default" (click)="addCard(2)">add card at 2</button>
        <button class="btn btn-default" (click)="removeCard(2)">remove card at 2</button>
        <button class="btn btn-default" (click)="removeCard(2, 2)">remove card at 2,3</button>
        <button class="btn btn-default" (click)="changeCardSet(1)">card set 1</button>
        <button class="btn btn-default" (click)="changeCardSet(2)">card set 2</button>
        <button class="btn btn-default" (click)="changeCardSet(3)">card set 3</button>
      </div>
    </ng-container>
    <ng-container api>
      <pre>
        <code>
          ContentChildren(Card1Component) cards: QueryList&lt;Card1Component&gt;;
          Input() maxSelectable:number = 0;
          Input() fixedWidth:number = 0;
          Input() maxCards:number = 0;
          Output() cardSelected:EventEmitter&lt;any&gt; = new EventEmitter();
          Output() cardDeselected:EventEmitter&lt;any&gt; = new EventEmitter();
        </code>
      </pre>
    </ng-container>
    <ng-container usage>
      <pre>
        <code>
          &lt;rjui-horizontal-card-scroller&gt;
          &lt;rjui-card-1&gt;&lt;/rjui-card-1&gt;
          &lt;/rjui-horizontal-card-scroller&gt;
        </code>
      </pre>
    </ng-container>
  </rjui-documentation>

  <rjui-documentation title="Filter Box" id="doc3">
    <ng-container example>
      <rjui-filter-box [title]="'TIMELINE'" (addNewCard)="addTimelineCard($event)">
        <rjui-card-3 *ngFor="let card of filterCards" [icon]="card?.icon" [title]="card?.title" [date]="card?.date" [time]="card?.time" [bodyText]="card?.bodyText"></rjui-card-3>
      </rjui-filter-box>
    </ng-container>
    <ng-container api>
      <pre>
        <code>
          ContentChildren(Card3Component, {{'{'}} read: ElementRef {{'}'}})
          Input() title:string = "";
          Output() addNewCard:EventEmitter&lt;string&gt; = new EventEmitter();
        </code>
      </pre>
    </ng-container>
    <ng-container usage>
      <pre>
        <code>
          &lt;rjui-filter-box [title]="'TIMELINE'" (addNewCard)="addTimelineCard($event)"&gt;
          &lt;rjui-card-3&gt;&lt;/rjui-card-3&gt;
          &lt;/rjui-filter-box&gt;
        </code>
      </pre>
    </ng-container>
  </rjui-documentation>

  <rjui-documentation title="Generic Panel" id="doc4">
    <ng-container example>
      <rjui-contact-detail-panel #contactDetailsPanel [title]="'Active Marketing Programs'" [loading]="true" [noResults]="false" [noResultsText]="'no marketing plans were found'">
        <rjui-contact-detail-panel-flyout [title]="'Add New Marketing Plan'"></rjui-contact-detail-panel-flyout>
      </rjui-contact-detail-panel>
    </ng-container>
    <ng-container api>
      <pre>
        <code>
          <!-- API details here -->
        </code>
      </pre>
    </ng-container>
    <ng-container usage>
      <pre>
        <code>
          <!-- Usage details here -->
        </code>
      </pre>
    </ng-container>
  </rjui-documentation>
</rjui-generic-page>
