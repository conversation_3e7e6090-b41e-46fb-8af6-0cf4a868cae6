@use 'sass:color';
@import '../../themes/_basic-theme.scss';

.container{
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: stretch;
    align-items:stretch;
    background-color:white;
    margin: 16px;
    color: #333;
    @include z-index(2, $shadow:true);
    flex: 1 1 auto;
    font-family: 'Roboto', sans-serif;

    mat-tab-group{
        display:flex;
        flex: 1 1 100%;
        font-family: 'Roboto', sans-serif;
        mat-tab{
            font-family: 'Roboto', sans-serif;
        }
    }


    .tab-body{
        height: 400px;
        min-height:10rem;
        padding: 16px;
        display:flex;
        flex-wrap:wrap;
        flex-direction:column;
        align-items: stretch;
        justify-content: stretch;
        flex: 1 1 100%;
        overflow-y: auto;
    }

    mat-chip-list{
        margin:5px;
    }
}