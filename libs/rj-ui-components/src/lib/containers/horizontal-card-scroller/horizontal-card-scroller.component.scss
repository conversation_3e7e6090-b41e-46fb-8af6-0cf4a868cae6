


$height:100px;
$scroll-button-width:25px;

.horizontal-card-scroller{
    box-sizing: border-box;
    position: relative;
    width:100%;
    background-color: transparent;



    .scroll-left, .scroll-right{
        box-sizing: border-box;
        position:absolute;
        height:100%;
        width:$scroll-button-width;
        display:inline-block;
        z-index: 6;
        opacity: 0.3;
        background-color: transparent;
        color:black;
        cursor:pointer; 

        &:hover{
            transition: ease 0.5s;
            opacity: 1;
        }

        &.no-scroll {
            display:none;
        }
        .fa {
            font-size:24px;
            position: relative;
            top: 50%;
            width:$scroll-button-width;
            text-align:center;
            transform: translateY(-50%); 
        }

    }
    .scroll-left{
        left: 0px;
    }

    .scroll-right{
        right: 0px;
    }
    .scroll-content{
        display:block;
        height:100%;
        overflow-x: hidden;
        overflow-y: hidden;
        white-space: nowrap;
        transition: all 1s;
        &.centered{
            text-align:center;
            margin-left:$scroll-button-width;
            margin-right:$scroll-button-width;
        }

        .item {
            left:0;
            display:inline-block;
            height: 100%;
            background-color: transparent;
            margin-right: 10px;
            vertical-align: center;
            z-index: 5;
            -webkit-transition: 0.3s;
            /* Safari */
            transition: ease 0.3s;
            -webkit-transition: 0.3s; /* Safari */
            transition: ease 0.3s;
            &.selected{
                border: 2px solid black;
                background-color: green;
                -webkit-transition: 0.3s;
                /* Safari */
                transition: ease 0.3s;
            }

            &:hover{               
            }

            &:focus{
            }
            

        }
    }

}

.warning-text{
    color:yellow;
}
.error-text{
    color:red;
}

 