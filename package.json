{"name": "@angular-monorepo/source", "version": "0.0.0", "license": "MIT", "scripts": {"start": "concurrently \"npx nx run rj-ui-components:build --watch=true\" \"npx nx serve angular-monorepo\""}, "private": true, "dependencies": {"@angular/animations": "19.0.7", "@angular/cdk": "~19.1.0", "@angular/common": "19.0.7", "@angular/compiler": "19.0.7", "@angular/core": "19.0.7", "@angular/fire": "^19.0.0", "@angular/forms": "19.0.7", "@angular/material": "~19.1.0", "@angular/platform-browser": "19.0.7", "@angular/platform-browser-dynamic": "19.0.7", "@angular/router": "19.0.7", "@fortawesome/fontawesome-free": "^6.7.2", "@nx/angular": "20.3.2", "angular-gridster2": "^19.0.0", "chart.js": "^4.4.8", "firebase": "^11.4.0", "hammerjs": "^2.0.8", "home-assistant-js-websocket": "^9.4.0", "material-design-icons": "^3.0.1", "material-icons": "^1.13.12", "ng2-charts": "^8.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rxjs": "~7.8.0", "storybook": "^8.2.8", "tslib": "^2.3.0", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "19.0.7", "@angular-devkit/core": "19.0.7", "@angular-devkit/schematics": "19.0.7", "@angular-eslint/eslint-plugin": "19.0.2", "@angular-eslint/eslint-plugin-template": "19.0.2", "@angular-eslint/template-parser": "19.0.2", "@angular/cli": "~18.0.0", "@angular/compiler-cli": "19.0.7", "@angular/language-service": "19.0.7", "@frxjs/ngx-timeline": "^19.0.2", "@nx/devkit": "20.3.2", "@nx/eslint": "20.3.2", "@nx/eslint-plugin": "20.3.2", "@nx/jest": "20.3.2", "@nx/js": "20.3.2", "@nx/playwright": "20.3.2", "@nx/storybook": "20.3.2", "@nx/web": "20.3.2", "@nx/workspace": "20.3.2", "@playwright/test": "^1.36.0", "@schematics/angular": "19.0.7", "@storybook/addon-essentials": "8.5.0", "@storybook/addon-interactions": "8.5.0", "@storybook/angular": "8.5.0", "@storybook/core-server": "8.5.0", "@storybook/jest": "^0.2.3", "@storybook/test-runner": "0.19.1", "@storybook/testing-library": "^0.2.2", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/hammerjs": "^2.0.45", "@types/jest": "29.5.14", "@types/node": "18.16.9", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "@typescript-eslint/utils": "7.18.0", "angular": "^1.7.2", "angular-animate": "^1.7.2", "angular-aria": "^1.7.2", "angular-material": "^1.2.5", "angular-messages": "^1.7.2", "autoprefixer": "^10.4.19", "css-loader": "^7.1.2", "dotenv": "^16.4.7", "eslint": "~8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-playwright": "^1.6.2", "firebase-admin": "^13.2.0", "firebase-functions": "^6.3.2", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-preset-angular": "14.4.2", "jsonc-eslint-parser": "^2.1.0", "ng-packagr": "19.0.1", "nx": "20.3.2", "postcss": "^8.4.5", "postcss-loader": "^8.1.1", "postcss-url": "~10.1.3", "prettier": "^2.6.2", "sass-loader": "^14.2.1", "style-loader": "^4.0.0", "tailwindcss": "^3.0.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "5.6.3", "vitest": "^3.0.8"}, "nx": {"targets": {}, "plugins": ["@nx/angular"]}, "packageManager": "yarn@4.7.0+sha512.5a0afa1d4c1d844b3447ee3319633797bcd6385d9a44be07993ae52ff4facabccafb4af5dcd1c2f9a94ac113e5e9ff56f6130431905884414229e284e37bb7c9"}